<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Desktop Investment Integration Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { margin: 5px; padding: 8px 16px; cursor: pointer; }
        .result-item { margin: 5px 0; padding: 8px; background: #f0f0f0; border-radius: 3px; }
        .storage-path { font-family: monospace; background: #e9ecef; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Desktop Investment Integration Test</h1>
    <p>This test verifies the integration between the React frontend and Python storage backend for investment portfolios.</p>
    <p><strong>Expected Storage Path:</strong> <span class="storage-path">C:\Users\<USER>\.financial_advisor</span></p>

    <div id="test-1" class="test-section info">
        <h3>Test 1: Environment Detection</h3>
        <button onclick="testEnvironment()">Check Environment</button>
        <div id="env-result"></div>
    </div>

    <div id="test-2" class="test-section info">
        <h3>Test 2: PyWebView API Availability</h3>
        <button onclick="testPyWebViewAPI()">Test PyWebView API</button>
        <div id="api-result"></div>
    </div>

    <div id="test-3" class="test-section info">
        <h3>Test 3: Basic Storage Functions</h3>
        <button onclick="testBasicStorage()">Test Basic Storage</button>
        <div id="storage-result"></div>
    </div>

    <div id="test-4" class="test-section info">
        <h3>Test 4: Investment Portfolio Creation</h3>
        <button onclick="testPortfolioCreation()">Test Portfolio Creation</button>
        <div id="portfolio-result"></div>
    </div>

    <div id="test-5" class="test-section info">
        <h3>Test 5: Stock Holdings Management</h3>
        <button onclick="testStockHoldings()">Test Stock Holdings</button>
        <div id="holdings-result"></div>
    </div>

    <div id="test-6" class="test-section info">
        <h3>Test 6: Data Persistence Verification</h3>
        <button onclick="testDataPersistence()">Test Data Persistence</button>
        <div id="persistence-result"></div>
    </div>

    <script>
        let isDesktopApp = false;
        let apiAvailable = false;

        function testEnvironment() {
            const resultDiv = document.getElementById('env-result');
            
            // Check if we're in desktop app
            isDesktopApp = !!window.pywebview;
            const userAgent = navigator.userAgent;
            const isInWebView = userAgent.includes('pywebview') || userAgent.includes('webview');
            
            const html = `
                <div class="${isDesktopApp ? 'success' : 'warning'}">
                    <h4>${isDesktopApp ? '✅' : '⚠️'} Environment Detection</h4>
                    <p><strong>Desktop App:</strong> ${isDesktopApp ? 'YES' : 'NO'}</p>
                    <p><strong>PyWebView Object:</strong> ${!!window.pywebview}</p>
                    <p><strong>User Agent WebView:</strong> ${isInWebView}</p>
                    <p><strong>User Agent:</strong> ${userAgent}</p>
                    ${!isDesktopApp ? '<p class="warning">⚠️ Running in browser mode - will use localStorage fallback</p>' : ''}
                </div>
            `;
            resultDiv.innerHTML = html;
            document.getElementById('test-1').className = `test-section ${isDesktopApp ? 'success' : 'warning'}`;
        }

        async function testPyWebViewAPI() {
            const resultDiv = document.getElementById('api-result');
            
            try {
                if (!isDesktopApp) {
                    resultDiv.innerHTML = `
                        <div class="warning">
                            <h4>⚠️ PyWebView API Test Skipped</h4>
                            <p>Not running in desktop app mode</p>
                        </div>
                    `;
                    document.getElementById('test-2').className = 'test-section warning';
                    return;
                }

                resultDiv.innerHTML = '<p>Testing PyWebView API availability...</p>';
                
                // Check if API methods are available
                const api = window.pywebview.api;
                const methods = [
                    'get_item', 'set_item', 'remove_item',
                    'save_investment_portfolio', 'get_investment_portfolios',
                    'save_stock_holding', 'get_portfolio_holdings'
                ];
                
                const results = [];
                for (const method of methods) {
                    const available = typeof api[method] === 'function';
                    results.push({
                        method,
                        available
                    });
                }

                const availableCount = results.filter(r => r.available).length;
                apiAvailable = availableCount === methods.length;

                const html = `
                    <div class="${apiAvailable ? 'success' : 'error'}">
                        <h4>${apiAvailable ? '✅' : '❌'} PyWebView API Test (${availableCount}/${methods.length})</h4>
                        ${results.map(r => `
                            <div class="result-item">
                                <strong>${r.method}:</strong> ${r.available ? '✅ Available' : '❌ Missing'}
                            </div>
                        `).join('')}
                    </div>
                `;
                resultDiv.innerHTML = html;
                document.getElementById('test-2').className = `test-section ${apiAvailable ? 'success' : 'error'}`;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ PyWebView API Test Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                document.getElementById('test-2').className = 'test-section error';
            }
        }

        async function testBasicStorage() {
            const resultDiv = document.getElementById('storage-result');
            
            try {
                resultDiv.innerHTML = '<p>Testing basic storage operations...</p>';
                
                const testKey = 'test_investment_' + Date.now();
                const testValue = JSON.stringify({ test: 'data', timestamp: Date.now() });
                
                let setResult, getResult, removeResult;
                
                if (isDesktopApp && window.pywebview.api) {
                    // Test with Python storage
                    setResult = await window.pywebview.api.set_item(testKey, testValue);
                    const retrieved = await window.pywebview.api.get_item(testKey);
                    getResult = retrieved === testValue;
                    removeResult = await window.pywebview.api.remove_item(testKey);
                } else {
                    // Test with localStorage fallback
                    localStorage.setItem(testKey, testValue);
                    setResult = true;
                    getResult = localStorage.getItem(testKey) === testValue;
                    localStorage.removeItem(testKey);
                    removeResult = true;
                }
                
                const allPassed = setResult && getResult && removeResult;
                
                const html = `
                    <div class="${allPassed ? 'success' : 'error'}">
                        <h4>${allPassed ? '✅' : '❌'} Basic Storage Test</h4>
                        <p><strong>Storage Type:</strong> ${isDesktopApp ? 'Python Backend' : 'localStorage Fallback'}</p>
                        <p><strong>Set Item:</strong> ${setResult ? '✅' : '❌'}</p>
                        <p><strong>Get Item:</strong> ${getResult ? '✅' : '❌'}</p>
                        <p><strong>Remove Item:</strong> ${removeResult ? '✅' : '❌'}</p>
                        <p><strong>Test Key:</strong> <code>${testKey}</code></p>
                    </div>
                `;
                resultDiv.innerHTML = html;
                document.getElementById('test-3').className = `test-section ${allPassed ? 'success' : 'error'}`;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Basic Storage Test Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                document.getElementById('test-3').className = 'test-section error';
            }
        }

        async function testPortfolioCreation() {
            const resultDiv = document.getElementById('portfolio-result');
            
            try {
                resultDiv.innerHTML = '<p>Testing portfolio creation...</p>';
                
                const testPortfolio = {
                    name: 'Test Desktop Portfolio',
                    description: 'Test portfolio for desktop integration',
                    type: 'stocks',
                    current_balance: 10000,
                    target_return: 10
                };
                
                let result;
                
                if (isDesktopApp && window.pywebview.api) {
                    // Test with Python storage
                    console.log('Creating portfolio with Python API:', testPortfolio);
                    const response = await window.pywebview.api.save_investment_portfolio(testPortfolio);
                    console.log('Python API response:', response);
                    
                    if (typeof response === 'string') {
                        result = JSON.parse(response);
                    } else {
                        result = response;
                    }
                } else {
                    // Test with localStorage fallback
                    const portfolios = JSON.parse(localStorage.getItem('investment_portfolios') || '[]');
                    const newPortfolio = { ...testPortfolio, id: Date.now().toString(), created_at: new Date().toISOString() };
                    portfolios.push(newPortfolio);
                    localStorage.setItem('investment_portfolios', JSON.stringify(portfolios));
                    result = { success: true, id: newPortfolio.id };
                }
                
                const success = result && result.success;
                
                const html = `
                    <div class="${success ? 'success' : 'error'}">
                        <h4>${success ? '✅' : '❌'} Portfolio Creation Test</h4>
                        <p><strong>Portfolio Name:</strong> ${testPortfolio.name}</p>
                        <p><strong>Storage Type:</strong> ${isDesktopApp ? 'Python Backend' : 'localStorage Fallback'}</p>
                        <p><strong>Success:</strong> ${success ? 'YES' : 'NO'}</p>
                        ${result.id ? `<p><strong>Portfolio ID:</strong> ${result.id}</p>` : ''}
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
                resultDiv.innerHTML = html;
                document.getElementById('test-4').className = `test-section ${success ? 'success' : 'error'}`;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Portfolio Creation Test Failed</h4>
                        <p>Error: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
                document.getElementById('test-4').className = 'test-section error';
            }
        }

        async function testStockHoldings() {
            const resultDiv = document.getElementById('holdings-result');
            
            try {
                resultDiv.innerHTML = '<p>Testing stock holdings management...</p>';
                
                const testHolding = {
                    portfolio_id: 'test_portfolio_' + Date.now(),
                    symbol: 'COMI',
                    company_name: 'Commercial International Bank',
                    shares: 100,
                    purchase_price: 80,
                    purchase_date: new Date().toISOString(),
                    average_cost: 80,
                    total_cost: 8000,
                    fees: 0
                };
                
                let result;
                
                if (isDesktopApp && window.pywebview.api) {
                    // Test with Python storage
                    console.log('Creating stock holding with Python API:', testHolding);
                    const response = await window.pywebview.api.save_stock_holding(testHolding);
                    console.log('Python API response:', response);
                    
                    if (typeof response === 'string') {
                        result = JSON.parse(response);
                    } else {
                        result = response;
                    }
                } else {
                    // Test with localStorage fallback
                    const holdings = JSON.parse(localStorage.getItem('investment_stock_holdings') || '[]');
                    const newHolding = { ...testHolding, id: Date.now().toString(), created_at: new Date().toISOString() };
                    holdings.push(newHolding);
                    localStorage.setItem('investment_stock_holdings', JSON.stringify(holdings));
                    result = { success: true, id: newHolding.id };
                }
                
                const success = result && result.success;
                
                const html = `
                    <div class="${success ? 'success' : 'error'}">
                        <h4>${success ? '✅' : '❌'} Stock Holdings Test</h4>
                        <p><strong>Stock Symbol:</strong> ${testHolding.symbol}</p>
                        <p><strong>Shares:</strong> ${testHolding.shares}</p>
                        <p><strong>Price:</strong> ${testHolding.purchase_price} EGP</p>
                        <p><strong>Storage Type:</strong> ${isDesktopApp ? 'Python Backend' : 'localStorage Fallback'}</p>
                        <p><strong>Success:</strong> ${success ? 'YES' : 'NO'}</p>
                        ${result.id ? `<p><strong>Holding ID:</strong> ${result.id}</p>` : ''}
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>
                `;
                resultDiv.innerHTML = html;
                document.getElementById('test-5').className = `test-section ${success ? 'success' : 'error'}`;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Stock Holdings Test Failed</h4>
                        <p>Error: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
                document.getElementById('test-5').className = 'test-section error';
            }
        }

        async function testDataPersistence() {
            const resultDiv = document.getElementById('persistence-result');
            
            try {
                resultDiv.innerHTML = '<p>Testing data persistence...</p>';
                
                let portfolioCount = 0;
                let holdingCount = 0;
                let storageLocation = '';
                
                if (isDesktopApp && window.pywebview.api) {
                    // Check Python storage
                    try {
                        const portfoliosResponse = await window.pywebview.api.get_investment_portfolios();
                        const portfoliosData = typeof portfoliosResponse === 'string' ? JSON.parse(portfoliosResponse) : portfoliosResponse;
                        portfolioCount = Array.isArray(portfoliosData) ? portfoliosData.length : 0;
                        storageLocation = 'C:\\Users\\<USER>\\.financial_advisor (Python Backend)';
                    } catch (e) {
                        console.warn('Error getting portfolios:', e);
                    }
                } else {
                    // Check localStorage
                    const portfolios = JSON.parse(localStorage.getItem('investment_portfolios') || '[]');
                    const holdings = JSON.parse(localStorage.getItem('investment_stock_holdings') || '[]');
                    portfolioCount = portfolios.length;
                    holdingCount = holdings.length;
                    storageLocation = 'Browser localStorage (Fallback)';
                }
                
                const html = `
                    <div class="info">
                        <h4>📊 Data Persistence Status</h4>
                        <p><strong>Storage Location:</strong> ${storageLocation}</p>
                        <p><strong>Portfolios Count:</strong> ${portfolioCount}</p>
                        <p><strong>Holdings Count:</strong> ${holdingCount}</p>
                        <p><strong>Storage Type:</strong> ${isDesktopApp ? 'Persistent (Python)' : 'Temporary (Browser)'}</p>
                        ${isDesktopApp ? 
                            '<p class="success">✅ Data will persist between app restarts</p>' : 
                            '<p class="warning">⚠️ Data will be lost when browser is closed</p>'
                        }
                    </div>
                `;
                resultDiv.innerHTML = html;
                document.getElementById('test-6').className = 'test-section info';
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Data Persistence Test Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                document.getElementById('test-6').className = 'test-section error';
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('Running automated desktop integration tests...');
                testEnvironment();
                setTimeout(() => testPyWebViewAPI(), 500);
                setTimeout(() => testBasicStorage(), 1000);
            }, 500);
        });
    </script>
</body>
</html>
