import json
import os
import datetime
import random

# Path to the financial advisor data directory
DATA_DIR = os.path.expanduser('~/.financial_advisor')

# Create the directory if it doesn't exist
os.makedirs(DATA_DIR, exist_ok=True)

# Generate a unique ID
def generate_id():
    return f"{int(datetime.datetime.now().timestamp())}-{random.randint(1000, 9999)}"

# Current date
today = datetime.datetime.now()

# Create test loan data
loans = []
for i in range(3):
    loan_id = generate_id()
    next_payment_date = (today + datetime.timedelta(days=random.randint(1, 30))).strftime('%Y-%m-%dT00:00:00.000Z')
    loan = {
        "id": loan_id,
        "user_id": "default-user",
        "name": f"Test Loan {i+1}",
        "principal": random.randint(10000, 100000),
        "interest_rate": round(random.uniform(3.0, 7.0), 2),
        "term_months": random.choice([12, 24, 36, 60, 120, 180, 240, 360]),
        "remaining_balance": random.randint(5000, 90000),
        "monthly_payment": random.randint(500, 2000),
        "next_payment_date": next_payment_date,
        "created_at": today.strftime('%Y-%m-%dT00:00:00.000Z')
    }
    loans.append(loan)
    
    # Save individual loan
    with open(os.path.join(DATA_DIR, f"loans/{loan_id}"), 'w') as f:
        json.dump(loan, f)

# Create loan index
with open(os.path.join(DATA_DIR, "loans_index"), 'w') as f:
    json.dump([loan["id"] for loan in loans], f)

# Create test expense data
expenses = []
for i in range(5):
    expense_id = generate_id()
    expense_date = (today + datetime.timedelta(days=random.randint(1, 45))).strftime('%Y-%m-%dT00:00:00.000Z')
    expense = {
        "id": expense_id,
        "user_id": "default-user",
        "amount": random.randint(50, 500),
        "category": random.choice(["Groceries", "Utilities", "Entertainment", "Transportation", "Healthcare"]),
        "description": f"Test Expense {i+1}",
        "date": expense_date,
        "created_at": today.strftime('%Y-%m-%dT00:00:00.000Z')
    }
    expenses.append(expense)
    
    # Save individual expense
    with open(os.path.join(DATA_DIR, f"expenses/{expense_id}"), 'w') as f:
        json.dump(expense, f)

# Create expense index
with open(os.path.join(DATA_DIR, "expenses_index"), 'w') as f:
    json.dump([expense["id"] for expense in expenses], f)

# Create test goals
goals = []
for i in range(2):
    goal_id = generate_id()
    target_date = (today + datetime.timedelta(days=random.randint(60, 365))).strftime('%Y-%m-%dT00:00:00.000Z')
    goal = {
        "id": goal_id,
        "user_id": "default-user",
        "name": f"Financial Goal {i+1}",
        "target_amount": random.randint(5000, 50000),
        "target_date": target_date,
        "notes": f"Test goal {i+1} description",
        "created_at": today.strftime('%Y-%m-%dT00:00:00.000Z')
    }
    goals.append(goal)
    
    # Save individual goal
    with open(os.path.join(DATA_DIR, f"financial_goals/{goal_id}"), 'w') as f:
        json.dump(goal, f)

# Create goals index
with open(os.path.join(DATA_DIR, "financial_goals_index"), 'w') as f:
    json.dump([goal["id"] for goal in goals], f)

# Create test CDs
cds = []
for i in range(2):
    cd_id = generate_id()
    maturity_date = (today + datetime.timedelta(days=random.randint(90, 730))).strftime('%Y-%m-%dT00:00:00.000Z')
    cd = {
        "id": cd_id,
        "user_id": "default-user",
        "institution": random.choice(["Bank of America", "Chase", "Wells Fargo", "Citi"]),
        "principal": random.randint(1000, 20000),
        "interest_rate": round(random.uniform(1.0, 4.0), 2),
        "term_months": random.choice([3, 6, 12, 24, 36, 60]),
        "maturity_date": maturity_date,
        "created_at": today.strftime('%Y-%m-%dT00:00:00.000Z')
    }
    cds.append(cd)
    
    # Save individual CD
    with open(os.path.join(DATA_DIR, f"cds/{cd_id}"), 'w') as f:
        json.dump(cd, f)

# Create CDs index
with open(os.path.join(DATA_DIR, "cds_index"), 'w') as f:
    json.dump([cd["id"] for cd in cds], f)

# Create custom calendar events
calendar_events = []
for i in range(3):
    event_id = generate_id()
    start_date = (today + datetime.timedelta(days=random.randint(1, 60))).strftime('%Y-%m-%dT00:00:00.000Z')
    event_type = random.choice(["income", "expense", "savings", "reminder"])
    event = {
        "id": event_id,
        "user_id": "default-user",
        "title": f"Custom Event {i+1}",
        "start_date": start_date,
        "type": event_type,
        "amount": random.randint(100, 1000) if event_type in ["income", "expense", "savings"] else None,
        "description": f"Test custom event {i+1}",
        "created_at": today.strftime('%Y-%m-%dT00:00:00.000Z')
    }
    calendar_events.append(event)
    
    # Save individual event
    with open(os.path.join(DATA_DIR, f"calendar_events/{event_id}"), 'w') as f:
        json.dump(event, f)

# Create calendar events index
with open(os.path.join(DATA_DIR, "calendar_events_index"), 'w') as f:
    json.dump([event["id"] for event in calendar_events], f)

# Create necessary directories
for directory in ['loans', 'expenses', 'financial_goals', 'cds', 'calendar_events']:
    os.makedirs(os.path.join(DATA_DIR, directory), exist_ok=True)

print(f"Created test data in {DATA_DIR}:")
print(f"- {len(loans)} loans")
print(f"- {len(expenses)} expenses")
print(f"- {len(goals)} financial goals")
print(f"- {len(cds)} CDs")
print(f"- {len(calendar_events)} custom calendar events")
