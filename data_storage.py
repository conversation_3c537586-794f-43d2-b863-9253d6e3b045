import os
import json
import traceback
import shutil
import glob
from datetime import datetime
from typing import Optional
# AI functionality removed

# Import the financial forecast functionality
from financial_forecast import financial_risk_forecast


class DataStorage:
    """
    Bridge class to handle data storage between the web frontend and local filesystem.
    Replaces localStorage with persistent file storage.
    """
    def __init__(self):
        # Create data directory if it doesn't exist
        self.data_dir = os.path.join(os.path.expanduser("~"), ".financial_advisor")
        os.makedirs(self.data_dir, exist_ok=True)
        print(f"Data directory: {self.data_dir}")

        # Keep an in-memory cache of loaded data
        self.data_cache = {}

        # Keep track of indices to ensure consistency
        self.indices = {}

        # Create a backup directory
        self.backup_dir = os.path.join(self.data_dir, "backups")
        os.makedirs(self.backup_dir, exist_ok=True)

        # Create a logs directory
        self.logs_dir = os.path.join(self.data_dir, "logs")
        os.makedirs(self.logs_dir, exist_ok=True)

        # Log rotation settings
        self.max_log_size_mb = 10  # Maximum log file size in MB
        self.max_log_files = 5     # Maximum number of log files to keep

        # Create a logs directory and initialize log file
        self._init_logging()

        # Log that we've initialized the storage
        self._log("DataStorage initialized")

        # AI advisor removed - functionality disabled
        self.ai_advisor = None

        # Check if we need to repair data
        self._check_data_integrity()

        # Clean up old backups
        self._cleanup_old_backups()

    def _init_logging(self):
        """Initialize logging system with rotation"""
        try:
            # Check if we need to migrate the old log file
            old_log_path = os.path.join(self.data_dir, "storage.log")
            if os.path.exists(old_log_path):
                # Get current timestamp for the new log file
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_log_path = os.path.join(self.logs_dir, f"storage_{timestamp}.log")

                # Move the old log file to the logs directory
                shutil.move(old_log_path, new_log_path)
                print(f"[STORAGE] Migrated old log file to {new_log_path}")

            # Set the current log file path
            self.current_log_path = os.path.join(self.logs_dir, "storage_current.log")

            # Create the log file if it doesn't exist
            if not os.path.exists(self.current_log_path):
                with open(self.current_log_path, 'w') as f:
                    f.write(f"Log initialized at {datetime.now().isoformat()}\n")

            # Clean up old log files
            self._cleanup_old_logs()

        except Exception as e:
            print(f"[STORAGE] Error initializing logging: {e}")
            traceback.print_exc()

    def _log(self, message):
        """Log a message to console and log file with rotation"""
        print(f"[STORAGE] {message}")
        try:
            # Add timestamp to the message
            timestamp = datetime.now().isoformat()
            log_message = f"[{timestamp}] {message}\n"

            # Check if we need to rotate the log file
            if os.path.exists(self.current_log_path):
                log_size_mb = os.path.getsize(self.current_log_path) / (1024 * 1024)
                if log_size_mb >= self.max_log_size_mb:
                    self._rotate_logs()

            # Write to the current log file
            with open(self.current_log_path, 'a') as f:
                f.write(log_message)

        except Exception as e:
            print(f"[STORAGE] Error writing to log: {e}")

    def _rotate_logs(self):
        """Rotate log files when the current log gets too large"""
        try:
            # Get current timestamp for the new log file name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            rotated_log_path = os.path.join(self.logs_dir, f"storage_{timestamp}.log")

            # Rename the current log file
            shutil.move(self.current_log_path, rotated_log_path)

            # Create a new current log file
            with open(self.current_log_path, 'w') as f:
                f.write(f"Log rotated at {datetime.now().isoformat()}\n")

            print(f"[STORAGE] Rotated log file to {rotated_log_path}")

            # Clean up old log files
            self._cleanup_old_logs()

        except Exception as e:
            print(f"[STORAGE] Error rotating logs: {e}")
            traceback.print_exc()

    def _cleanup_old_logs(self):
        """Clean up old log files, keeping only the most recent ones"""
        try:
            # Get all log files except the current one
            log_files = glob.glob(os.path.join(self.logs_dir, "storage_*.log"))

            # Sort by modification time (newest first)
            log_files.sort(key=os.path.getmtime, reverse=True)

            # Remove old log files beyond our limit
            if len(log_files) > self.max_log_files:
                for old_log in log_files[self.max_log_files:]:
                    os.remove(old_log)
                    print(f"[STORAGE] Removed old log file: {old_log}")

        except Exception as e:
            print(f"[STORAGE] Error cleaning up old logs: {e}")
            traceback.print_exc()

    def _check_data_integrity(self):
        """Check data integrity and repair if needed"""
        try:
            # Load all indices
            index_files = [f for f in os.listdir(self.data_dir) if f.endswith('_index.json')]
            for index_file in index_files:
                collection_name = index_file.replace('_index.json', '')
                collection_dir = os.path.join(self.data_dir, collection_name)

                # Create collection directory if it doesn't exist
                if not os.path.exists(collection_dir) and collection_name:
                    os.makedirs(collection_dir, exist_ok=True)
                    self._log(f"Created missing collection directory: {collection_dir}")

                # Load the index
                index_path = os.path.join(self.data_dir, index_file)
                with open(index_path, 'r', encoding='utf-8') as f:
                    try:
                        index_data = json.loads(f.read())
                        if isinstance(index_data, list):
                            self.indices[collection_name] = index_data
                            self._log(f"Loaded index for {collection_name} with {len(index_data)} items")
                    except json.JSONDecodeError:
                        self._log(f"Error parsing index file {index_file}")

            # Perform a backup
            self._create_backup()

        except Exception as e:
            self._log(f"Error checking data integrity: {e}")
            traceback.print_exc()

    def _create_backup(self):
        """Create a backup of current data"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(self.backup_dir, f"backup_{timestamp}")
            os.makedirs(backup_path, exist_ok=True)

            # Copy all JSON files
            for file in os.listdir(self.data_dir):
                if file.endswith('.json'):
                    src = os.path.join(self.data_dir, file)
                    dst = os.path.join(backup_path, file)
                    shutil.copy2(src, dst)

            # Copy collection directories
            for item in os.listdir(self.data_dir):
                item_path = os.path.join(self.data_dir, item)
                if os.path.isdir(item_path) and item != "backups" and item != "logs":
                    dst_dir = os.path.join(backup_path, item)
                    os.makedirs(dst_dir, exist_ok=True)

                    # Copy all JSON files in the collection directory
                    for file in os.listdir(item_path):
                        if file.endswith('.json'):
                            src = os.path.join(item_path, file)
                            dst = os.path.join(dst_dir, file)
                            shutil.copy2(src, dst)

            # Record the backup
            self._log(f"Created data backup at {backup_path}")

            # Clean up old backups
            self._cleanup_old_backups()

            return backup_path

        except Exception as e:
            self._log(f"Error creating backup: {e}")
            traceback.print_exc()
            return None

    def _cleanup_old_backups(self):
        """Clean up old backups, keeping only the most recent ones"""
        try:
            # Maximum number of backups to keep
            max_backups = 10

            # Get all backup directories
            backup_dirs = glob.glob(os.path.join(self.backup_dir, "backup_*"))

            # Sort by modification time (newest first)
            backup_dirs.sort(key=os.path.getmtime, reverse=True)

            # Remove old backups beyond our limit
            if len(backup_dirs) > max_backups:
                for old_backup in backup_dirs[max_backups:]:
                    shutil.rmtree(old_backup)
                    self._log(f"Removed old backup: {old_backup}")

        except Exception as e:
            self._log(f"Error cleaning up old backups: {e}")
            traceback.print_exc()

    def _ensure_collection_dir(self, collection):
        """Ensure the collection directory exists"""
        if '/' in collection or '\\' in collection:
            # This is a collection path like 'loans/123'
            parts = collection.replace('\\', '/').split('/')
            collection_dir = os.path.join(self.data_dir, parts[0])
            os.makedirs(collection_dir, exist_ok=True)
            return True
        return False

    def _get_collection_from_key(self, key):
        """Extract collection name from a key"""
        if '/' in key:
            return key.split('/')[0]
        return None

    def get_item(self, key):
        """Get an item from storage"""
        try:
            # Check cache first
            if key in self.data_cache:
                self._log(f"get_item: {key} (from cache)")
                return self.data_cache[key]

            # If not in cache, try to load from file
            self._log(f"get_item: {key}")

            # Normalize path, replace forward slashes with OS-specific separators
            norm_key = key.replace('/', os.path.sep).replace('\\', os.path.sep)
            file_path = os.path.join(self.data_dir, f"{norm_key}.json")

            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = f.read()
                self.data_cache[key] = data
                return data
            else:
                # Check if this is a collection item that should be in an index
                collection = self._get_collection_from_key(key)
                if collection and collection in self.indices:
                    # If item ID is in the index but file is missing, note the inconsistency
                    item_id = key.split('/')[1]
                    if item_id in self.indices[collection]:
                        self._log(f"WARNING: Item {key} is in index but file is missing")

                self._log(f"get_item: {key} not found in storage")
                return None
        except Exception as e:
            self._log(f"Error getting data for {key}: {e}")
            traceback.print_exc()
            return None

    def set_item(self, key, value):
        """Set an item in storage"""
        try:
            self._log(f"set_item: {key}, value length: {len(value)}")

            # Update cache
            self.data_cache[key] = value

            # Normalize path, replace forward slashes with OS-specific separators
            norm_key = key.replace('/', os.path.sep).replace('\\', os.path.sep)
            file_path = os.path.join(self.data_dir, f"{norm_key}.json")

            # Make sure the directory exists
            dir_path = os.path.dirname(file_path)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                self._log(f"Created directory: {dir_path}")

            # Write to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(value)

            # Check if this is a collection item that should be added to an index
            collection = self._get_collection_from_key(key)
            if collection:
                # This is a collection item, update the index
                item_id = key.split('/')[1]
                index_key = f"{collection}_index"
                index_path = os.path.join(self.data_dir, f"{index_key}.json")

                # Get current index
                index_data = []
                if os.path.exists(index_path):
                    try:
                        with open(index_path, 'r', encoding='utf-8') as f:
                            index_data = json.loads(f.read())
                    except Exception as e:
                        self._log(f"Error reading {index_path}, will create new empty index: {e}")

                # Add item to index if not already there
                if item_id not in index_data:
                    index_data.append(item_id)
                    index_json = json.dumps(index_data)

                    # Update cache and file
                    self.data_cache[index_key] = index_json
                    with open(index_path, 'w', encoding='utf-8') as f:
                        f.write(index_json)

                    # Store in our indices map
                    self.indices[collection] = index_data

                    self._log(f"Updated index for {collection}, now has {len(index_data)} items")
                else:
                    self._log(f"Item already in index for {collection}")

            self._log(f"set_item: {key} saved to file: {file_path}")
            self._log(f"Value content (first 100 chars): {value[:100]}...")
            return True
        except Exception as e:
            self._log(f"Error saving data for {key}: {e}")
            traceback.print_exc()
            return False

    def remove_item(self, key):
        """Remove an item from storage"""
        try:
            self._log(f"remove_item: {key}")

            # Remove from cache
            if key in self.data_cache:
                del self.data_cache[key]

            # Normalize path, replace forward slashes with OS-specific separators
            norm_key = key.replace('/', os.path.sep).replace('\\', os.path.sep)
            file_path = os.path.join(self.data_dir, f"{norm_key}.json")

            # Remove the file if it exists
            if os.path.exists(file_path):
                os.remove(file_path)
                self._log(f"Removed file: {file_path}")
            else:
                self._log(f"File not found: {file_path}")

            # Check if this is a collection item that should be removed from an index
            collection = self._get_collection_from_key(key)
            if collection:
                # This is a collection item, update the index
                item_id = key.split('/')[1]
                index_key = f"{collection}_index"
                index_path = os.path.join(self.data_dir, f"{index_key}.json")

                # Get current index
                index_data = []
                if os.path.exists(index_path):
                    try:
                        with open(index_path, 'r', encoding='utf-8') as f:
                            index_data = json.loads(f.read())
                    except Exception as e:
                        self._log(f"Error reading {index_path}: {e}")
                        return False

                # Remove item from index if it exists
                if item_id in index_data:
                    index_data.remove(item_id)
                    index_json = json.dumps(index_data)

                    # Update cache and file
                    self.data_cache[index_key] = index_json
                    with open(index_path, 'w', encoding='utf-8') as f:
                        f.write(index_json)

                    # Store in our indices map
                    self.indices[collection] = index_data

                    self._log(f"Updated index for {collection}, now has {len(index_data)} items")
                else:
                    self._log(f"Item not found in index for {collection}")

            self._log(f"remove_item: {key} removed successfully")
            return True
        except Exception as e:
            self._log(f"Error removing data for {key}: {e}")
            traceback.print_exc()
            return False

    def get_all_keys(self):
        """Get all keys in storage"""
        try:
            self._log("get_all_keys: retrieving all storage keys")
            keys = []
            
            # Walk through the data directory and find all .json files
            for root, dirs, files in os.walk(self.data_dir):
                for file in files:
                    if file.endswith('.json'):
                        # Get the relative path from data_dir
                        full_path = os.path.join(root, file)
                        rel_path = os.path.relpath(full_path, self.data_dir)
                        
                        # Remove the .json extension and normalize path separators
                        key = rel_path[:-5].replace(os.path.sep, '/')
                        keys.append(key)
            
            self._log(f"get_all_keys: found {len(keys)} keys")
            return json.dumps(keys)
        except Exception as e:
            self._log(f"Error getting all keys: {e}")
            traceback.print_exc()
            return json.dumps([])

    def clean_indexes(self):
        """Clean up indexes to remove phantom references"""
        try:
            self._log("Starting index cleanup")

            # Create a backup first
            backup_path = self._create_backup()
            if backup_path:
                self._log(f"Created backup before index cleanup at {backup_path}")

            # Track statistics
            stats = {
                "collections_processed": 0,
                "phantom_references_removed": 0,
                "errors": 0
            }

            # Process each collection index
            for collection, index_data in self.indices.items():
                try:
                    self._log(f"Processing index for collection: {collection}")
                    stats["collections_processed"] += 1

                    # Check each item in the index
                    valid_items = []
                    for item_id in index_data:
                        item_key = f"{collection}/{item_id}"
                        item_path = os.path.join(self.data_dir, f"{item_key}.json")

                        if os.path.exists(item_path):
                            valid_items.append(item_id)
                        else:
                            self._log(f"Removing phantom reference: {item_key}")
                            stats["phantom_references_removed"] += 1

                    # Update the index if needed
                    if len(valid_items) != len(index_data):
                        self._log(f"Updating index for {collection}: {len(index_data)} -> {len(valid_items)} items")

                        # Update the index file
                        index_key = f"{collection}_index"
                        index_path = os.path.join(self.data_dir, f"{index_key}.json")
                        index_json = json.dumps(valid_items)

                        # Update cache and file
                        self.data_cache[index_key] = index_json
                        with open(index_path, 'w', encoding='utf-8') as f:
                            f.write(index_json)

                        # Update our indices map
                        self.indices[collection] = valid_items

                except Exception as e:
                    self._log(f"Error processing index for {collection}: {e}")
                    stats["errors"] += 1
                    traceback.print_exc()

            # Return results as JSON
            result = {
                "success": True,
                "message": f"Cleaned {stats['phantom_references_removed']} phantom references from {stats['collections_processed']} collections",
                "stats": stats
            }

            return json.dumps(result)

        except Exception as e:
            self._log(f"Error in clean_indexes: {e}")
            traceback.print_exc()
            return json.dumps({
                "success": False,
                "error": str(e)
            })

    def repair_storage(self):
        """Repair storage inconsistencies"""
        try:
            self._log("Starting storage repair")

            # Create a backup first if one wasn't created by clean_indexes
            backup_path = self._create_backup()
            if backup_path:
                self._log(f"Created backup before storage repair at {backup_path}")

            # Track statistics
            stats = {
                "collections_processed": 0,
                "files_processed": 0,
                "orphaned_files_found": 0,
                "errors": 0
            }

            # Check for orphaned files (files not in any index)
            for root, dirs, files in os.walk(self.data_dir):
                # Skip backup and logs directories
                if "backups" in root or "logs" in root:
                    continue

                # Process JSON files
                for file in files:
                    if file.endswith('.json') and not file.endswith('_index.json'):
                        stats["files_processed"] += 1

                        try:
                            # Get relative path from data directory
                            rel_path = os.path.relpath(os.path.join(root, file), self.data_dir)

                            # Convert to key format (remove .json extension)
                            key = rel_path.replace(os.path.sep, '/').replace('.json', '')

                            # Check if this is a collection item
                            collection = self._get_collection_from_key(key)
                            if collection:
                                # This is a collection item, check if it's in the index
                                item_id = key.split('/')[1]

                                if collection in self.indices and item_id not in self.indices[collection]:
                                    self._log(f"Found orphaned file: {key}")
                                    stats["orphaned_files_found"] += 1

                                    # Add to index
                                    index_key = f"{collection}_index"
                                    index_path = os.path.join(self.data_dir, f"{index_key}.json")

                                    # Get current index
                                    index_data = self.indices.get(collection, [])

                                    # Add item to index
                                    index_data.append(item_id)
                                    index_json = json.dumps(index_data)

                                    # Update cache and file
                                    self.data_cache[index_key] = index_json
                                    with open(index_path, 'w', encoding='utf-8') as f:
                                        f.write(index_json)

                                    # Update our indices map
                                    self.indices[collection] = index_data

                                    self._log(f"Added orphaned file to index: {key}")

                        except Exception as e:
                            self._log(f"Error processing file {file}: {e}")
                            stats["errors"] += 1

                # Process collection directories
                if os.path.basename(root) != self.data_dir and not root.startswith(os.path.join(self.data_dir, "backups")) and not root.startswith(os.path.join(self.data_dir, "logs")):
                    collection = os.path.basename(root)
                    stats["collections_processed"] += 1

                    # Make sure this collection has an index
                    if collection not in self.indices:
                        self._log(f"Creating missing index for collection: {collection}")

                        # Create empty index
                        index_key = f"{collection}_index"
                        index_path = os.path.join(self.data_dir, f"{index_key}.json")
                        index_json = json.dumps([])

                        # Update cache and file
                        self.data_cache[index_key] = index_json
                        with open(index_path, 'w', encoding='utf-8') as f:
                            f.write(index_json)

                        # Update our indices map
                        self.indices[collection] = []

            # Return results as JSON
            result = {
                "success": True,
                "message": f"Processed {stats['files_processed']} files, found and fixed {stats['orphaned_files_found']} orphaned files",
                "stats": stats
            }

            return json.dumps(result)

        except Exception as e:
            self._log(f"Error in repair_storage: {e}")
            traceback.print_exc()
            return json.dumps({
                "success": False,
                "error": str(e)
            })

    def get_storage_stats(self):
        """Get statistics about the storage"""
        try:
            self._log("Getting storage statistics")

            # Track statistics
            stats = {
                "total_items": 0,
                "collections": {},
                "index_items": 0,
                "orphaned_files": 0
            }

            # Count items in each collection
            for collection, index_data in self.indices.items():
                stats["collections"][collection] = len(index_data)
                stats["total_items"] += len(index_data)
                stats["index_items"] += len(index_data)

            # Return results as JSON
            result = {
                "success": True,
                "total_items": stats["total_items"],
                "collections": stats["collections"]
            }

            return json.dumps(result)

        except Exception as e:
            self._log(f"Error in get_storage_stats: {e}")
            traceback.print_exc()
            return json.dumps({
                "success": False,
                "error": str(e)
            })



    def get_collection(self, collection):
        """Get all items from a collection"""
        try:
            self._log(f"get_collection: {collection}")

            # Get the collection index
            if collection not in self.indices:
                self._log(f"Collection {collection} not found in indices")
                return []

            # Get all items in the collection
            items = []
            for item_id in self.indices[collection]:
                item_key = f"{collection}/{item_id}"
                item_data = self.get_item(item_key)
                if item_data:
                    try:
                        # Parse the JSON data
                        parsed_item = json.loads(item_data)
                        items.append(parsed_item)
                    except json.JSONDecodeError as e:
                        self._log(f"Error parsing item {item_key}: {e}")
                        continue

            self._log(f"get_collection: {collection} returned {len(items)} items")
            return items  # Return the actual list, not JSON string

        except Exception as e:
            self._log(f"Error getting collection {collection}: {e}")
            traceback.print_exc()
            return []  # Return empty list, not JSON string

    def run_financial_plan_projection(self, planner_settings_json_string: Optional[str] = None) -> str:
        """
        Runs the financial plan projection based on stored data and provided settings.
        Args:
            planner_settings_json_string: Optional JSON string containing planner settings.
                                          If None, settings are fetched from storage.
        Returns:
            A JSON string of the projection results.
        """
        try:
            self._log("Received request to run financial plan projection.")

            # Import projection engine classes first
            try:
                from .projection_engine import run_projection, PlannerInputData, Loan, CD, Fund, ExpenseCategory, PlannerSettings
            except ImportError:
                 from projection_engine import run_projection, PlannerInputData, Loan, CD, Fund, ExpenseCategory, PlannerSettings

            # 1. Determine Planner Settings
            settings_data = {}
            if planner_settings_json_string:
                try:
                    settings_data = json.loads(planner_settings_json_string)
                except json.JSONDecodeError as je:
                    self._log(f"Error parsing planner_settings_json_string: {je}")
                    # Fallback to stored or default settings
                    stored_settings_json = self.get_item('planner_settings/default_settings')
                    if stored_settings_json:
                        settings_data = json.loads(stored_settings_json)
            else:
                stored_settings_json = self.get_item('planner_settings/default_settings')
                if stored_settings_json:
                    settings_data = json.loads(stored_settings_json)

            # Use fund balance as savings buffer if available
            fund_balance = float(settings_data.get("fund_balance", 0.0))
            savings_buffer = float(settings_data.get("available_savings_buffer", fund_balance))

            planner_settings = PlannerSettings(
                available_savings_buffer=savings_buffer,
                projection_period_months=int(settings_data.get("projection_period_months", 24))
            )
            self._log(f"Using planner settings: {planner_settings}")

            # 2. Fetch Data from DataStorage and Convert to Dataclasses
            loans_list = []
            loans_json_str = self.get_collection('loans')
            if loans_json_str:
                loan_dicts_list = json.loads(loans_json_str)
                for loan_dict in loan_dicts_list:
                    # Map UI fields to projection engine fields
                    mapped_loan = {
                        'loan_id': loan_dict.get('id', ''),
                        'loan_name': loan_dict.get('loan_name', ''),
                        'original_principal': float(loan_dict.get('original_principal', 0)),
                        'current_principal_balance': float(loan_dict.get('remaining_balance', 0)),
                        'interest_rate': float(loan_dict.get('interest_rate', 0)),
                        'loan_term_months': int(loan_dict.get('loan_term_months', 0)),
                        'monthly_payment': float(loan_dict.get('monthly_payment', 0)),
                        'loan_start_date': loan_dict.get('start_date', ''),
                        'next_payment_date': loan_dict.get('next_payment_date', ''),
                        'remaining_term_months': loan_dict.get('remaining_term_months'),
                        'lender_name': loan_dict.get('lender_name'),
                        'loan_type': loan_dict.get('loan_type')
                    }
                    loans_list.append(Loan(**mapped_loan))

            cds_list = []
            cds_json_str = self.get_collection('cds')
            if cds_json_str:
                cd_dicts_list = json.loads(cds_json_str)
                for cd_dict in cd_dicts_list:
                    # Map UI fields to projection engine fields
                    mapped_cd = {
                        'cd_id': cd_dict.get('id', ''),
                        'cd_name': cd_dict.get('cd_name', ''),
                        'principal_amount': float(cd_dict.get('principal', 0)),
                        'current_value': float(cd_dict.get('current_value', cd_dict.get('principal', 0))),
                        'interest_rate': float(cd_dict.get('interest_rate', 0)),
                        'term_months': int(cd_dict.get('term_months', 0)),
                        'start_date': cd_dict.get('start_date', ''),
                        'maturity_date': cd_dict.get('maturity_date', ''),
                        'interest_payout_frequency': cd_dict.get('interest_payout_frequency', 'monthly'),
                        'interest_treatment': cd_dict.get('interest_treatment', 'payout'),
                        'last_value_update_date': cd_dict.get('last_value_update_date', ''),
                        'payout_account_id': cd_dict.get('payout_account_id'),
                        'institution_name': cd_dict.get('institution_name')
                    }
                    cds_list.append(CD(**mapped_cd))

            funds_list = []
            funds_json_str = self.get_collection('fund_accounts')
            if funds_json_str:
                fund_dicts_list = json.loads(funds_json_str)
                for fund_dict in fund_dicts_list:
                    # Map UI fields to projection engine fields
                    mapped_fund = {
                        'fund_id': fund_dict.get('id', ''),
                        'fund_name': fund_dict.get('fund_name', ''),
                        'expected_monthly_income': float(fund_dict.get('expected_monthly_income', 0)),
                        'last_income_update_date': fund_dict.get('last_income_update_date')
                    }
                    funds_list.append(Fund(**mapped_fund))

            expense_categories_list = []
            # Try to get planner expense categories, if not available, create default ones
            exp_cat_json_str = self.get_collection('planner_expense_categories')
            if exp_cat_json_str:
                exp_cat_dicts_list = json.loads(exp_cat_json_str)
                for cat_dict in exp_cat_dicts_list:
                    mapped_expense = {
                        'expense_category_id': cat_dict.get('id', ''),
                        'category_name': cat_dict.get('category_name', ''),
                        'budgeted_monthly_amount': float(cat_dict.get('budgeted_monthly_amount', 0)),
                        'is_essential': cat_dict.get('is_essential', False)
                    }
                    expense_categories_list.append(ExpenseCategory(**mapped_expense))
            else:
                # Create default expense categories based on the loan coverage calculation
                # Use monthly_expenses from settings if provided, otherwise use defaults
                monthly_expenses = float(settings_data.get("monthly_expenses", 92048.0))

                default_expenses = [
                    {
                        'expense_category_id': 'loan_payments',
                        'category_name': 'Monthly Loan Payments',
                        'budgeted_monthly_amount': monthly_expenses,
                        'is_essential': True
                    }
                ]
                for expense_dict in default_expenses:
                    expense_categories_list.append(ExpenseCategory(**expense_dict))

            input_data = PlannerInputData(
                loans=loans_list,
                cds=cds_list,
                funds=funds_list,
                expense_categories=expense_categories_list,
                settings=planner_settings
            )

            self._log("Calling projection engine...")
            projection_results = run_projection(input_data)
            self._log("Projection engine completed.")

            return json.dumps(projection_results, default=str)

        except Exception as e:
            self._log(f"Error in run_financial_plan_projection: {e}")
            detailed_error = traceback.format_exc()
            self._log(detailed_error)
            return json.dumps({"error": str(e), "trace": detailed_error})

    # AI Advisor Methods - Disabled
    def ai_chat(self, message):
        """Chat with AI financial advisor - Currently disabled"""
        return json.dumps({"error": "AI functionality has been disabled. Please use the analysis tools above for financial insights."})

    def ai_analyze_expenses(self):
        """Analyze expenses using AI - Currently disabled"""
        return json.dumps({"error": "AI expense analysis has been disabled. Please use the manual expense tracking and categorization features."})

    def ai_optimize_loans(self):
        """Optimize loan payments using AI - Currently disabled"""
        return json.dumps({"error": "AI loan optimization has been disabled. Please use manual loan tracking and payment planning."})

    def ai_financial_health(self):
        """Get comprehensive financial health check using AI - Currently disabled"""
        return json.dumps({"error": "AI financial health analysis has been disabled. Please review your financial data manually using the dashboard."})

    def _get_financial_context(self):
        """Get basic financial context for AI"""
        try:
            loans = self._get_loans_data()
            accounts = self._get_bank_accounts_data()

            total_debt = sum(float(loan.get('remaining_balance', 0)) for loan in loans)
            total_savings = sum(float(account.get('balance', 0)) for account in accounts)

            return {
                "total_debt": total_debt,
                "total_savings": total_savings,
                "loan_count": len(loans),
                "account_count": len(accounts)
            }
        except:
            return {}

    def _get_loans_data(self):
        """Get loans data for AI analysis"""
        try:
            loans_json = self.get_collection("loans")
            return json.loads(loans_json) if loans_json else []
        except:
            return []

    def _get_bank_accounts_data(self):
        """Get bank accounts data for AI analysis"""
        try:
            accounts_json = self.get_collection("bank_accounts")
            return json.loads(accounts_json) if accounts_json else []
        except:
            return []

    def _get_cds_data(self):
        """Get CDs data for AI analysis"""
        try:
            cds_json = self.get_collection("cds")
            return json.loads(cds_json) if cds_json else []
        except:
            return []

    def _get_expenses_data(self):
        """Get expenses data for AI analysis"""
        try:
            expenses_json = self.get_collection("expenses")
            return json.loads(expenses_json) if expenses_json else []
        except:
            return []

    def _get_income_data(self):
        """Get income data for AI analysis"""
        try:
            income_json = self.get_collection("income_sources")
            return json.loads(income_json) if income_json else []
        except:
            return []

    # Financial Forecast Methods
    def calculate_financial_forecast(self, inputs_json):
        """
        Calculate financial forecast using the Python backend

        Args:
            inputs_json (str): JSON string containing forecast inputs

        Returns:
            str: JSON string containing forecast results
        """
        try:
            self._log(f"Calculating financial forecast with inputs: {inputs_json}")

            # Parse the inputs
            inputs = json.loads(inputs_json)

            # Call the Python forecast function
            result = financial_risk_forecast(inputs)

            # Convert result to JSON
            result_json = json.dumps(result)

            self._log(f"Financial forecast calculated successfully")
            return result_json

        except Exception as e:
            self._log(f"Error calculating financial forecast: {e}")
            traceback.print_exc()
            return json.dumps({
                "error": f"Failed to calculate forecast: {str(e)}"
            })

    def get_forecast_settings(self):
        """
        Get saved forecast settings

        Returns:
            str: JSON string containing saved forecast settings or None
        """
        try:
            self._log("Loading forecast settings")

            # Try to load from the forecast_settings collection
            settings_data = self.get_item("forecast_settings/forecast_settings_1")

            if settings_data:
                self._log("Forecast settings loaded successfully")
                return settings_data
            else:
                self._log("No saved forecast settings found")
                return None

        except Exception as e:
            self._log(f"Error loading forecast settings: {e}")
            traceback.print_exc()
            return None

    def save_forecast_settings(self, settings_json):
        """
        Save forecast settings

        Args:
            settings_json (str): JSON string containing forecast settings

        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            self._log(f"Saving forecast settings: {settings_json}")

            # Save the settings
            success = self.set_item("forecast_settings/forecast_settings_1", settings_json)

            if success:
                self._log("Forecast settings saved successfully")
            else:
                self._log("Failed to save forecast settings")

            return success

        except Exception as e:
            self._log(f"Error saving forecast settings: {e}")
            traceback.print_exc()
            return False

    def load_actual_financial_data(self):
        """
        Load actual financial data from the user's records to populate forecast inputs

        Returns:
            str: JSON string containing actual financial data
        """
        try:
            self._log("Loading actual financial data for forecast")

            # Load loans to calculate monthly payments
            loans = self._get_loans_data()
            total_monthly_loans = sum(loan.get('monthly_payment', 0) for loan in loans)

            # Load expenses to estimate monthly expenses (current month)
            expenses = self._get_expenses_data()
            current_month = datetime.now().month
            monthly_expenses = sum(
                expense.get('amount', 0)
                for expense in expenses
                if datetime.fromisoformat(expense.get('date', '2024-01-01')).month == current_month
            )

            # Load bank accounts to get total savings
            bank_accounts = self._get_bank_accounts_data()
            total_savings = sum(account.get('balance', 0) for account in bank_accounts)

            # Prepare the result
            result = {
                "monthly_loans": total_monthly_loans,
                "monthly_expenses": monthly_expenses,
                "current_savings": total_savings,
                "data_loaded": True
            }

            self._log(f"Actual financial data loaded: loans={total_monthly_loans}, expenses={monthly_expenses}, savings={total_savings}")

            return json.dumps(result)

        except Exception as e:
            self._log(f"Error loading actual financial data: {e}")
            traceback.print_exc()
            return json.dumps({
                "monthly_loans": 0,
                "monthly_expenses": 0,
                "current_savings": 0,
                "data_loaded": False,
                "error": str(e)
            })

    # ============================================
    # INVESTMENT PORTFOLIO MANAGEMENT METHODS
    # ============================================

    def get_investment_portfolios(self):
        """Get all investment portfolios for the current user"""
        try:
            self._log("get_investment_portfolios: retrieving all portfolios")
            portfolios = self.get_collection('investment_portfolios')
            self._log(f"Retrieved {len(portfolios)} investment portfolios")
            return json.dumps(portfolios)
        except Exception as e:
            self._log(f"Error getting investment portfolios: {e}")
            traceback.print_exc()
            return json.dumps([])

    def save_investment_portfolio(self, portfolio_data):
        """Save a new investment portfolio"""
        try:
            if isinstance(portfolio_data, str):
                portfolio_dict = json.loads(portfolio_data)
            else:
                portfolio_dict = portfolio_data

            portfolio_id = portfolio_dict.get('id')
            if not portfolio_id:
                portfolio_id = str(int(datetime.now().timestamp() * 1000))
                portfolio_dict['id'] = portfolio_id

            portfolio_dict['created_at'] = portfolio_dict.get('created_at', datetime.now().isoformat())
            portfolio_dict['updated_at'] = datetime.now().isoformat()

            key = f"investment_portfolios/{portfolio_id}"
            success = self.set_item(key, json.dumps(portfolio_dict))

            self._log(f"save_investment_portfolio: {portfolio_id} - {'success' if success else 'failed'}")
            
            return json.dumps({
                "success": success,
                "id": portfolio_id,
                "portfolio": portfolio_dict if success else None
            })
        except Exception as e:
            self._log(f"Error saving investment portfolio: {e}")
            traceback.print_exc()
            return json.dumps({"success": False, "error": str(e)})

    def update_investment_portfolio(self, portfolio_id, updates):
        """Update an existing investment portfolio"""
        try:
            if isinstance(updates, str):
                updates_dict = json.loads(updates)
            else:
                updates_dict = updates

            # Get existing portfolio
            existing_data = self.get_item(f"investment_portfolios/{portfolio_id}")
            if not existing_data:
                return json.dumps({"success": False, "error": "Portfolio not found"})

            existing_portfolio = json.loads(existing_data)
            
            # Merge updates
            existing_portfolio.update(updates_dict)
            existing_portfolio['updated_at'] = datetime.now().isoformat()

            # Save updated portfolio
            key = f"investment_portfolios/{portfolio_id}"
            success = self.set_item(key, json.dumps(existing_portfolio))

            return json.dumps({
                "success": success,
                "portfolio": existing_portfolio if success else None
            })
        except Exception as e:
            self._log(f"Error updating investment portfolio {portfolio_id}: {e}")
            traceback.print_exc()
            return json.dumps({"success": False, "error": str(e)})

    def delete_investment_portfolio(self, portfolio_id):
        """Delete an investment portfolio"""
        try:
            # Also delete all holdings for this portfolio
            holdings = self.get_portfolio_holdings(portfolio_id)
            if isinstance(holdings, str):
                holdings = json.loads(holdings)
            
            for holding in holdings:
                self.delete_stock_holding(holding.get('id'))

            # Delete the portfolio
            success = self.remove_item(f"investment_portfolios/{portfolio_id}")
            
            return json.dumps({"success": success})
        except Exception as e:
            self._log(f"Error deleting investment portfolio {portfolio_id}: {e}")
            traceback.print_exc()
            return json.dumps({"success": False, "error": str(e)})

    def get_portfolio_holdings(self, portfolio_id):
        """Get all stock holdings for a specific portfolio"""
        try:
            self._log(f"get_portfolio_holdings: retrieving holdings for portfolio {portfolio_id}")
            all_holdings = self.get_collection('stock_holdings')
            
            # Filter holdings for this portfolio
            portfolio_holdings = [
                holding for holding in all_holdings 
                if holding.get('portfolio_id') == portfolio_id
            ]
            
            self._log(f"Retrieved {len(portfolio_holdings)} holdings for portfolio {portfolio_id}")
            return json.dumps(portfolio_holdings)
        except Exception as e:
            self._log(f"Error getting portfolio holdings for {portfolio_id}: {e}")
            traceback.print_exc()
            return json.dumps([])

    def save_stock_holding(self, holding_data):
        """Save a new stock holding"""
        try:
            if isinstance(holding_data, str):
                holding_dict = json.loads(holding_data)
            else:
                holding_dict = holding_data

            holding_id = holding_dict.get('id')
            if not holding_id:
                holding_id = str(int(datetime.now().timestamp() * 1000))
                holding_dict['id'] = holding_id

            holding_dict['created_at'] = holding_dict.get('created_at', datetime.now().isoformat())
            holding_dict['updated_at'] = datetime.now().isoformat()

            key = f"stock_holdings/{holding_id}"
            success = self.set_item(key, json.dumps(holding_dict))

            # Also record the transaction
            transaction_data = {
                'id': str(int(datetime.now().timestamp() * 1000) + 1),
                'user_id': holding_dict.get('user_id', 'current_user'),
                'portfolio_id': holding_dict.get('portfolio_id'),
                'symbol': holding_dict.get('symbol'),
                'transaction_type': 'buy',
                'shares': holding_dict.get('shares'),
                'price': holding_dict.get('average_cost'),
                'total_amount': holding_dict.get('total_cost'),
                'fees': 0,
                'notes': 'Initial purchase',
                'transaction_date': holding_dict.get('created_at'),
                'created_at': datetime.now().isoformat()
            }
            self.save_trading_transaction(transaction_data)

            self._log(f"save_stock_holding: {holding_id} - {'success' if success else 'failed'}")
            
            return json.dumps({
                "success": success,
                "id": holding_id,
                "holding": holding_dict if success else None
            })
        except Exception as e:
            self._log(f"Error saving stock holding: {e}")
            traceback.print_exc()
            return json.dumps({"success": False, "error": str(e)})

    def update_stock_holding(self, holding_id, updates):
        """Update an existing stock holding"""
        try:
            if isinstance(updates, str):
                updates_dict = json.loads(updates)
            else:
                updates_dict = updates

            # Get existing holding
            existing_data = self.get_item(f"stock_holdings/{holding_id}")
            if not existing_data:
                return json.dumps({"success": False, "error": "Holding not found"})

            existing_holding = json.loads(existing_data)
            
            # Merge updates
            existing_holding.update(updates_dict)
            existing_holding['updated_at'] = datetime.now().isoformat()

            # Save updated holding
            key = f"stock_holdings/{holding_id}"
            success = self.set_item(key, json.dumps(existing_holding))

            return json.dumps({
                "success": success,
                "holding": existing_holding if success else None
            })
        except Exception as e:
            self._log(f"Error updating stock holding {holding_id}: {e}")
            traceback.print_exc()
            return json.dumps({"success": False, "error": str(e)})

    def delete_stock_holding(self, holding_id):
        """Delete/sell a stock holding"""
        try:
            # Get the holding data before deleting for transaction record
            holding_data = self.get_item(f"stock_holdings/{holding_id}")
            if holding_data:
                holding_dict = json.loads(holding_data)
                
                # Record sell transaction
                transaction_data = {
                    'id': str(int(datetime.now().timestamp() * 1000)),
                    'user_id': holding_dict.get('user_id', 'current_user'),
                    'portfolio_id': holding_dict.get('portfolio_id'),
                    'symbol': holding_dict.get('symbol'),
                    'transaction_type': 'sell',
                    'shares': holding_dict.get('shares'),
                    'price': holding_dict.get('current_price', holding_dict.get('average_cost')),
                    'total_amount': holding_dict.get('current_value', holding_dict.get('total_cost')),
                    'fees': 0,
                    'notes': 'Position closed',
                    'transaction_date': datetime.now().isoformat(),
                    'created_at': datetime.now().isoformat()
                }
                self.save_trading_transaction(transaction_data)

            # Delete the holding
            success = self.remove_item(f"stock_holdings/{holding_id}")
            
            return json.dumps({"success": success})
        except Exception as e:
            self._log(f"Error deleting stock holding {holding_id}: {e}")
            traceback.print_exc()
            return json.dumps({"success": False, "error": str(e)})

    def get_trading_transactions(self, portfolio_id=None):
        """Get trading transaction history"""
        try:
            self._log(f"get_trading_transactions: portfolio_id={portfolio_id}")
            all_transactions = self.get_collection('trading_transactions')
            
            if portfolio_id:
                # Filter transactions for specific portfolio
                portfolio_transactions = [
                    transaction for transaction in all_transactions 
                    if transaction.get('portfolio_id') == portfolio_id
                ]
                return json.dumps(portfolio_transactions)
            
            return json.dumps(all_transactions)
        except Exception as e:
            self._log(f"Error getting trading transactions: {e}")
            traceback.print_exc()
            return json.dumps([])

    def save_trading_transaction(self, transaction_data):
        """Save a trading transaction"""
        try:
            if isinstance(transaction_data, str):
                transaction_dict = json.loads(transaction_data)
            else:
                transaction_dict = transaction_data

            transaction_id = transaction_dict.get('id')
            if not transaction_id:
                transaction_id = str(int(datetime.now().timestamp() * 1000))
                transaction_dict['id'] = transaction_id

            transaction_dict['created_at'] = transaction_dict.get('created_at', datetime.now().isoformat())

            key = f"trading_transactions/{transaction_id}"
            success = self.set_item(key, json.dumps(transaction_dict))

            self._log(f"save_trading_transaction: {transaction_id} - {'success' if success else 'failed'}")
            
            return json.dumps({
                "success": success,
                "id": transaction_id,
                "transaction": transaction_dict if success else None
            })
        except Exception as e:
            self._log(f"Error saving trading transaction: {e}")
            traceback.print_exc()
            return json.dumps({"success": False, "error": str(e)})

    def get_investment_watchlist(self):
        """Get user's investment watchlist"""
        try:
            self._log("get_investment_watchlist: retrieving watchlist")
            watchlist_data = self.get_item('investment_watchlist/default')
            
            if watchlist_data:
                watchlist = json.loads(watchlist_data)
                return json.dumps(watchlist.get('symbols', []))
            
            return json.dumps([])
        except Exception as e:
            self._log(f"Error getting investment watchlist: {e}")
            traceback.print_exc()
            return json.dumps([])

    def add_to_watchlist(self, symbol):
        """Add a symbol to the watchlist"""
        try:
            current_watchlist = json.loads(self.get_investment_watchlist())
            
            if symbol not in current_watchlist:
                current_watchlist.append(symbol)
                
                watchlist_data = {
                    'symbols': current_watchlist,
                    'updated_at': datetime.now().isoformat()
                }
                
                success = self.set_item('investment_watchlist/default', json.dumps(watchlist_data))
                return json.dumps({"success": success})
            
            return json.dumps({"success": True, "message": "Symbol already in watchlist"})
        except Exception as e:
            self._log(f"Error adding to watchlist: {e}")
            traceback.print_exc()
            return json.dumps({"success": False, "error": str(e)})

    def remove_from_watchlist(self, symbol):
        """Remove a symbol from the watchlist"""
        try:
            current_watchlist = json.loads(self.get_investment_watchlist())
            
            if symbol in current_watchlist:
                current_watchlist.remove(symbol)
                
                watchlist_data = {
                    'symbols': current_watchlist,
                    'updated_at': datetime.now().isoformat()
                }
                
                success = self.set_item('investment_watchlist/default', json.dumps(watchlist_data))
                return json.dumps({"success": success})
            
            return json.dumps({"success": True, "message": "Symbol not in watchlist"})
        except Exception as e:
            self._log(f"Error removing from watchlist: {e}")
            traceback.print_exc()
            return json.dumps({"success": False, "error": str(e)})

    def calculate_portfolio_analytics(self, portfolio_id):
        """Calculate portfolio performance metrics"""
        try:
            self._log(f"calculate_portfolio_analytics: portfolio_id={portfolio_id}")
            
            holdings_data = self.get_portfolio_holdings(portfolio_id)
            holdings = json.loads(holdings_data) if holdings_data else []
            
            if not holdings:
                return json.dumps({
                    'total_value': 0,
                    'total_cost': 0,
                    'total_gain_loss': 0,
                    'gain_loss_percentage': 0,
                    'top_performers': [],
                    'allocation': []
                })
            
            total_value = sum(holding.get('current_value', 0) for holding in holdings)
            total_cost = sum(holding.get('total_cost', 0) for holding in holdings)
            total_gain_loss = total_value - total_cost
            gain_loss_percentage = (total_gain_loss / total_cost * 100) if total_cost > 0 else 0
            
            # Calculate top performers
            top_performers = []
            for holding in holdings:
                cost = holding.get('total_cost', 0)
                value = holding.get('current_value', 0)
                if cost > 0:
                    gain_loss_pct = ((value - cost) / cost) * 100
                    top_performers.append({
                        'symbol': holding.get('symbol'),
                        'gain_loss_percentage': gain_loss_pct
                    })
            
            top_performers.sort(key=lambda x: x['gain_loss_percentage'], reverse=True)
            top_performers = top_performers[:5]  # Top 5
            
            # Calculate allocation
            allocation = []
            for holding in holdings:
                value = holding.get('current_value', 0)
                if total_value > 0:
                    percentage = (value / total_value) * 100
                    allocation.append({
                        'symbol': holding.get('symbol'),
                        'percentage': percentage,
                        'value': value
                    })
            
            allocation.sort(key=lambda x: x['percentage'], reverse=True)
            
            result = {
                'total_value': total_value,
                'total_cost': total_cost,
                'total_gain_loss': total_gain_loss,
                'gain_loss_percentage': gain_loss_percentage,
                'top_performers': top_performers,
                'allocation': allocation
            }
            
            return json.dumps(result)
            
        except Exception as e:
            self._log(f"Error calculating portfolio analytics: {e}")
            traceback.print_exc()
            return json.dumps({
                'total_value': 0,
                'total_cost': 0,
                'total_gain_loss': 0,
                'gain_loss_percentage': 0,
                'top_performers': [],
                'allocation': []
            })