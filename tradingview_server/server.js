/**
 * Minimal TradingView API Server
 * Extracted and simplified from TradingView-API for Financial Advisor integration
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const axios = require('axios');
const WebSocket = require('ws');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// EGX Stock configuration
const egxStocks = [
  { symbol: 'EGX:EGX30', name: 'EGX 30 Index', type: 'index', color: '#1f77b4' },
  { symbol: 'EGX:COMI', name: 'Commercial International Bank', type: 'bank', color: '#ff7f0e' },
  { symbol: 'EGX:SWDY', name: 'El Sewedy Electric', type: 'industrial', color: '#2ca02c' },
  { symbol: 'EGX:FWRY', name: '<PERSON><PERSON>ry Banking Technology', type: 'fintech', color: '#d62728' },
  { symbol: 'EGX:PHDC', name: 'Palm Hills Development', type: 'real_estate', color: '#9467bd' },
  { symbol: 'EGX:ORAS', name: 'Orascom Construction', type: 'construction', color: '#8c564b' },
  { symbol: 'EGX:ISPH', name: 'Ibn Sina Pharma', type: 'pharma', color: '#e377c2' },
  { symbol: 'EGX:HRHO', name: 'EFG Holding', type: 'financial', color: '#7f7f7f' },
  { symbol: 'EGX:TALAAT', name: 'Talaat Moustafa Group', type: 'real_estate', color: '#bcbd22' },
  { symbol: 'EGX:ETEL', name: 'Egyptian Telecommunications', type: 'telecom', color: '#17becf' }
];

let stockData = {};
let wsConnections = new Map();

// Minimal TradingView WebSocket client implementation
class SimpleTradingViewClient {
  constructor() {
    this.ws = null;
    this.sessionId = Math.random().toString(36).substring(2, 15);
    this.chartSessions = new Map();
  }

  connect() {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket('wss://data.tradingview.com/socket.io/websocket', {
          origin: 'https://data.tradingview.com'
        });

        this.ws.on('open', () => {
          console.log('✅ Connected to TradingView WebSocket');
          this.sendAuth();
          resolve();
        });

        this.ws.on('message', (data) => {
          this.handleMessage(data.toString());
        });

        this.ws.on('error', (error) => {
          console.error('❌ TradingView WebSocket error:', error);
          reject(error);
        });

        this.ws.on('close', () => {
          console.log('🔌 TradingView WebSocket disconnected');
          this.reconnect();
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  sendAuth() {
    this.send('set_auth_token', ['unauthorized_user_token']);
    this.send('chart_create_session', [this.sessionId, '']);
    this.send('quote_create_session', [this.sessionId]);
  }

  send(method, params = []) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = JSON.stringify({
        m: method,
        p: params
      });
      this.ws.send(`~m~${message.length}~m~${message}`);
    }
  }

  subscribeToStock(symbol) {
    const chartId = `chart_${symbol.replace(':', '_')}`;
    this.chartSessions.set(symbol, chartId);
    
    this.send('chart_create_session', [chartId, '']);
    this.send('resolve_symbol', [chartId, 'symbol_1', `=${symbol}`]);
    this.send('create_series', [chartId, 'series_1', 'symbol_1', '1D', 300]);
    this.send('quote_add_symbols', [this.sessionId, symbol]);
  }

  handleMessage(data) {
    try {
      // Parse TradingView message format
      const messages = this.parseMessages(data);
      
      messages.forEach(msg => {
        if (msg.m === 'quote_completed' || msg.m === 'qsd') {
          this.handleQuoteData(msg.p);
        } else if (msg.m === 'series_completed') {
          this.handleSeriesData(msg.p);
        }
      });
    } catch (error) {
      // Silently handle parsing errors - TradingView sends various message formats
    }
  }

  parseMessages(data) {
    const messages = [];
    let pos = 0;
    
    while (pos < data.length) {
      const lengthStart = data.indexOf('~m~', pos);
      if (lengthStart === -1) break;
      
      const lengthEnd = data.indexOf('~m~', lengthStart + 3);
      if (lengthEnd === -1) break;
      
      const length = parseInt(data.substring(lengthStart + 3, lengthEnd));
      const messageData = data.substring(lengthEnd + 3, lengthEnd + 3 + length);
      
      try {
        const message = JSON.parse(messageData);
        messages.push(message);
      } catch (e) {
        // Skip invalid JSON
      }
      
      pos = lengthEnd + 3 + length;
    }
    
    return messages;
  }

  handleQuoteData(params) {
    if (params && params.length >= 2) {
      const symbol = params[1];
      const data = params[2];
      
      if (data && typeof data === 'object') {
        this.updateStockData(symbol, data);
      }
    }
  }

  handleSeriesData(params) {
    // Handle series data for charts if needed
  }

  updateStockData(symbol, data) {
    const stock = egxStocks.find(s => s.symbol === symbol);
    if (!stock) return;

    const price = data.lp || data.price || 0;
    const change = data.ch || 0;
    const changePercent = data.chp || 0;
    
    const stockInfo = {
      symbol: symbol,
      name: stock.name,
      type: stock.type,
      color: stock.color,
      price: price,
      open: data.open_price || price,
      high: data.high_price || price,
      low: data.low_price || price,
      volume: data.volume || 0,
      change: change,
      changePercent: changePercent,
      timestamp: new Date().toISOString()
    };

    stockData[symbol] = stockInfo;
    
    // Broadcast to connected clients
    io.emit('stockUpdate', stockInfo);
  }

  reconnect() {
    setTimeout(() => {
      console.log('🔄 Reconnecting to TradingView...');
      this.connect().catch(console.error);
    }, 5000);
  }
}

// Mock data generator for fallback
function generateMockData() {
  egxStocks.forEach(stock => {
    if (!stockData[stock.symbol]) {
      const basePrice = Math.random() * 100 + 10;
      const change = (Math.random() - 0.5) * 10;
      const changePercent = (change / basePrice) * 100;
      
      stockData[stock.symbol] = {
        symbol: stock.symbol,
        name: stock.name,
        type: stock.type,
        color: stock.color,
        price: basePrice,
        open: basePrice - change,
        high: basePrice + Math.random() * 5,
        low: basePrice - Math.random() * 5,
        volume: Math.floor(Math.random() * 1000000),
        change: change,
        changePercent: changePercent,
        timestamp: new Date().toISOString()
      };
    }
  });
}

// Technical Analysis API using TradingView public endpoints
async function getTechnicalAnalysis(symbol) {
  try {
    const response = await axios.post(
      'https://scanner.tradingview.com/global/scan',
      {
        symbols: { tickers: [symbol] },
        columns: [
          'Recommend.Other', 'Recommend.All', 'Recommend.MA',
          'RSI', 'MACD.macd', 'Stoch.K', 'CCI20'
        ]
      },
      {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        timeout: 5000
      }
    );

    if (response.data && response.data.data && response.data.data[0]) {
      const data = response.data.data[0].d;
      return {
        symbol: symbol,
        recommendation: data[1] || 0,
        buy: Math.max(0, data[1] || 0),
        sell: Math.max(0, -(data[1] || 0)),
        neutral: Math.max(0, 1 - Math.abs(data[1] || 0)),
        indicators: {
          rsi: data[3] || 50,
          macd: data[4] || 0,
          stoch: data[5] || 50,
          cci: data[6] || 0
        }
      };
    }
  } catch (error) {
    console.log(`⚠️ Could not fetch technical analysis for ${symbol}:`, error.message);
  }

  // Return mock technical analysis
  return {
    symbol: symbol,
    recommendation: Math.random() * 2 - 1,
    buy: Math.random(),
    sell: Math.random(),
    neutral: Math.random(),
    indicators: {
      rsi: Math.random() * 100,
      macd: Math.random() * 2 - 1,
      stoch: Math.random() * 100,
      cci: Math.random() * 200 - 100
    }
  };
}

// Initialize TradingView client
let tvClient = null;

function initializeTradingView() {
  console.log('🇪🇬 Initializing EGX Stock Monitor...');
  
  // Generate initial mock data
  generateMockData();
  
  // Try to connect to real TradingView data
  tvClient = new SimpleTradingViewClient();
  
  tvClient.connect()
    .then(() => {
      console.log('✅ TradingView client connected');
      
      // Subscribe to all EGX stocks
      egxStocks.forEach(stock => {
        tvClient.subscribeToStock(stock.symbol);
      });
    })
    .catch(error => {
      console.log('⚠️ Could not connect to TradingView, using mock data:', error.message);
      
      // Update mock data periodically
      setInterval(() => {
        generateMockData();
        Object.values(stockData).forEach(stock => {
          io.emit('stockUpdate', stock);
        });
      }, 10000); // Update every 10 seconds
    });
}

// API Routes
app.get('/api/stocks', (req, res) => {
  res.json(Object.values(stockData));
});

app.get('/api/stocks/:symbol', (req, res) => {
  const symbol = req.params.symbol;
  const stock = stockData[symbol];
  
  if (stock) {
    res.json(stock);
  } else {
    res.status(404).json({ error: 'Stock not found' });
  }
});

app.get('/api/technical-analysis/:symbol', async (req, res) => {
  try {
    const symbol = req.params.symbol;
    const analysis = await getTechnicalAnalysis(symbol);
    res.json(analysis);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    stocks: Object.keys(stockData).length,
    connected: tvClient && tvClient.ws && tvClient.ws.readyState === WebSocket.OPEN
  });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('👤 Client connected:', socket.id);
  
  // Send current stock data to new client
  socket.emit('initialData', Object.values(stockData));
  
  socket.on('disconnect', () => {
    console.log('👤 Client disconnected:', socket.id);
  });
});

// Start server
const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  console.log(`🌐 Financial Advisor TradingView Server running on http://localhost:${PORT}`);
  console.log('📊 EGX Stock data available at: /api/stocks');
  
  // Initialize TradingView connection
  initializeTradingView();
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  if (tvClient && tvClient.ws) {
    tvClient.ws.close();
  }
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

module.exports = app;
