#!/usr/bin/env python3

import sys
sys.path.append('.')
from data_storage import DataStorage
import json

def test_all_features():
    """Comprehensive test of all app features and data integrity"""
    
    print("🧪 COMPREHENSIVE FEATURE TESTING")
    print("=" * 60)
    
    storage = DataStorage()
    
    # Test 1: Core Data Integrity
    print("\n1️⃣ CORE DATA INTEGRITY TEST")
    print("-" * 40)
    
    try:
        # Test all collections
        collections = ['loans', 'bank_accounts', 'cds', 'assets', 'liabilities', 'paid_months', 'cash_flow_config']
        
        for collection in collections:
            try:
                data = storage.get_collection(collection)
                print(f"✅ {collection}: {len(data) if isinstance(data, list) else 'Not list'} records")
                
                # Validate structure for critical collections
                if collection == 'loans' and isinstance(data, list) and len(data) > 0:
                    loan = data[0]
                    required_fields = ['id', 'name', 'principal', 'term_months', 'start_date', 'next_payment_date', 'end_date']
                    missing_fields = [field for field in required_fields if field not in loan]
                    if missing_fields:
                        print(f"⚠️  Missing fields in loans: {missing_fields}")
                    else:
                        print(f"✅ Loan structure valid")
                        
            except Exception as e:
                print(f"❌ {collection}: Error - {e}")
    
    except Exception as e:
        print(f"❌ Data integrity test failed: {e}")
    
    # Test 2: Financial Calculations
    print("\n2️⃣ FINANCIAL CALCULATIONS TEST")
    print("-" * 40)
    
    try:
        loans = storage.get_collection('loans')
        bank_accounts = storage.get_collection('bank_accounts')
        cds = storage.get_collection('cds')
        assets = storage.get_collection('assets')
        liabilities = storage.get_collection('liabilities')
        
        # Test calculations
        total_loan_balance = sum(float(loan.get('remaining_balance', 0)) for loan in loans if isinstance(loan, dict))
        total_bank_balance = sum(float(acc.get('balance', 0)) for acc in bank_accounts if isinstance(acc, dict))
        total_cd_value = sum(float(cd.get('principal', 0)) for cd in cds if isinstance(cd, dict))
        total_assets = sum(float(asset.get('value', 0)) for asset in assets if isinstance(asset, dict))
        total_liabilities = sum(float(liability.get('amount', 0)) for liability in liabilities if isinstance(liability, dict))
        
        print(f"💰 Total Loan Balance: {total_loan_balance:,.2f}")
        print(f"🏦 Total Bank Balance: {total_bank_balance:,.2f}")
        print(f"💿 Total CD Value: {total_cd_value:,.2f}")
        print(f"📊 Total Assets: {total_assets:,.2f}")
        print(f"💸 Total Liabilities: {total_liabilities:,.2f}")
        print(f"💎 Net Worth: {total_assets - total_liabilities:,.2f}")
        print(f"💵 Liquid Assets: {total_bank_balance + total_cd_value:,.2f}")
        
        # Validate calculations make sense
        if total_loan_balance < 0:
            print("⚠️  Warning: Negative loan balance detected")
        if total_assets < total_bank_balance + total_cd_value:
            print("⚠️  Warning: Assets less than liquid assets")
        if total_assets > 0 and total_liabilities > 0:
            print("✅ Financial calculations appear valid")
            
    except Exception as e:
        print(f"❌ Financial calculations failed: {e}")
    
    # Test 3: Date Consistency
    print("\n3️⃣ DATE CONSISTENCY TEST")
    print("-" * 40)
    
    try:
        loans = storage.get_collection('loans')
        from datetime import datetime
        
        for loan in loans:
            if isinstance(loan, dict):
                name = loan.get('name', 'Unknown')
                start_date = loan.get('start_date')
                next_payment = loan.get('next_payment_date')
                end_date = loan.get('end_date')
                
                # Basic date format validation
                try:
                    if start_date and start_date != 'N/A':
                        datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                    if next_payment and next_payment != 'N/A':
                        datetime.fromisoformat(next_payment.replace('Z', '+00:00'))
                    if end_date and end_date != 'N/A':
                        datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                    print(f"✅ {name}: Date formats valid")
                except Exception as date_error:
                    print(f"⚠️  {name}: Date format issue - {date_error}")
                    
    except Exception as e:
        print(f"❌ Date consistency test failed: {e}")
    
    # Test 4: Data Relationships
    print("\n4️⃣ DATA RELATIONSHIPS TEST")
    print("-" * 40)
    
    try:
        loans = storage.get_collection('loans')
        paid_months = storage.get_collection('paid_months')
        
        # Check if paid months reference valid loans
        valid_loan_ids = {loan.get('id') for loan in loans if isinstance(loan, dict) and loan.get('id')}
        
        orphaned_payments = []
        for payment in paid_months:
            if isinstance(payment, dict):
                loan_id = payment.get('loan_id')
                if loan_id and loan_id not in valid_loan_ids:
                    orphaned_payments.append(payment)
        
        if orphaned_payments:
            print(f"⚠️  Found {len(orphaned_payments)} orphaned payment records")
        else:
            print("✅ Payment records properly linked to loans")
            
        print(f"📊 {len(valid_loan_ids)} active loans, {len(paid_months)} payment records")
        
    except Exception as e:
        print(f"❌ Data relationships test failed: {e}")
    
    print("\n🎯 TESTING COMPLETE")
    print("=" * 60)

if __name__ == '__main__':
    test_all_features()
