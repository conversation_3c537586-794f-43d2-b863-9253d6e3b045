#!/bin/bash

echo "🔄 Financial Advisor - Windows to Mac Data Migration"
echo "=================================================="
echo

# Simple approach - check Desktop first
DESKTOP_PATH="$HOME/Desktop/.financial_advisor"
MAC_DATA_PATH="$HOME/.financial_advisor"

if [ ! -d "$DESKTOP_PATH" ]; then
    echo "❌ Please move your Windows .financial_advisor folder to Desktop first!"
    echo ""
    echo "📋 Steps:"
    echo "1. Open Finder"
    echo "2. Go to Downloads folder"
    echo "3. Find your .financial_advisor folder"
    echo "4. Drag it to Desktop"
    echo "5. Run this script again"
    echo ""
    exit 1
fi

echo "✅ Found Windows data at: $DESKTOP_PATH"

# Show what's in the Windows data
echo
echo "📁 Windows data contents:"
ls -la "$DESKTOP_PATH" | head -10

# Backup current Mac data
if [ -d "$MAC_DATA_PATH" ]; then
    echo
    echo "💾 Backing up current Mac data..."
    cp -r "$MAC_DATA_PATH" "${MAC_DATA_PATH}_backup_$(date +%Y%m%d_%H%M%S)"
    echo "✅ Backup created"
fi

# Migrate the data
echo
echo "🔄 Migrating Windows data to Mac..."
rm -rf "$MAC_DATA_PATH"
cp -r "$DESKTOP_PATH" "$MAC_DATA_PATH"

# Fix permissions
echo "🔧 Fixing permissions..."
chmod -R 755 "$MAC_DATA_PATH"

# Verify migration
echo
echo "✅ Migration completed!"
echo "📁 Mac data location: $MAC_DATA_PATH"
echo "📊 Data contents:"
ls -la "$MAC_DATA_PATH" | head -10

echo
echo "🎉 Your Windows financial data is now ready on Mac!"
echo "You can now start the app with: python3 run-simplified-app.py"
