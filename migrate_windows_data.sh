#!/bin/bash

echo "🔄 Financial Advisor - Windows to Mac Data Migration"
echo "=================================================="
echo

# Check if Windows data exists
WINDOWS_DATA_LOCATIONS=(
    "$HOME/Desktop/.financial_advisor"
    "$HOME/Downloads/.financial_advisor"
    "$HOME/Documents/.financial_advisor"
    "./.financial_advisor"
)

WINDOWS_DATA_PATH=""
for location in "${WINDOWS_DATA_LOCATIONS[@]}"; do
    if [ -d "$location" ]; then
        WINDOWS_DATA_PATH="$location"
        echo "✅ Found Windows data at: $WINDOWS_DATA_PATH"
        break
    fi
done

if [ -z "$WINDOWS_DATA_PATH" ]; then
    echo "❌ Windows .financial_advisor folder not found!"
    echo "Please place your Windows .financial_advisor folder in one of these locations:"
    echo "  - ~/Desktop/.financial_advisor"
    echo "  - ~/Downloads/.financial_advisor"
    echo "  - ~/Documents/.financial_advisor"
    echo "  - Current directory: ./.financial_advisor"
    exit 1
fi

# Show what's in the Windows data
echo
echo "📁 Windows data contents:"
ls -la "$WINDOWS_DATA_PATH" | head -10

# Backup current Mac data
MAC_DATA_PATH="/Users/<USER>/.financial_advisor"
if [ -d "$MAC_DATA_PATH" ]; then
    echo
    echo "💾 Backing up current Mac data..."
    cp -r "$MAC_DATA_PATH" "${MAC_DATA_PATH}_backup_$(date +%Y%m%d_%H%M%S)"
    echo "✅ Backup created"
fi

# Migrate the data
echo
echo "🔄 Migrating Windows data to Mac..."
rm -rf "$MAC_DATA_PATH"
cp -r "$WINDOWS_DATA_PATH" "$MAC_DATA_PATH"

# Fix permissions
echo "🔧 Fixing permissions..."
chmod -R 755 "$MAC_DATA_PATH"

# Verify migration
echo
echo "✅ Migration completed!"
echo "📁 Mac data location: $MAC_DATA_PATH"
echo "📊 Data contents:"
ls -la "$MAC_DATA_PATH" | head -10

echo
echo "🎉 Your Windows financial data is now ready on Mac!"
echo "You can now start the app with: python3 run-simplified-app.py"
