#!/usr/bin/env python3

import os
import re

def performance_audit():
    """Audit performance issues in large components"""
    
    print("⚡ PERFORMANCE AUDIT")
    print("=" * 50)
    
    # Large pages that need review
    large_pages = [
        'src/pages/Calendar.tsx',
        'src/pages/CashFlowAnalysis.tsx'
    ]
    
    for page_path in large_pages:
        if os.path.exists(page_path):
            page_name = os.path.basename(page_path).replace('.tsx', '')
            file_size = os.path.getsize(page_path)
            
            print(f"\n📊 {page_name} ({file_size:,} bytes)")
            print("-" * 40)
            
            with open(page_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Count potential performance issues
            use_effect_count = len(re.findall(r'useEffect\s*\(', content))
            use_state_count = len(re.findall(r'useState\s*\(', content))
            map_operations = len(re.findall(r'\.map\s*\(', content))
            filter_operations = len(re.findall(r'\.filter\s*\(', content))
            nested_operations = len(re.findall(r'\.map\s*\([^}]+\.map\s*\(', content))
            
            # Check for memoization
            use_memo_count = len(re.findall(r'useMemo\s*\(', content))
            use_callback_count = len(re.findall(r'useCallback\s*\(', content))
            react_memo_count = len(re.findall(r'React\.memo\s*\(', content))
            
            # Check for expensive operations
            json_parse_count = len(re.findall(r'JSON\.parse\s*\(', content))
            date_operations = len(re.findall(r'new Date\s*\(', content))
            sort_operations = len(re.findall(r'\.sort\s*\(', content))
            
            print(f"🔄 React Hooks:")
            print(f"  • useEffect: {use_effect_count}")
            print(f"  • useState: {use_state_count}")
            
            print(f"📋 Array Operations:")
            print(f"  • .map(): {map_operations}")
            print(f"  • .filter(): {filter_operations}")
            print(f"  • Nested operations: {nested_operations}")
            
            print(f"⚡ Performance Optimizations:")
            print(f"  • useMemo: {use_memo_count}")
            print(f"  • useCallback: {use_callback_count}")
            print(f"  • React.memo: {react_memo_count}")
            
            print(f"💰 Expensive Operations:")
            print(f"  • JSON.parse: {json_parse_count}")
            print(f"  • new Date(): {date_operations}")
            print(f"  • .sort(): {sort_operations}")
            
            # Performance warnings
            warnings = []
            if use_effect_count > 5:
                warnings.append(f"Many useEffect hooks ({use_effect_count}) - consider consolidation")
            if nested_operations > 0:
                warnings.append(f"Nested array operations detected - performance risk")
            if map_operations > 10 and use_memo_count == 0:
                warnings.append("Many array operations without memoization")
            if date_operations > 20:
                warnings.append(f"Many date operations ({date_operations}) - consider caching")
            if file_size > 80000:
                warnings.append("Very large component - consider splitting")
            
            if warnings:
                print(f"⚠️  Performance Warnings:")
                for warning in warnings:
                    print(f"   • {warning}")
            else:
                print("✅ No major performance issues detected")
    
    print(f"\n🎯 PERFORMANCE AUDIT COMPLETE")

if __name__ == '__main__':
    performance_audit()
