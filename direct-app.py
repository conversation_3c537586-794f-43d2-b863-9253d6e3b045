import os
import sys
import webview
import traceback
from data_storage import DataStorage

# Constants
APP_NAME = "Financial Advisor"
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
DEBUG_MODE = True

def on_loaded(window):
    """Called when the webpage has loaded"""
    print("WebView loaded - page is ready")
    
    # First, ensure the PyWebView API is properly exposed to the frontend
    try:
        if hasattr(window, 'evaluate_js'):
            # This is critical - we need to make sure the API is properly exposed
            window.evaluate_js("""
                // Create a proper API bridge
                window.pywebview = {
                    api: {
                        // Expose all the methods from the Python backend
                        get_item: function(key) {
                            return window.pywebview_channel.call('get_item', key);
                        },
                        set_item: function(key, value) {
                            return window.pywebview_channel.call('set_item', key, value);
                        },
                        remove_item: function(key) {
                            return window.pywebview_channel.call('remove_item', key);
                        },
                        get_all_keys: function() {
                            return window.pywebview_channel.call('get_all_keys');
                        },
                        dump_storage: function() {
                            return window.pywebview_channel.call('dump_storage');
                        },
                        clean_indexes: function() {
                            return window.pywebview_channel.call('clean_indexes');
                        },
                        repair_storage: function() {
                            return window.pywebview_channel.call('repair_storage');
                        },
                        get_storage_stats: function() {
                            return window.pywebview_channel.call('get_storage_stats');
                        },
                        get_collection: function(collection) {
                            return window.pywebview_channel.call('get_collection', collection);
                        }
                    }
                };
                
                // Set the environment variable properly
                window.USE_LOCAL_STORE = true;
                
                console.log('PyWebView API bridge created and configured');
                console.log('USE_LOCAL_STORE set to:', window.USE_LOCAL_STORE);
            """)
            print("PyWebView API bridge created")
            
            # Now run the storage maintenance
            window.evaluate_js("""
                console.log('Running storage maintenance...');
                
                // Run clean_indexes
                window.pywebview.api.clean_indexes().then(result => {
                    console.log('Storage index cleanup result:', result);
                }).catch(err => {
                    console.error('Error cleaning indexes:', err);
                });
                
                // Run repair_storage
                window.pywebview.api.repair_storage().then(result => {
                    console.log('Storage repair result:', result);
                }).catch(err => {
                    console.error('Error repairing storage:', err);
                });
            """)
            print("Storage maintenance scheduled")
            
            # Add a direct UI that doesn't depend on React
            window.evaluate_js("""
                // Create a direct UI that doesn't depend on React
                document.body.innerHTML = `
                    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100vh; color: white; font-family: Arial, sans-serif; background-color: #0f172a;">
                        <h1 style="font-size: 28px; margin-bottom: 20px;">Financial Advisor</h1>
                        <div style="background-color: #1e293b; padding: 20px; border-radius: 8px; width: 80%; max-width: 800px;">
                            <h2 style="font-size: 20px; margin-bottom: 15px;">Financial Overview</h2>
                            
                            <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; margin-bottom: 20px;">
                                <div style="background-color: #334155; padding: 15px; border-radius: 6px;">
                                    <h3 style="font-size: 16px; margin-bottom: 10px;">Bank Accounts</h3>
                                    <div id="bank-accounts">Loading...</div>
                                </div>
                                
                                <div style="background-color: #334155; padding: 15px; border-radius: 6px;">
                                    <h3 style="font-size: 16px; margin-bottom: 10px;">CDs</h3>
                                    <div id="cds">Loading...</div>
                                </div>
                                
                                <div style="background-color: #334155; padding: 15px; border-radius: 6px;">
                                    <h3 style="font-size: 16px; margin-bottom: 10px;">Loans</h3>
                                    <div id="loans">Loading...</div>
                                </div>
                            </div>
                            
                            <div style="margin-top: 20px;">
                                <button id="reload-btn" style="background-color: #3b82f6; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer;">Reload Data</button>
                                <button id="test-storage-btn" style="background-color: #10b981; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; margin-left: 10px;">Test Storage</button>
                            </div>
                            <div id="test-result" style="margin-top: 15px; font-size: 14px;"></div>
                        </div>
                    </div>
                `;
                
                // Function to load and display data
                async function loadData() {
                    try {
                        // Load bank accounts
                        const bankAccountsJson = await window.pywebview.api.get_collection('bank_accounts');
                        const bankAccounts = JSON.parse(bankAccountsJson || '[]');
                        
                        let bankAccountsHtml = '';
                        if (bankAccounts.length > 0) {
                            bankAccountsHtml = bankAccounts.map(account => `
                                <div style="margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #475569;">
                                    <div style="font-weight: bold;">${account.name}</div>
                                    <div>Balance: $${account.balance.toFixed(2)}</div>
                                    <div>Interest: ${account.interest_rate}%</div>
                                </div>
                            `).join('');
                        } else {
                            bankAccountsHtml = '<p>No bank accounts found.</p>';
                        }
                        document.getElementById('bank-accounts').innerHTML = bankAccountsHtml;
                        
                        // Load CDs
                        const cdsJson = await window.pywebview.api.get_collection('cds');
                        const cds = JSON.parse(cdsJson || '[]');
                        
                        let cdsHtml = '';
                        if (cds.length > 0) {
                            cdsHtml = cds.map(cd => `
                                <div style="margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #475569;">
                                    <div style="font-weight: bold;">${cd.name}</div>
                                    <div>Principal: $${cd.principal.toFixed(2)}</div>
                                    <div>Interest: ${cd.interest_rate}%</div>
                                    <div>Term: ${cd.term_months} months</div>
                                </div>
                            `).join('');
                        } else {
                            cdsHtml = '<p>No CDs found.</p>';
                        }
                        document.getElementById('cds').innerHTML = cdsHtml;
                        
                        // Load loans
                        const loansJson = await window.pywebview.api.get_collection('loans');
                        const loans = JSON.parse(loansJson || '[]');
                        
                        let loansHtml = '';
                        if (loans.length > 0) {
                            loansHtml = loans.map(loan => `
                                <div style="margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #475569;">
                                    <div style="font-weight: bold;">${loan.name}</div>
                                    <div>Balance: $${loan.balance.toFixed(2)}</div>
                                    <div>Interest: ${loan.interest_rate}%</div>
                                    <div>Payment: $${loan.monthly_payment.toFixed(2)}/mo</div>
                                </div>
                            `).join('');
                        } else {
                            loansHtml = '<p>No loans found.</p>';
                        }
                        document.getElementById('loans').innerHTML = loansHtml;
                        
                    } catch (error) {
                        console.error('Error loading data:', error);
                        document.getElementById('test-result').innerHTML = `Error loading data: ${error.message}`;
                    }
                }
                
                // Load data immediately
                loadData();
                
                // Add event listeners to buttons
                document.getElementById('reload-btn').addEventListener('click', loadData);
                
                document.getElementById('test-storage-btn').addEventListener('click', async () => {
                    const resultDiv = document.getElementById('test-result');
                    resultDiv.innerHTML = 'Testing storage...';
                    
                    try {
                        // Test with a simple key-value pair
                        const testKey = 'test-key-' + Date.now();
                        const testValue = JSON.stringify({ 
                            test: 'data', 
                            timestamp: Date.now(),
                            message: 'Storage test from direct UI'
                        });
                        
                        // Save the data
                        const saveResult = await window.pywebview.api.set_item(testKey, testValue);
                        
                        // Load the data back
                        const loadResult = await window.pywebview.api.get_item(testKey);
                        
                        resultDiv.innerHTML = `
                            <div style="color: #10b981;">Storage test successful!</div>
                            <div>Data was saved and retrieved.</div>
                        `;
                    } catch (error) {
                        resultDiv.innerHTML = `
                            <div style="color: #ef4444;">Storage test failed:</div>
                            <div>${error.message}</div>
                        `;
                    }
                });
            """)
            print("Direct UI created")
        else:
            print("Window does not support evaluate_js, skipping API setup")
    except Exception as e:
        print(f"Error setting up PyWebView API: {e}")
        traceback.print_exc()

def main():
    """Launch the direct application"""
    print(f"Starting {APP_NAME}...")
    print(f"Storage location: {os.path.join(os.path.expanduser('~'), '.financial_advisor')}")
    
    try:
        # Initialize the data storage
        storage = DataStorage()
        
        # Set environment variables
        os.environ["USE_LOCAL_STORE"] = "true"
        
        # Create a simple HTML file
        html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Advisor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #0f172a;
            color: white;
        }
        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #3b82f6;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="loading">
        <div class="spinner"></div>
        <div>Loading Financial Advisor...</div>
    </div>
</body>
</html>
"""
        
        # Save the HTML to a temporary file
        html_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'direct-app.html')
        with open(html_path, 'w') as f:
            f.write(html_content)
        
        # Create the URL for the local file
        url = f"file://{html_path}"
        print(f"Loading app from: {url}")
        
        # Create the window with storage API exposed
        window = webview.create_window(
            title=APP_NAME,
            url=url,
            width=WINDOW_WIDTH,
            height=WINDOW_HEIGHT,
            min_size=(800, 600),
            js_api=storage,
            text_select=True
        )
        
        # Register the loaded event handler
        window.events.loaded += on_loaded
        
        # Start the window
        webview.start(debug=DEBUG_MODE)
        
    except Exception as e:
        print(f"Error in direct app: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
