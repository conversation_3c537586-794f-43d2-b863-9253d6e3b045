import webview
import os
import sys
import subprocess
import time
import signal
import socket
from http.client import HTTPConnection

"""
Universal Web App to Desktop App Converter
-----------------------------------------
This script converts any web application (React, Vue, Angular, etc.)
into a native desktop application using PyWebView.

How to use:
1. Copy this file to your web application directory
2. Adjust the configuration variables below if needed
3. Run with Python: python desktop_app.py

Requirements:
- Python 3.7+
- PyWebView (install with: pip install pywebview)
- npm (for running the web application)
"""

#############################################
# CONFIGURATION - Edit these settings as needed
#############################################

# App configuration
APP_NAME = "AI Advisor Pro"  # Name of your application window
WINDOW_WIDTH = 1200          # Initial window width
WINDOW_HEIGHT = 800          # Initial window height

# Development server configuration
START_COMMAND = "npm run dev"  # Command to start your dev server
DEFAULT_PORT = 5173            # Default port for Vite (change for other frameworks)
HOST = "localhost"             # Host the dev server runs on
MAX_STARTUP_TIME = 60          # Maximum time to wait for server startup (seconds)

# Advanced options (most users won't need to change these)
DEBUG_MODE = False             # Set to True to see debug information
FRAMELESS = False              # Set to True for a frameless window
FORCE_PORT = None              # Set to a specific port to force usage (e.g. 3000)
CUSTOM_USER_AGENT = None       # Set a custom user agent

#############################################
# APPLICATION CODE - No need to edit below this line
#############################################

# Global variable to store server process
server_process = None

def is_port_in_use(port):
    """Check if a port is already in use"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex((HOST, port)) == 0
        
def find_available_port():
    """Find an available port starting from the default port"""
    if FORCE_PORT is not None:
        return FORCE_PORT
        
    port = DEFAULT_PORT
    while is_port_in_use(port):
        port += 1
        if port > DEFAULT_PORT + 100:  # Avoid endless loop
            raise Exception(f"No available ports found in range {DEFAULT_PORT}-{DEFAULT_PORT+100}")
    return port

def wait_for_server(host, port, timeout=MAX_STARTUP_TIME):
    """Wait for the development server to become available"""
    print(f"Waiting for server to start on {host}:{port}...")
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            conn = HTTPConnection(host, port, timeout=1)
            conn.request("HEAD", "/")
            response = conn.getresponse()
            print(f"Server started successfully on {host}:{port}")
            return True
        except Exception as e:
            if DEBUG_MODE:
                print(f"Still waiting for server: {e}")
            time.sleep(0.5)
    return False

def run_dev_server():
    """Run the web development server in a separate process"""
    global server_process
    
    try:
        # Find an available port
        port = find_available_port()
        print(f"Using port: {port}")
        
        # Prepare the command with port specification
        command = START_COMMAND
        if "dev" in command and "--port" not in command:
            command = f"{command} --port {port}"
        
        # Set environment variables for the dev server
        env = os.environ.copy()
        env["PORT"] = str(port)
        
        # Start the dev server
        if sys.platform == 'win32':
            server_process = subprocess.Popen(
                command,
                cwd=os.path.dirname(os.path.abspath(__file__)),
                env=env,
                shell=True,
                stdout=subprocess.PIPE if not DEBUG_MODE else None,
                stderr=subprocess.PIPE if not DEBUG_MODE else None,
                text=True
            )
        else:
            server_process = subprocess.Popen(
                command.split(),
                cwd=os.path.dirname(os.path.abspath(__file__)),
                env=env,
                stdout=subprocess.PIPE if not DEBUG_MODE else None,
                stderr=subprocess.PIPE if not DEBUG_MODE else None,
                text=True
            )
        
        # Wait for server to start
        if wait_for_server(HOST, port):
            return f"http://{HOST}:{port}"
        else:
            raise Exception(f"Development server failed to start within {MAX_STARTUP_TIME} seconds")
            
    except Exception as e:
        print(f"Error starting development server: {e}")
        sys.exit(1)

def cleanup():
    """Clean up resources when app exits"""
    global server_process
    
    if server_process:
        print("Shutting down development server...")
        try:
            if sys.platform == 'win32':
                subprocess.call(['taskkill', '/F', '/T', '/PID', str(server_process.pid)])
            else:
                os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
            print("Server process terminated")
        except Exception as e:
            print(f"Error shutting down server: {e}")

def launch_app():
    """Launch the desktop application"""
    print(f"Starting {APP_NAME} desktop application...")
    
    try:
        # Start the dev server
        url = run_dev_server()
        
        # Create window options
        window_options = {
            'title': APP_NAME,
            'width': WINDOW_WIDTH,
            'height': WINDOW_HEIGHT,
            'min_size': (800, 600),
            'text_select': True,
            'frameless': FRAMELESS
        }
        
        # Some PyWebView versions support easy_drag, but not all
        if FRAMELESS:
            try:
                window_options['easy_drag'] = True
            except:
                pass
        
        # Register window closing event handler - for PyWebView 3.x
        try:
            webview.events.closed += cleanup
        except:
            pass
            
        # Create the window
        window = webview.create_window(title=APP_NAME, url=url, **window_options)
        
        # Start the window - this will block until window is closed
        webview.start(debug=DEBUG_MODE)
        
    except Exception as e:
        print(f"Error in desktop app: {e}")
        
    finally:
        # Always cleanup when done
        cleanup()

if __name__ == "__main__":
    launch_app()