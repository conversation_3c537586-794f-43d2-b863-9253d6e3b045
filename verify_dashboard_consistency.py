#!/usr/bin/env python3

"""
<PERSON><PERSON>t to verify that Dashboard calculations are consistent with Net Worth page.
Both should now show the same values for Assets table, Liabilities table, and Net Worth.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_storage import DataStorage

def main():
    print("=== Dashboard vs Net Worth Consistency Check ===\n")
    
    storage = DataStorage()
    
    # Get data from all relevant collections
    bank_accounts = storage.get_collection('bank_accounts')
    cds = storage.get_collection('cds')
    loans = storage.get_collection('loans')
    assets = storage.get_collection('assets')
    liabilities = storage.get_collection('liabilities')
    
    # Dashboard calculations (as per new implementation)
    total_bank_balance = sum(acc.get('balance', 0) for acc in bank_accounts)
    total_cd_value = sum(cd.get('principal', 0) for cd in cds)
    liquid_assets = total_bank_balance + total_cd_value
    
    assets_value = sum(asset.get('value', 0) for asset in assets)
    liabilities_value = sum(liability.get('amount', 0) for liability in liabilities)
    net_worth = assets_value - liabilities_value
    
    # Net Worth page calculations (for comparison)
    nw_total_assets = assets_value  # Same as Dashboard's assetsValue
    nw_total_liabilities = liabilities_value  # Same as Dashboard's liabilitiesValue
    nw_net_worth = nw_total_assets - nw_total_liabilities
    
    # Loan balance (for reference, no longer used in liabilities card)
    total_loan_balance = sum(loan.get('remaining_balance', 0) for loan in loans)
    
    print("DASHBOARD VALUES:")
    print(f"  Net Worth: ${net_worth:,.2f} (Assets table - Liabilities table)")
    print(f"  Liquid Assets: ${liquid_assets:,.2f} (Bank Accounts + CDs)")
    print(f"  Total Liabilities: ${liabilities_value:,.2f} (From Liabilities table)")
    print()
    
    print("NET WORTH PAGE VALUES:")
    print(f"  Total Assets: ${nw_total_assets:,.2f}")
    print(f"  Total Liabilities: ${nw_total_liabilities:,.2f}")
    print(f"  Net Worth: ${nw_net_worth:,.2f}")
    print()
    
    print("ADDITIONAL INFO:")
    print(f"  Bank Accounts: ${total_bank_balance:,.2f}")
    print(f"  CDs: ${total_cd_value:,.2f}")
    print(f"  Loan Balances: ${total_loan_balance:,.2f} (not used in liabilities card anymore)")
    print()
    
    # Check consistency
    consistent = (
        net_worth == nw_net_worth and
        assets_value == nw_total_assets and
        liabilities_value == nw_total_liabilities
    )
    
    if consistent:
        print("✅ DASHBOARD AND NET WORTH PAGE ARE NOW CONSISTENT!")
        print("   - Both use Assets table for total assets")
        print("   - Both use Liabilities table for total liabilities")
        print("   - Both calculate Net Worth = Assets - Liabilities")
    else:
        print("❌ INCONSISTENCY DETECTED!")
        print(f"   Net Worth: Dashboard={net_worth}, NetWorth={nw_net_worth}")
        print(f"   Assets: Dashboard={assets_value}, NetWorth={nw_total_assets}")
        print(f"   Liabilities: Dashboard={liabilities_value}, NetWorth={nw_total_liabilities}")
    
    print()
    print("DASHBOARD CARD LABELS:")
    print('  - "Net Worth": Assets table - Liabilities table')
    print('  - "Liquid Assets": Bank Accounts + CDs')
    print('  - "Total Liabilities": From Liabilities table')
    print()
    print("This ensures clear, consistent terminology throughout the app.")

if __name__ == "__main__":
    main()
