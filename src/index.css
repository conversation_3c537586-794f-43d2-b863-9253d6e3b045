@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-50 dark:bg-dark-900 text-gray-900 dark:text-gray-100;
  }

  input, select, textarea {
    @apply bg-white dark:bg-dark-800 text-gray-900 dark:text-white border-gray-300 dark:border-dark-600;
  }

  input:focus, select:focus, textarea:focus {
    @apply ring-indigo-500 dark:ring-indigo-400 border-indigo-500 dark:border-indigo-400;
  }

  button {
    @apply text-gray-700 dark:text-gray-300;
  }

  button.primary {
    @apply bg-indigo-600 dark:bg-indigo-500 text-white hover:bg-indigo-700 dark:hover:bg-indigo-600;
  }

  button.secondary {
    @apply bg-white dark:bg-dark-800 border border-gray-300 dark:border-dark-600 hover:bg-gray-50 dark:hover:bg-dark-700;
  }
}

/* Accessibility Utilities */
@layer utilities {
  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Show screen reader content when focused */
  .sr-only:focus,
  .focus-within\:not-sr-only:focus-within .sr-only {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
  }

  /* Skip links */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    z-index: 1000;
    padding: 8px;
    background: theme('colors.blue.600');
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: top 0.3s;
  }

  .skip-link:focus {
    top: 6px;
  }

  /* Focus indicators */
  .focus-visible:focus-visible {
    outline: 2px solid theme('colors.indigo.500');
    outline-offset: 2px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .high-contrast {
      border: 2px solid;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .motion-reduce {
      animation: none;
      transition: none;
    }
  }

  /* Focus management */
  .focus-trap:focus {
    outline: none;
  }
}

/* Form Accessibility Styles */
@layer components {
  .form-group {
    @apply mb-4;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
  }

  .form-label.required::after {
    content: ' *';
    @apply text-red-500;
  }

  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md shadow-sm
           placeholder-gray-400 dark:placeholder-gray-500 
           focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500
           dark:focus:ring-indigo-400 dark:focus:border-indigo-400
           bg-white dark:bg-dark-800 text-gray-900 dark:text-white;
  }

  .form-input.error {
    @apply border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500
           dark:focus:ring-red-400 dark:focus:border-red-400;
  }

  .form-select {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md shadow-sm
           focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500
           dark:focus:ring-indigo-400 dark:focus:border-indigo-400
           bg-white dark:bg-dark-800 text-gray-900 dark:text-white;
  }

  .form-select.error {
    @apply border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500
           dark:focus:ring-red-400 dark:focus:border-red-400;
  }

  .form-help-text {
    @apply mt-2 text-sm text-gray-500 dark:text-gray-400;
  }

  .form-error-text {
    @apply mt-2 text-sm text-red-600 dark:text-red-400;
  }

  .required-indicator {
    @apply text-red-500 ml-1;
  }
}

/* Button Accessibility Styles */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md
           focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-indigo-600 hover:bg-indigo-700 text-white focus:ring-indigo-500
           dark:bg-indigo-500 dark:hover:bg-indigo-600 dark:focus:ring-indigo-400;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-gray-700 border-gray-300 focus:ring-indigo-500
           dark:bg-dark-800 dark:hover:bg-dark-700 dark:text-gray-300 dark:border-dark-600 dark:focus:ring-indigo-400;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white focus:ring-red-500
           dark:bg-red-500 dark:hover:bg-red-600 dark:focus:ring-red-400;
  }

  .btn-ghost {
    @apply bg-transparent hover:bg-gray-100 text-gray-600 hover:text-gray-800 focus:ring-gray-500
           dark:hover:bg-dark-700 dark:text-gray-400 dark:hover:text-gray-200 dark:focus:ring-gray-400;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-md {
    @apply px-4 py-2 text-sm;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  .btn.loading {
    @apply cursor-wait;
  }

  .spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2;
  }
}

/* Calendar Dark Mode */
.dark .fc {
  --fc-border-color: theme('colors.dark.700');
  --fc-page-bg-color: theme('colors.dark.800');
  --fc-neutral-bg-color: theme('colors.dark.700');
  --fc-list-event-hover-bg-color: theme('colors.dark.700');
  --fc-today-bg-color: theme('colors.indigo.900' / 0.3);
}

.dark .fc-theme-standard td,
.dark .fc-theme-standard th {
  @apply border-dark-700;
}

.dark .fc-theme-standard .fc-scrollgrid {
  @apply border-dark-700;
}

.dark .fc .fc-button {
  @apply bg-dark-700 border-dark-600 hover:bg-dark-600;
}

.dark .fc .fc-button-primary:not(:disabled).fc-button-active,
.dark .fc .fc-button-primary:not(:disabled):active {
  @apply bg-indigo-600 border-indigo-700;
}

.dark .fc-daygrid-day-number,
.dark .fc-col-header-cell-cushion {
  @apply text-gray-300;
}