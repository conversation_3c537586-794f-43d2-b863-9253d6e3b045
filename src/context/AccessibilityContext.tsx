import React, { createContext, useContext, useRef, useCallback } from 'react'

interface AccessibilityContextType {
  announceToScreenReader: (message: string, priority?: 'polite' | 'assertive') => void
  setFocusToElement: (elementId: string) => void
  setFocusToRef: (ref: React.RefObject<HTMLElement>) => void
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined)

export const useAccessibility = () => {
  const context = useContext(AccessibilityContext)
  if (!context) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider')
  }
  return context
}

interface AccessibilityProviderProps {
  children: React.ReactNode
}

export const AccessibilityProvider: React.FC<AccessibilityProviderProps> = ({ children }) => {
  const liveRegionRef = useRef<HTMLDivElement>(null)

  const announceToScreenReader = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (liveRegionRef.current) {
      // Clear previous message
      liveRegionRef.current.textContent = ''
      
      // Set new message after a brief delay to ensure screen readers pick it up
      setTimeout(() => {
        if (liveRegionRef.current) {
          liveRegionRef.current.textContent = message
          liveRegionRef.current.setAttribute('aria-live', priority)
        }
      }, 100)
    }
  }, [])

  const setFocusToElement = useCallback((elementId: string) => {
    const element = document.getElementById(elementId)
    if (element) {
      element.focus()
    }
  }, [])

  const setFocusToRef = useCallback((ref: React.RefObject<HTMLElement>) => {
    if (ref.current) {
      ref.current.focus()
    }
  }, [])

  const value: AccessibilityContextType = {
    announceToScreenReader,
    setFocusToElement,
    setFocusToRef,
  }

  return (
    <AccessibilityContext.Provider value={value}>
      {children}
      {/* Screen reader live region for announcements */}
      <div
        ref={liveRegionRef}
        className="sr-only"
        aria-live="polite"
        aria-atomic="true"
        role="status"
      />
    </AccessibilityContext.Provider>
  )
}

export default AccessibilityProvider
