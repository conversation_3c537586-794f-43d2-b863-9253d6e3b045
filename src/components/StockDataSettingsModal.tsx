import { useState } from 'react';
import { X, Settings, AlertCircle, ExternalLink, Key } from 'lucide-react';
import { stockDataService } from '../services/stockDataService';

interface StockDataSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

export function StockDataSettingsModal({ isOpen, onClose, onSave }: StockDataSettingsModalProps) {
  const [provider, setProvider] = useState<'mock' | 'manual' | 'alphavantage' | 'yahoo' | 'finnhub'>('mock');
  const [apiKey, setApiKey] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  if (!isOpen) return null;

  const handleSave = async () => {
    setIsSaving(true);
    try {
      if (provider !== 'mock' && provider !== 'manual' && apiKey.trim()) {
        stockDataService.configureAPI(provider, apiKey.trim());
      }
      onSave();
      onClose();
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Error saving settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const addManualStock = () => {
    const symbol = prompt('Enter stock symbol (e.g., COMI):');
    const price = prompt('Enter current price:');
    
    if (symbol && price) {
      const numPrice = parseFloat(price);
      if (!isNaN(numPrice)) {
        stockDataService.addManualStock(symbol.toUpperCase(), {
          symbol: symbol.toUpperCase(),
          price: numPrice,
          change: 0,
          change_percentage: 0,
          volume: 0
        });
        alert(`Added ${symbol.toUpperCase()} at ${numPrice}`);
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <Settings className="w-5 h-5 text-blue-600 mr-3" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Stock Data Settings
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Current Status */}
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Current Data Source</h3>
            <p className="text-blue-800 dark:text-blue-200">
              {stockDataService.getDataSource()}
            </p>
          </div>

          {/* Data Source Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Choose Data Source
            </label>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="mock"
                  checked={provider === 'mock'}
                  onChange={(e) => setProvider(e.target.value as any)}
                  className="mr-3"
                />
                <div>
                  <div className="font-medium">Mock Data (Default)</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Use sample data for demonstration purposes
                  </div>
                </div>
              </label>

              <label className="flex items-center">
                <input
                  type="radio"
                  value="manual"
                  checked={provider === 'manual'}
                  onChange={(e) => setProvider(e.target.value as any)}
                  className="mr-3"
                />
                <div>
                  <div className="font-medium">Manual Input</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Add your own stock prices manually
                  </div>
                </div>
              </label>

              <label className="flex items-center">
                <input
                  type="radio"
                  value="alphavantage"
                  checked={provider === 'alphavantage'}
                  onChange={(e) => setProvider(e.target.value as any)}
                  className="mr-3"
                />
                <div>
                  <div className="font-medium">Alpha Vantage API</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Free tier available (500 requests/day)
                    <a href="https://www.alphavantage.co/support/#api-key" target="_blank" rel="noopener noreferrer" className="ml-2 text-blue-600 hover:underline inline-flex items-center">
                      Get API Key <ExternalLink className="w-3 h-3 ml-1" />
                    </a>
                  </div>
                </div>
              </label>

              <label className="flex items-center">
                <input
                  type="radio"
                  value="yahoo"
                  checked={provider === 'yahoo'}
                  onChange={(e) => setProvider(e.target.value as any)}
                  className="mr-3"
                />
                <div>
                  <div className="font-medium">Yahoo Finance (RapidAPI)</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Requires RapidAPI subscription
                    <a href="https://rapidapi.com/apidojo/api/yahoo-finance1/" target="_blank" rel="noopener noreferrer" className="ml-2 text-blue-600 hover:underline inline-flex items-center">
                      Get API Key <ExternalLink className="w-3 h-3 ml-1" />
                    </a>
                  </div>
                </div>
              </label>

              <label className="flex items-center">
                <input
                  type="radio"
                  value="finnhub"
                  checked={provider === 'finnhub'}
                  onChange={(e) => setProvider(e.target.value as any)}
                  className="mr-3"
                />
                <div>
                  <div className="font-medium">Finnhub API</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Free tier available (60 requests/minute)
                    <a href="https://finnhub.io/" target="_blank" rel="noopener noreferrer" className="ml-2 text-blue-600 hover:underline inline-flex items-center">
                      Get API Key <ExternalLink className="w-3 h-3 ml-1" />
                    </a>
                  </div>
                </div>
              </label>
            </div>
          </div>

          {/* API Key Input */}
          {provider !== 'mock' && provider !== 'manual' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                API Key
              </label>
              <div className="relative">
                <input
                  type="password"
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  className="w-full px-3 py-2 pl-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter your API key"
                />
                <Key className="w-4 h-4 text-gray-400 absolute left-3 top-3" />
              </div>
              <div className="flex items-center mt-2 text-sm text-amber-600 dark:text-amber-400">
                <AlertCircle className="w-4 h-4 mr-1" />
                API keys are stored locally and never transmitted to our servers
              </div>
            </div>
          )}

          {/* Manual Stock Entry */}
          {provider === 'manual' && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Add Manual Stock Data</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                You can add stock prices manually. This is useful for private companies or when you want to track specific prices.
              </p>
              <button
                onClick={addManualStock}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm"
              >
                Add Stock Price
              </button>
            </div>
          )}

          {/* Warning for API usage */}
          {provider !== 'mock' && provider !== 'manual' && (
            <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg">
              <div className="flex items-start">
                <AlertCircle className="w-5 h-5 text-amber-600 dark:text-amber-400 mr-3 mt-0.5" />
                <div>
                  <h4 className="font-medium text-amber-800 dark:text-amber-200">Important Notes</h4>
                  <ul className="mt-2 text-sm text-amber-700 dark:text-amber-300 space-y-1">
                    <li>• Real-time data may have usage limits and costs</li>
                    <li>• Some APIs may not support Egyptian stocks (EGX)</li>
                    <li>• API keys are required for most real-time services</li>
                    <li>• Consider rate limits when using the app frequently</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            disabled={isSaving}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {isSaving ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </div>
    </div>
  );
}
