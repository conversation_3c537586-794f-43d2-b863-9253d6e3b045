import React from 'react'

interface AccessibleFormProps {
  children: React.ReactNode
  onSubmit?: (event: React.FormEvent) => void
  className?: string
  'aria-label'?: string
  'aria-labelledby'?: string
  'aria-describedby'?: string
}

/**
 * Accessible Form wrapper component
 * Provides proper ARIA attributes and keyboard navigation
 */
const AccessibleForm: React.FC<AccessibleFormProps> = ({
  children,
  onSubmit,
  className = '',
  'aria-label': ariaLabel,
  'aria-labelledby': ariaLabelledBy,
  'aria-describedby': ariaDescribedBy,
}) => {
  return (
    <form
      onSubmit={onSubmit}
      className={className}
      role="form"
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledBy}
      aria-describedby={ariaDescribedBy}
      noValidate
    >
      {children}
    </form>
  )
}

interface AccessibleInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'className'> {
  label: string
  error?: string
  helpText?: string
  required?: boolean
  hideLabel?: boolean
  className?: string
}

/**
 * Accessible Input component with proper labeling and error handling
 */
const AccessibleInput: React.FC<AccessibleInputProps> = ({
  label,
  error,
  helpText,
  required = false,
  hideLabel = false,
  id,
  className = '',
  ...props
}) => {
  const inputId = id || `input-${label.toLowerCase().replace(/\s+/g, '-')}`
  const errorId = error ? `${inputId}-error` : undefined
  const helpId = helpText ? `${inputId}-help` : undefined
  
  const describedBy = [errorId, helpId].filter(Boolean).join(' ') || undefined

  return (
    <div className={`form-group ${className}`}>
      <label
        htmlFor={inputId}
        className={`form-label ${hideLabel ? 'sr-only' : ''} ${required ? 'required' : ''}`}
      >
        {label}
        {required && <span className="required-indicator" aria-label="required">*</span>}
      </label>
      
      <input
        {...props}
        id={inputId}
        className={`form-input ${error ? 'error' : ''}`}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={describedBy}
        aria-required={required}
      />
      
      {helpText && (
        <div id={helpId} className="form-help-text" role="note">
          {helpText}
        </div>
      )}
      
      {error && (
        <div 
          id={errorId} 
          className="form-error-text" 
          role="alert"
          aria-live="polite"
        >
          {error}
        </div>
      )}
    </div>
  )
}

interface AccessibleSelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'className'> {
  label: string
  error?: string
  helpText?: string
  required?: boolean
  hideLabel?: boolean
  options: Array<{ value: string; label: string; disabled?: boolean }>
  className?: string
}

/**
 * Accessible Select component with proper labeling and error handling
 */
const AccessibleSelect: React.FC<AccessibleSelectProps> = ({
  label,
  error,
  helpText,
  required = false,
  hideLabel = false,
  options,
  id,
  className = '',
  ...props
}) => {
  const selectId = id || `select-${label.toLowerCase().replace(/\s+/g, '-')}`
  const errorId = error ? `${selectId}-error` : undefined
  const helpId = helpText ? `${selectId}-help` : undefined
  
  const describedBy = [errorId, helpId].filter(Boolean).join(' ') || undefined

  return (
    <div className={`form-group ${className}`}>
      <label
        htmlFor={selectId}
        className={`form-label ${hideLabel ? 'sr-only' : ''} ${required ? 'required' : ''}`}
      >
        {label}
        {required && <span className="required-indicator" aria-label="required">*</span>}
      </label>
      
      <select
        {...props}
        id={selectId}
        className={`form-select ${error ? 'error' : ''}`}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={describedBy}
        aria-required={required}
      >
        {options.map((option, index) => (
          <option 
            key={index} 
            value={option.value} 
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
      </select>
      
      {helpText && (
        <div id={helpId} className="form-help-text" role="note">
          {helpText}
        </div>
      )}
      
      {error && (
        <div 
          id={errorId} 
          className="form-error-text" 
          role="alert"
          aria-live="polite"
        >
          {error}
        </div>
      )}
    </div>
  )
}

interface AccessibleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  loadingText?: string
  'aria-label'?: string
  'aria-describedby'?: string
}

/**
 * Accessible Button component with proper ARIA attributes
 */
const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  loadingText = 'Loading...',
  className = '',
  disabled,
  'aria-label': ariaLabel,
  'aria-describedby': ariaDescribedBy,
  ...props
}) => {
  const isDisabled = disabled || loading

  return (
    <button
      {...props}
      className={`btn btn-${variant} btn-${size} ${className} ${loading ? 'loading' : ''}`}
      disabled={isDisabled}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-busy={loading}
    >
      {loading ? (
        <>
          <span className="sr-only">{loadingText}</span>
          <span className="spinner" aria-hidden="true" />
        </>
      ) : (
        children
      )}
    </button>
  )
}

export { AccessibleForm, AccessibleInput, AccessibleSelect, AccessibleButton }
