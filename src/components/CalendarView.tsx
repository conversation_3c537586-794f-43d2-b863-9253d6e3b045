/**
 * CalendarView Component
 * Main calendar display using FullCalendar
 */

import { memo } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { CalendarEvent } from '../types/calendar';

interface CalendarViewProps {
  events: CalendarEvent[];
  onDateClick: (info: any) => void;
  onEventClick: (info: any) => void;
  onEventDrop?: (info: any) => void;
  onEventResize?: (info: any) => void;
  height?: string | number;
  initialView?: string;
  headerToolbar?: {
    left?: string;
    center?: string;
    right?: string;
  };
}

export const CalendarView = memo<CalendarViewProps>(({
  events,
  onDateClick,
  onEventClick,
  onEventDrop,
  onEventResize,
  height = 'auto',
  initialView = 'dayGridMonth',
  headerToolbar = {
    left: 'prev,next today',
    center: 'title',
    right: 'dayGridMonth,timeGridWeek,timeGridDay'
  }
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <FullCalendar
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
        initialView={initialView}
        headerToolbar={headerToolbar}
        height={height}
        events={events}
        dateClick={onDateClick}
        eventClick={onEventClick}
        eventDrop={onEventDrop}
        eventResize={onEventResize}
        editable={true}
        selectable={true}
        selectMirror={true}
        dayMaxEvents={true}
        weekends={true}
        eventDisplay="block"
        eventTextColor="#ffffff"
        eventBackgroundColor="#3b82f6"
        eventBorderColor="#3b82f6"
        themeSystem="standard"
        fixedWeekCount={false}
        showNonCurrentDates={false}
        aspectRatio={1.35}
        eventClassNames={(arg) => {
          const baseClasses = [
            'cursor-pointer',
            'transition-all',
            'duration-200',
            'hover:brightness-110',
            'text-xs',
            'font-medium'
          ];
          
          // Add type-specific classes
          const typeClass = `event-${arg.event.extendedProps.type}`;
          baseClasses.push(typeClass);
          
          // Add custom class if specified
          if (arg.event.classNames) {
            baseClasses.push(...arg.event.classNames);
          }
          
          return baseClasses;
        }}
        eventContent={(arg) => {
          const { event } = arg;
          const { amount, icon } = event.extendedProps;
          
          return (
            <div className="p-1 overflow-hidden">
              <div className="flex items-center space-x-1">
                {icon && (
                  <span className="text-xs opacity-80">
                    {getIconSymbol(icon)}
                  </span>
                )}
                <span className="truncate flex-1 text-xs font-medium">
                  {event.title}
                </span>
              </div>
              {amount && (
                <div className="text-xs opacity-90 mt-0.5">
                  {formatCurrency(amount)}
                </div>
              )}
            </div>
          );
        }}
        dayHeaderFormat={{ weekday: 'short' }}
        titleFormat={{ year: 'numeric', month: 'long' }}
        buttonText={{
          today: 'Today',
          month: 'Month',
          week: 'Week',
          day: 'Day'
        }}
        firstDay={0} // Sunday
        slotMinTime="06:00:00"
        slotMaxTime="22:00:00"
        allDaySlot={true}
        allDayText="All Day"
        slotLabelFormat={{
          hour: 'numeric',
          minute: '2-digit',
          omitZeroMinute: false,
          meridiem: 'short'
        }}
        eventTimeFormat={{
          hour: 'numeric',
          minute: '2-digit',
          meridiem: 'short'
        }}
        nowIndicator={true}
        scrollTime="08:00:00"
        businessHours={{
          daysOfWeek: [1, 2, 3, 4, 5], // Monday - Friday
          startTime: '09:00',
          endTime: '17:00',
        }}
      />
    </div>
  );
});

CalendarView.displayName = 'CalendarView';

/**
 * Get icon symbol for display
 */
function getIconSymbol(icon: string): string {
  const iconMap: Record<string, string> = {
    'credit-card': '💳',
    'dollar-sign': '💰',
    'piggy-bank': '🐷',
    'target': '🎯',
    'banknote': '💵',
    'calendar': '📅',
    'alert-circle': '⚠️',
    'trending-up': '📈',
    'shield': '🛡️',
    'file-text': '📄'
  };
  
  return iconMap[icon] || '📅';
}

/**
 * Format currency for display
 */
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}
