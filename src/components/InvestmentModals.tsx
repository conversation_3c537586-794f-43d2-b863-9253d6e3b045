import { useState } from 'react';
import { X, Plus, Save, AlertCircle } from 'lucide-react';
import { Portfolio } from '../types/investment';

interface CreatePortfolioModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (portfolio: Omit<Portfolio, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => void;
}

export function CreatePortfolioModal({ isOpen, onClose, onSave }: CreatePortfolioModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    initial_balance: '',
    currency: 'EGP'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const validateForm = () => {
    console.log('🔍 Validating form with data:', formData);
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Portfolio name is required';
    }
    
    if (!formData.initial_balance || parseFloat(formData.initial_balance) <= 0) {
      newErrors.initial_balance = 'Initial balance must be greater than 0';
    }
    
    setErrors(newErrors);
    console.log('📋 Validation result:', { errors: newErrors, isValid: Object.keys(newErrors).length === 0 });
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('📋 Portfolio form submitted with data:', formData);
    
    if (!validateForm()) {
      console.log('❌ Form validation failed:', errors);
      return;
    }
    
    const initialBalance = parseFloat(formData.initial_balance);
    console.log('💰 Parsed initial balance:', initialBalance);
    
    const portfolioData = {
      name: formData.name.trim(),
      description: formData.description.trim(),
      initial_balance: initialBalance,
      current_balance: initialBalance, // Start with same as initial
      currency: formData.currency
    };
    
    console.log('🎯 Calling onSave with portfolio data:', portfolioData);
    onSave(portfolioData);
    
    // Reset form
    setFormData({
      name: '',
      description: '',
      initial_balance: '',
      currency: 'EGP'
    });
    setErrors({});
    onClose();
  };

  const handleClose = () => {
    setFormData({
      name: '',
      description: '',
      initial_balance: '',
      currency: 'EGP'
    });
    setErrors({});
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Create New Portfolio
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Portfolio Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Portfolio Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white ${
                errors.name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder="e.g., Growth Portfolio, Income Portfolio"
            />
            {errors.name && (
              <div className="flex items-center mt-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.name}
              </div>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Optional description of your investment strategy"
              rows={3}
            />
          </div>

          {/* Initial Balance */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Initial Balance *
            </label>
            <div className="relative">
              <input
                type="number"
                value={formData.initial_balance}
                onChange={(e) => setFormData({ ...formData, initial_balance: e.target.value })}
                className={`w-full px-3 py-2 pr-16 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white ${
                  errors.initial_balance ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                }`}
                placeholder="50000"
                min="0"
                step="0.01"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <select
                  value={formData.currency}
                  onChange={(e) => setFormData({ ...formData, currency: e.target.value })}
                  className="bg-transparent border-none text-sm text-gray-500 focus:outline-none"
                >
                  <option value="EGP">EGP</option>
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                </select>
              </div>
            </div>
            {errors.initial_balance && (
              <div className="flex items-center mt-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.initial_balance}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
            >
              <Save className="w-4 h-4 mr-2" />
              Create Portfolio
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

interface AddStockModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (stock: {
    symbol: string;
    shares: number;
    purchase_price: number;
    portfolio_id: string;
  }) => void;
  portfolios: Portfolio[];
}

export function AddStockModal({ isOpen, onClose, onSave, portfolios }: AddStockModalProps) {
  const [formData, setFormData] = useState({
    symbol: '',
    shares: '',
    purchase_price: '',
    portfolio_id: portfolios[0]?.id || ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  if (!isOpen) return null;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.symbol.trim()) {
      newErrors.symbol = 'Stock symbol is required';
    }
    
    if (!formData.shares || parseFloat(formData.shares) <= 0) {
      newErrors.shares = 'Shares must be greater than 0';
    }
    
    if (!formData.purchase_price || parseFloat(formData.purchase_price) <= 0) {
      newErrors.purchase_price = 'Purchase price must be greater than 0';
    }
    
    if (!formData.portfolio_id) {
      newErrors.portfolio_id = 'Please select a portfolio';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    onSave({
      symbol: formData.symbol.trim().toUpperCase(),
      shares: parseFloat(formData.shares),
      purchase_price: parseFloat(formData.purchase_price),
      portfolio_id: formData.portfolio_id
    });
    
    // Reset form
    setFormData({
      symbol: '',
      shares: '',
      purchase_price: '',
      portfolio_id: portfolios[0]?.id || ''
    });
    setErrors({});
    onClose();
  };

  const handleClose = () => {
    setFormData({
      symbol: '',
      shares: '',
      purchase_price: '',
      portfolio_id: portfolios[0]?.id || ''
    });
    setErrors({});
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Add Stock Purchase
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Portfolio Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Portfolio *
            </label>
            <select
              value={formData.portfolio_id}
              onChange={(e) => setFormData({ ...formData, portfolio_id: e.target.value })}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white ${
                errors.portfolio_id ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
            >
              <option value="">Select Portfolio</option>
              {portfolios.map((portfolio) => (
                <option key={portfolio.id} value={portfolio.id}>
                  {portfolio.name}
                </option>
              ))}
            </select>
            {errors.portfolio_id && (
              <div className="flex items-center mt-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.portfolio_id}
              </div>
            )}
          </div>

          {/* Stock Symbol */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Stock Symbol *
            </label>
            <input
              type="text"
              value={formData.symbol}
              onChange={(e) => setFormData({ ...formData, symbol: e.target.value })}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white ${
                errors.symbol ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder="e.g., COMI, ETEL, TALAAT"
              style={{ textTransform: 'uppercase' }}
            />
            {errors.symbol && (
              <div className="flex items-center mt-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.symbol}
              </div>
            )}
          </div>

          {/* Number of Shares */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Number of Shares *
            </label>
            <input
              type="number"
              value={formData.shares}
              onChange={(e) => setFormData({ ...formData, shares: e.target.value })}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white ${
                errors.shares ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder="100"
              min="0"
              step="1"
            />
            {errors.shares && (
              <div className="flex items-center mt-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.shares}
              </div>
            )}
          </div>

          {/* Purchase Price */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Purchase Price per Share *
            </label>
            <input
              type="number"
              value={formData.purchase_price}
              onChange={(e) => setFormData({ ...formData, purchase_price: e.target.value })}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white ${
                errors.purchase_price ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder="52.15"
              min="0"
              step="0.01"
            />
            {errors.purchase_price && (
              <div className="flex items-center mt-1 text-red-600 text-sm">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.purchase_price}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Stock
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
