/**
 * Enhanced Investment Modals for Real Portfolio Management
 */

import { useState } from 'react';
import { X, Search, TrendingUp } from 'lucide-react';
import { Portfolio } from '../types/investment';
import { stockDataService } from '../services/stockDataService';

interface CreateRealPortfolioModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (portfolio: Omit<Portfolio, 'id' | 'user_id' | 'created_at' | 'updated_at' | 'current_balance'>) => Promise<void>;
}

export function CreateRealPortfolioModal({ isOpen, onClose, onSave }: CreateRealPortfolioModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    initial_balance: 0,
    currency: 'EGP'
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) return;

    setIsLoading(true);
    try {
      await onSave(formData);
      setFormData({ name: '', description: '', initial_balance: 0, currency: 'EGP' });
      onClose();
    } catch (error) {
      console.error('Error creating portfolio:', error);
      alert('Failed to create portfolio. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">Create New Portfolio</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Portfolio Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="e.g., Growth Portfolio"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Investment strategy and goals..."
              rows={3}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Initial Cash Balance (EGP)
            </label>
            <input
              type="number"
              value={formData.initial_balance}
              onChange={(e) => setFormData({...formData, initial_balance: parseFloat(e.target.value) || 0})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="0"
              min="0"
              step="0.01"
            />
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || !formData.name.trim()}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Creating...' : 'Create Portfolio'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

interface AddStockToPortfolioModalProps {
  isOpen: boolean;
  onClose: () => void;
  portfolios: Portfolio[];
  onAddStock: (transaction: {
    portfolio_id: string;
    symbol: string;
    shares: number;
    price: number;
    fees?: number;
    notes?: string;
  }) => Promise<void>;
}

export function AddStockToPortfolioModal({ isOpen, onClose, portfolios, onAddStock }: AddStockToPortfolioModalProps) {
  const [formData, setFormData] = useState({
    portfolio_id: '',
    symbol: '',
    shares: 0,
    price: 0,
    fees: 0,
    notes: ''
  });
  const [stockSearch, setStockSearch] = useState('');
  const [stockSuggestions, setStockSuggestions] = useState<any[]>([]);
  const [currentPrice, setCurrentPrice] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const searchStocks = async (query: string) => {
    if (query.length < 2) {
      setStockSuggestions([]);
      return;
    }

    try {
      const results = await stockDataService.searchStocks(query);
      setStockSuggestions(results.slice(0, 5)); // Limit to 5 suggestions
    } catch (error) {
      console.error('Error searching stocks:', error);
      setStockSuggestions([]);
    }
  };

  const selectStock = async (stock: any) => {
    setFormData({
      ...formData,
      symbol: stock.symbol,
      price: stock.price || 0
    });
    setStockSearch(stock.symbol);
    setCurrentPrice(stock.price || null);
    setStockSuggestions([]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.portfolio_id || !formData.symbol || formData.shares <= 0 || formData.price <= 0) {
      alert('Please fill in all required fields with valid values.');
      return;
    }

    setIsLoading(true);
    try {
      await onAddStock({
        portfolio_id: formData.portfolio_id,
        symbol: formData.symbol.toUpperCase(),
        shares: formData.shares,
        price: formData.price,
        fees: formData.fees,
        notes: formData.notes
      });
      
      setFormData({
        portfolio_id: '',
        symbol: '',
        shares: 0,
        price: 0,
        fees: 0,
        notes: ''
      });
      setStockSearch('');
      setCurrentPrice(null);
      onClose();
    } catch (error) {
      console.error('Error adding stock:', error);
      alert('Failed to add stock. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const totalCost = (formData.shares * formData.price) + formData.fees;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">Add Stock to Portfolio</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Portfolio *
            </label>
            <select
              value={formData.portfolio_id}
              onChange={(e) => setFormData({...formData, portfolio_id: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              required
            >
              <option value="">Select Portfolio</option>
              {portfolios.map(portfolio => (
                <option key={portfolio.id} value={portfolio.id}>{portfolio.name}</option>
              ))}
            </select>
          </div>

          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Stock Symbol *
            </label>
            <div className="relative">
              <input
                type="text"
                value={stockSearch}
                onChange={(e) => {
                  setStockSearch(e.target.value);
                  searchStocks(e.target.value);
                }}
                className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Search EGX stocks..."
                required
              />
              <Search className="absolute right-3 top-2.5 w-5 h-5 text-gray-400" />
            </div>
            
            {stockSuggestions.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-48 overflow-y-auto">
                {stockSuggestions.map((stock, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => selectStock(stock)}
                    className="w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-600 flex items-center justify-between"
                  >
                    <div>
                      <span className="font-medium text-gray-900 dark:text-white">{stock.symbol}</span>
                      <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">{stock.name}</span>
                    </div>
                    <span className="text-sm font-medium text-green-600 dark:text-green-400">
                      {stock.price ? `${stock.price.toFixed(2)} EGP` : 'N/A'}
                    </span>
                  </button>
                ))}
              </div>
            )}
          </div>

          {currentPrice && (
            <div className="flex items-center p-3 bg-green-50 dark:bg-green-900 rounded-lg">
              <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
              <span className="text-sm text-green-800 dark:text-green-200">
                Current Price: <strong>{currentPrice.toFixed(2)} EGP</strong>
              </span>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Shares *
              </label>
              <input
                type="number"
                value={formData.shares}
                onChange={(e) => setFormData({...formData, shares: parseInt(e.target.value) || 0})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="0"
                min="1"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Purchase Price (EGP) *
              </label>
              <input
                type="number"
                value={formData.price}
                onChange={(e) => setFormData({...formData, price: parseFloat(e.target.value) || 0})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="0.00"
                min="0.01"
                step="0.01"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Trading Fees (EGP)
            </label>
            <input
              type="number"
              value={formData.fees}
              onChange={(e) => setFormData({...formData, fees: parseFloat(e.target.value) || 0})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="0.00"
              min="0"
              step="0.01"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Optional notes about this purchase..."
              rows={2}
            />
          </div>

          {totalCost > 0 && (
            <div className="p-3 bg-blue-50 dark:bg-blue-900 rounded-lg">
              <span className="text-sm text-blue-800 dark:text-blue-200">
                Total Cost: <strong>{totalCost.toFixed(2)} EGP</strong>
                {formData.fees > 0 && (
                  <span className="text-xs ml-2">
                    ({(formData.shares * formData.price).toFixed(2)} + {formData.fees.toFixed(2)} fees)
                  </span>
                )}
              </span>
            </div>
          )}

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Adding...' : 'Add Stock'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
