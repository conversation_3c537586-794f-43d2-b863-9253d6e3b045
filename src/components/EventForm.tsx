/**
 * EventForm Component
 * Form for creating and editing calendar events
 */

import { memo } from 'react';
import { RecurrencePattern, ReminderUnit } from '../types/calendar';
import { ColorPicker } from './ColorPicker';
import { format } from 'date-fns';

interface EventFormProps {
  selectedDate: Date | null;
  eventTitle: string;
  eventDescription: string;
  eventAmount: string;
  eventColor: string;
  isRecurring: boolean;
  recurrencePattern: RecurrencePattern;
  recurrenceEndDate: string;
  recurrenceCount: string;
  reminder: boolean;
  reminderTime: number;
  reminderUnit: ReminderUnit;
  eventCategory: string;
  onEventTitleChange: (value: string) => void;
  onEventDescriptionChange: (value: string) => void;
  onEventAmountChange: (value: string) => void;
  onEventColorChange: (value: string) => void;
  onIsRecurringChange: (value: boolean) => void;
  onRecurrencePatternChange: (value: RecurrencePattern) => void;
  onRecurrenceEndDateChange: (value: string) => void;
  onRecurrenceCountChange: (value: string) => void;
  onReminderChange: (value: boolean) => void;
  onReminderTimeChange: (value: number) => void;
  onReminderUnitChange: (value: ReminderUnit) => void;
  onEventCategoryChange: (value: string) => void;
}

// Event categories
const eventCategories = [
  { id: 'bills', label: 'Bills & Utilities', icon: '💳' },
  { id: 'income', label: 'Income & Salary', icon: '💰' },
  { id: 'investments', label: 'Investments', icon: '📈' },
  { id: 'savings', label: 'Savings', icon: '🐷' },
  { id: 'taxes', label: 'Taxes', icon: '📄' },
  { id: 'insurance', label: 'Insurance', icon: '🛡️' },
  { id: 'medical', label: 'Medical & Health', icon: '🏥' },
  { id: 'entertainment', label: 'Entertainment', icon: '🎬' },
  { id: 'travel', label: 'Travel', icon: '✈️' },
  { id: 'education', label: 'Education', icon: '📚' },
  { id: 'other', label: 'Other', icon: '📅' }
];

export const EventForm = memo<EventFormProps>(({
  selectedDate,
  eventTitle,
  eventDescription,
  eventAmount,
  eventColor,
  isRecurring,
  recurrencePattern,
  recurrenceEndDate,
  recurrenceCount,
  reminder,
  reminderTime,
  reminderUnit,
  eventCategory,
  onEventTitleChange,
  onEventDescriptionChange,
  onEventAmountChange,
  onEventColorChange,
  onIsRecurringChange,
  onRecurrencePatternChange,
  onRecurrenceEndDateChange,
  onRecurrenceCountChange,
  onReminderChange,
  onReminderTimeChange,
  onReminderUnitChange,
  onEventCategoryChange
}) => {
  const selectedDateString = selectedDate ? format(selectedDate, 'yyyy-MM-dd') : '';

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <div>
          <label htmlFor="eventTitle" className="block text-sm font-medium text-gray-700 mb-1">
            Event Title *
          </label>
          <input
            type="text"
            id="eventTitle"
            value={eventTitle}
            onChange={(e) => onEventTitleChange(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter event title"
            maxLength={100}
          />
        </div>

        <div>
          <label htmlFor="eventDescription" className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            id="eventDescription"
            value={eventDescription}
            onChange={(e) => onEventDescriptionChange(e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter event description"
            maxLength={500}
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="eventAmount" className="block text-sm font-medium text-gray-700 mb-1">
              Amount (Optional)
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">$</span>
              <input
                type="number"
                id="eventAmount"
                value={eventAmount}
                onChange={(e) => onEventAmountChange(e.target.value)}
                className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="0.00"
                min="0"
                step="0.01"
              />
            </div>
          </div>

          <div>
            <label htmlFor="eventCategory" className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              id="eventCategory"
              value={eventCategory}
              onChange={(e) => onEventCategoryChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select category</option>
              {eventCategories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.icon} {category.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Date Display */}
      {selectedDate && (
        <div className="p-3 bg-blue-50 rounded-md">
          <p className="text-sm text-blue-800">
            <strong>Selected Date:</strong> {format(selectedDate, 'MMMM dd, yyyy')}
          </p>
        </div>
      )}

      {/* Color Picker */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Event Color
        </label>
        <ColorPicker
          value={eventColor}
          onChange={onEventColorChange}
          label=""
        />
      </div>

      {/* Recurrence Settings */}
      <div className="border-t border-gray-200 pt-6">
        <div className="flex items-center mb-4">
          <input
            type="checkbox"
            id="isRecurring"
            checked={isRecurring}
            onChange={(e) => onIsRecurringChange(e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isRecurring" className="ml-2 text-sm font-medium text-gray-700">
            Recurring Event
          </label>
        </div>

        {isRecurring && (
          <div className="space-y-4 pl-6">
            <div>
              <label htmlFor="recurrencePattern" className="block text-sm font-medium text-gray-700 mb-1">
                Repeat Pattern
              </label>
              <select
                id="recurrencePattern"
                value={recurrencePattern}
                onChange={(e) => onRecurrencePatternChange(e.target.value as RecurrencePattern)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="recurrenceEndDate" className="block text-sm font-medium text-gray-700 mb-1">
                  End Date (Optional)
                </label>
                <input
                  type="date"
                  id="recurrenceEndDate"
                  value={recurrenceEndDate}
                  onChange={(e) => onRecurrenceEndDateChange(e.target.value)}
                  min={selectedDateString}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label htmlFor="recurrenceCount" className="block text-sm font-medium text-gray-700 mb-1">
                  Or Repeat Count
                </label>
                <input
                  type="number"
                  id="recurrenceCount"
                  value={recurrenceCount}
                  onChange={(e) => onRecurrenceCountChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Number of times"
                  min="1"
                  max="365"
                />
              </div>
            </div>

            <p className="text-xs text-gray-500">
              Specify either an end date or a repeat count, not both.
            </p>
          </div>
        )}
      </div>

      {/* Reminder Settings */}
      <div className="border-t border-gray-200 pt-6">
        <div className="flex items-center mb-4">
          <input
            type="checkbox"
            id="reminder"
            checked={reminder}
            onChange={(e) => onReminderChange(e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="reminder" className="ml-2 text-sm font-medium text-gray-700">
            Set Reminder
          </label>
        </div>

        {reminder && (
          <div className="pl-6 grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="reminderTime" className="block text-sm font-medium text-gray-700 mb-1">
                Reminder Time
              </label>
              <input
                type="number"
                id="reminderTime"
                value={reminderTime}
                onChange={(e) => onReminderTimeChange(parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="1"
                max="999"
              />
            </div>

            <div>
              <label htmlFor="reminderUnit" className="block text-sm font-medium text-gray-700 mb-1">
                Time Unit
              </label>
              <select
                id="reminderUnit"
                value={reminderUnit}
                onChange={(e) => onReminderUnitChange(e.target.value as ReminderUnit)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="minutes">Minutes</option>
                <option value="hours">Hours</option>
                <option value="days">Days</option>
              </select>
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

EventForm.displayName = 'EventForm';
