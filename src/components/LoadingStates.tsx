/**
 * Loading Components
 * Improved loading states for better user experience
 */

import { Loader2, TrendingUp, DollarSign, Calculator } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  return (
    <Loader2 className={`animate-spin ${sizeClasses[size]} ${className}`} />
  );
}

interface PageLoadingProps {
  message?: string;
  icon?: 'default' | 'financial' | 'calculator' | 'chart';
}

export function PageLoading({ message = 'Loading...', icon = 'default' }: PageLoadingProps) {
  const getIcon = () => {
    switch (icon) {
      case 'financial':
        return <DollarSign className="h-8 w-8 text-blue-600" />;
      case 'calculator':
        return <Calculator className="h-8 w-8 text-blue-600" />;
      case 'chart':
        return <TrendingUp className="h-8 w-8 text-blue-600" />;
      default:
        return <LoadingSpinner size="lg" className="text-blue-600" />;
    }
  };

  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="text-center">
        <div className="flex justify-center mb-4">
          {getIcon()}
        </div>
        <p className="text-gray-600 dark:text-gray-400 text-lg">{message}</p>
      </div>
    </div>
  );
}

interface CardLoadingProps {
  title?: string;
  lines?: number;
}

export function CardLoading({ title, lines = 3 }: CardLoadingProps) {
  return (
    <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm border border-gray-200 dark:border-dark-700 p-6">
      {title && (
        <div className="mb-4">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/3"></div>
        </div>
      )}
      <div className="space-y-3">
        {Array.from({ length: lines }, (_, i) => (
          <div key={i} className="animate-pulse">
            <div className={`h-4 bg-gray-200 dark:bg-gray-700 rounded ${i === lines - 1 ? 'w-2/3' : 'w-full'}`}></div>
          </div>
        ))}
      </div>
    </div>
  );
}

interface TableLoadingProps {
  columns?: number;
  rows?: number;
}

export function TableLoading({ columns = 4, rows = 5 }: TableLoadingProps) {
  return (
    <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm border border-gray-200 dark:border-dark-700">
      {/* Header */}
      <div className="border-b border-gray-200 dark:border-dark-700 p-4">
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }, (_, i) => (
            <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
      
      {/* Rows */}
      <div className="divide-y divide-gray-200 dark:divide-dark-700">
        {Array.from({ length: rows }, (_, rowIndex) => (
          <div key={rowIndex} className="p-4">
            <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }, (_, colIndex) => (
                <div 
                  key={colIndex} 
                  className={`h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse ${
                    colIndex === columns - 1 ? 'w-2/3' : 'w-full'
                  }`}
                  style={{ animationDelay: `${(rowIndex * columns + colIndex) * 0.1}s` }}
                ></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

interface ChartLoadingProps {
  height?: string;
  title?: string;
}

export function ChartLoading({ height = '300px', title }: ChartLoadingProps) {
  return (
    <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm border border-gray-200 dark:border-dark-700 p-6">
      {title && (
        <div className="mb-6">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/3"></div>
        </div>
      )}
      
      <div className="relative" style={{ height }}>
        <div className="absolute inset-0 bg-gray-100 dark:bg-gray-800 rounded animate-pulse">
          {/* Simulate chart bars */}
          <div className="flex items-end justify-around h-full p-4">
            {Array.from({ length: 8 }, (_, i) => (
              <div
                key={i}
                className="bg-gray-200 dark:bg-gray-700 rounded-t animate-pulse"
                style={{
                  height: `${Math.random() * 60 + 20}%`,
                  width: '8%',
                  animationDelay: `${i * 0.2}s`
                }}
              ></div>
            ))}
          </div>
        </div>
        
        {/* Loading overlay */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <TrendingUp className="h-8 w-8 text-blue-600 mb-2 mx-auto animate-pulse" />
            <p className="text-gray-500 dark:text-gray-400 text-sm">Loading chart...</p>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Error state component
 */
interface ErrorStateProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  className?: string;
}

export function ErrorState({ 
  title = 'Something went wrong', 
  message, 
  onRetry, 
  className = '' 
}: ErrorStateProps) {
  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-6 ${className}`}>
      <div className="text-center">
        <div className="text-red-600 mb-4">
          <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.728-.833-2.498 0L4.346 15.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-red-900 mb-2">{title}</h3>
        <p className="text-red-700 mb-4">{message}</p>
        {onRetry && (
          <button
            onClick={onRetry}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Try Again
          </button>
        )}
      </div>
    </div>
  );
}

/**
 * Empty state component
 */
interface EmptyStateProps {
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: React.ReactNode;
  className?: string;
}

export function EmptyState({ 
  title, 
  description, 
  action, 
  icon, 
  className = '' 
}: EmptyStateProps) {
  return (
    <div className={`text-center py-12 ${className}`}>
      {icon && (
        <div className="text-gray-400 mb-4">
          {icon}
        </div>
      )}
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{title}</h3>
      <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">{description}</p>
      {action && (
        <button
          onClick={action.onClick}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {action.label}
        </button>
      )}
    </div>
  );
}
