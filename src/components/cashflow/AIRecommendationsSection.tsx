import type { AIRecommendation } from '../../types/cashflow';
import { Brain } from 'lucide-react';

interface AIRecommendationsSectionProps {
  aiRecommendations: AIRecommendation[];
}

export function AIRecommendationsSection({ aiRecommendations }: AIRecommendationsSectionProps) {
  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Brain className="h-6 w-6 mr-2 text-indigo-600" />
          🤖 AI-Powered Financial Recommendations
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Intelligent recommendations based on your financial situation analysis.
        </p>
        
        <div className="space-y-4">
          {aiRecommendations.map((rec, index) => (
            <div 
              key={index}
              className={`p-4 rounded-lg border-l-4 ${
                rec.priority.includes('URGENT') 
                  ? 'border-red-500 bg-red-50 dark:bg-red-900/20' 
                  : rec.priority.includes('HIGH')
                  ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20'
                  : rec.priority.includes('STRATEGIC')
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-green-500 bg-green-50 dark:bg-green-900/20'
              }`}
            >
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-semibold text-gray-900 dark:text-white">
                  {rec.priority} - {rec.action}
                </h4>
                <span className={`text-xs px-2 py-1 rounded ${
                  rec.category === 'liquidity' ? 'bg-red-100 text-red-800' :
                  rec.category === 'debt' ? 'bg-orange-100 text-orange-800' :
                  rec.category === 'optimization' ? 'bg-blue-100 text-blue-800' :
                  'bg-green-100 text-green-800'
                }`}>
                  {rec.category}
                </span>
              </div>
              <p className="text-gray-700 dark:text-gray-300">
                {rec.details}
              </p>
            </div>
          ))}
        </div>

        {aiRecommendations.length === 0 && (
          <div className="text-center py-8">
            <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              No specific recommendations at this time. Your financial situation appears stable.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
