import { formatCurrency } from '../../utils/currency';
import type { ScenarioResult } from '../../types/cashflow';
import { Calculator, TrendingUp, CheckCircle } from 'lucide-react';

interface ScenarioAnalysisSectionProps {
  scenarios: ScenarioResult[];
}

export function ScenarioAnalysisSection({ scenarios }: ScenarioAnalysisSectionProps) {
  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Calculator className="h-6 w-6 mr-2 text-purple-600" />
          🔄 Scenario Analysis
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Compare different expense reduction scenarios to see their impact on your cash flow.
        </p>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {scenarios.map((scenario, index) => (
            <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <h4 className="font-semibold text-lg mb-3 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                {scenario.name}
              </h4>
              
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">Monthly Savings:</span>
                    <div className="font-bold text-green-600">
                      {formatCurrency(scenario.monthly_savings)}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">Annual Savings:</span>
                    <div className="font-bold text-green-600">
                      {formatCurrency(scenario.annual_savings)}
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">New Monthly Expense:</span>
                    <div className="font-medium text-blue-600">
                      {formatCurrency(scenario.new_monthly_expense)}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">Achieves Positive Flow:</span>
                    <div className={`font-medium flex items-center ${
                      scenario.achieves_positive_flow ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {scenario.achieves_positive_flow ? (
                        <>
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Yes
                        </>
                      ) : (
                        <>
                          ❌ No
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Mini projection chart or summary */}
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <h5 className="text-sm font-medium mb-2">24-Month Impact:</h5>
                <div className="grid grid-cols-4 gap-2 text-xs">
                  {scenario.projection.slice(0, 4).map((p, i) => (
                    <div key={i} className="text-center">
                      <div className="text-gray-500">M{i + 1}</div>
                      <div className={`font-medium ${
                        p.ending_balance >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {formatCurrency(p.ending_balance)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Scenario Comparison */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">📊 Scenario Comparison</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Scenario
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Monthly Savings
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Annual Impact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  New Expense Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Positive Flow
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {scenarios.map((scenario, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    {scenario.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                    {formatCurrency(scenario.monthly_savings)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                    {formatCurrency(scenario.annual_savings)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                    {formatCurrency(scenario.new_monthly_expense)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      scenario.achieves_positive_flow 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {scenario.achieves_positive_flow ? '✅ Yes' : '❌ No'}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
