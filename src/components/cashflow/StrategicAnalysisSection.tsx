import { AlertTriangle, TrendingUp, DollarSign, Calendar, CheckCircle, XCircle } from 'lucide-react';
import { formatCurrency } from '../../utils/currency';
import type { StrategicAnalysis, FinancialGap, StrategicOption } from '../../utils/strategicAnalysis';

interface StrategicAnalysisSectionProps {
  strategicAnalysis: StrategicAnalysis;
}

export function StrategicAnalysisSection({ strategicAnalysis }: StrategicAnalysisSectionProps) {
  const { gaps, totalRisk, riskLevel, strategicOptions, recommendedAction } = strategicAnalysis;

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-green-600 bg-green-100';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-green-600 bg-green-100';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'cd_renewal': return <TrendingUp className="w-5 h-5" />;
      case 'early_payoff': return <DollarSign className="w-5 h-5" />;
      case 'cash_reserve': return <Calendar className="w-5 h-5" />;
      default: return <AlertTriangle className="w-5 h-5" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Risk Overview */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <AlertTriangle className="w-5 h-5 mr-2" />
          🎯 Strategic Financial Analysis
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="text-center p-4 rounded-lg bg-gray-50 dark:bg-gray-700">
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {gaps.length}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Funding Gaps</div>
          </div>
          
          <div className="text-center p-4 rounded-lg bg-gray-50 dark:bg-gray-700">
            <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {formatCurrency(totalRisk)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Total Risk Exposure</div>
          </div>
          
          <div className="text-center p-4 rounded-lg">
            <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getRiskColor(riskLevel)}`}>
              {riskLevel.toUpperCase()} RISK
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">Risk Level</div>
          </div>
        </div>

        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
          <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
            📋 Recommended Action
          </h4>
          <p className="text-blue-800 dark:text-blue-200">{recommendedAction}</p>
        </div>
      </div>

      {/* Financial Gaps Analysis */}
      {gaps.length > 0 && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4">🔍 Identified Funding Gaps</h3>
          
          <div className="space-y-4">
            {gaps.map((gap, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                      Gap #{index + 1}: {gap.startDate} - {gap.endDate}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {gap.durationMonths} months • Active Loan: {gap.activeLoan}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-red-600">
                      {formatCurrency(gap.monthlyShortfall)}/month
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Total: {formatCurrency(gap.totalFundingNeeded)}
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-1">Expired CDs:</h5>
                    <ul className="text-gray-600 dark:text-gray-400">
                      {gap.expiredCDs.length > 0 ? (
                        gap.expiredCDs.map((cd, i) => <li key={i}>• {cd}</li>)
                      ) : (
                        <li>• None</li>
                      )}
                    </ul>
                  </div>
                  
                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-1">Active CDs:</h5>
                    <ul className="text-gray-600 dark:text-gray-400">
                      {gap.activeCDs.length > 0 ? (
                        gap.activeCDs.map((cd, i) => <li key={i}>• {cd}</li>)
                      ) : (
                        <li>• None</li>
                      )}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Strategic Options */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">💡 Strategic Options</h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {strategicOptions.map((option) => (
            <div key={option.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center">
                  {getTypeIcon(option.type)}
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100 ml-2">
                    {option.title}
                  </h4>
                </div>
                <span className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(option.priority)}`}>
                  {option.priority.toUpperCase()}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                {option.description}
              </p>
              
              <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                <div>
                  <span className="font-medium">Cost:</span> {formatCurrency(option.estimatedCost)}
                </div>
                <div>
                  <span className="font-medium">Savings:</span> {formatCurrency(option.estimatedSavings)}
                </div>
              </div>
              
              <div className="text-sm mb-3">
                <span className="font-medium">Timeframe:</span> {option.timeframe}
              </div>
              
              <div className="space-y-2 text-sm">
                <div>
                  <h5 className="font-medium text-green-700 dark:text-green-400 flex items-center">
                    <CheckCircle className="w-4 h-4 mr-1" /> Pros:
                  </h5>
                  <ul className="text-gray-600 dark:text-gray-400 ml-5">
                    {option.pros.map((pro, i) => <li key={i}>• {pro}</li>)}
                  </ul>
                </div>
                
                <div>
                  <h5 className="font-medium text-red-700 dark:text-red-400 flex items-center">
                    <XCircle className="w-4 h-4 mr-1" /> Cons:
                  </h5>
                  <ul className="text-gray-600 dark:text-gray-400 ml-5">
                    {option.cons.map((con, i) => <li key={i}>• {con}</li>)}
                  </ul>
                </div>
              </div>
              
              <details className="mt-3">
                <summary className="cursor-pointer text-sm font-medium text-blue-600 dark:text-blue-400">
                  Implementation Steps
                </summary>
                <ul className="mt-2 text-sm text-gray-600 dark:text-gray-400 ml-4">
                  {option.implementation.map((step, i) => (
                    <li key={i} className="mb-1">
                      {i + 1}. {step}
                    </li>
                  ))}
                </ul>
              </details>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
