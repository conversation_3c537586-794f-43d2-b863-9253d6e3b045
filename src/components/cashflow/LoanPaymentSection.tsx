import { useState } from 'react';
import { format } from 'date-fns';
import { formatCurrency } from '../../utils/currency';
import type { Loan } from '../../types/cashflow';
import { Calendar, CheckCircle, XCircle } from 'lucide-react';

interface LoanPaymentSectionProps {
  loans?: Loan[];
  isMonthPaid: (loanId: string, month: string) => boolean;
  markMonthAsPaid: (loanId: string, month: string) => void;
  markMonthAsUnpaid: (loanId: string, month: string) => void;
}

export function LoanPaymentSection({ 
  loans, 
  isMonthPaid, 
  markMonthAsPaid, 
  markMonthAsUnpaid 
}: LoanPaymentSectionProps) {
  const [selectedLoan, setSelectedLoan] = useState<string>('');

  if (!loans || loans.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">📅 Loan Payment Management</h3>
        <p className="text-gray-500 dark:text-gray-400">No loans found.</p>
      </div>
    );
  }

  // Generate months to show - show current and future months around the payment schedule
  const generateMonthsToShow = () => {
    const currentDate = new Date();
    const months = [];
    
    // Show 6 months before current month and 12 months after
    for (let i = -6; i <= 12; i++) {
      const monthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + i, 1);
      months.push({
        date: monthDate,
        key: format(monthDate, 'yyyy-MM'),
        display: format(monthDate, 'MMM yyyy'),
        isPast: monthDate < new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
      });
    }
    
    return months;
  };

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Calendar className="h-6 w-6 mr-2 text-blue-600" />
          📅 Loan Payment Management
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Track which loan payments have been made to get accurate cash flow projections.
        </p>

        {/* Loan Selector */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Select Loan:
          </label>
          <select
            value={selectedLoan}
            onChange={(e) => setSelectedLoan(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          >
            <option value="">Choose a loan...</option>
            {loans.map((loan) => (
              <option key={loan.id} value={loan.id}>
                {loan.name} - {formatCurrency(loan.monthly_payment)}/month
              </option>
            ))}
          </select>
        </div>

        {/* Payment Management Grid */}
        {selectedLoan && (
          <div className="space-y-4">
            {loans
              .filter((loan) => loan.id === selectedLoan)
              .map((loan) => {
                const monthsToShow = generateMonthsToShow();
                
                return (
                  <div key={loan.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <h4 className="font-semibold text-lg mb-4">
                      {loan.name} - {formatCurrency(loan.monthly_payment)}/month
                    </h4>
                    
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                      {monthsToShow.map((month) => {
                        const paid = isMonthPaid(loan.id, month.key);
                        
                        return (
                          <div
                            key={month.key}
                            className={`p-3 rounded-lg border text-center cursor-pointer transition-colors ${
                              paid
                                ? 'border-green-500 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                                : month.isPast
                                ? 'border-red-300 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
                                : 'border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
                            }`}
                            onClick={() => {
                              if (paid) {
                                markMonthAsUnpaid(loan.id, month.key);
                              } else {
                                markMonthAsPaid(loan.id, month.key);
                              }
                            }}
                          >
                            <div className="flex items-center justify-center mb-1">
                              {paid ? (
                                <CheckCircle className="h-5 w-5 text-green-600" />
                              ) : (
                                <XCircle className="h-5 w-5 text-gray-400" />
                              )}
                            </div>
                            <div className="text-xs font-medium">
                              {month.display}
                            </div>
                            <div className="text-xs mt-1">
                              {paid ? 'Paid' : month.isPast ? 'Overdue' : 'Pending'}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                    
                    <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
                      <p>Click on a month to toggle payment status.</p>
                      <p>Green = Paid, Red = Overdue, Gray = Pending</p>
                    </div>
                  </div>
                );
              })}
          </div>
        )}
      </div>

      {/* Payment Summary */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">📊 Payment Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {loans.map((loan) => {
            const currentDate = new Date();
            const currentMonth = format(currentDate, 'yyyy-MM');
            const lastMonth = format(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1), 'yyyy-MM');
            
            const currentPaid = isMonthPaid(loan.id, currentMonth);
            const lastPaid = isMonthPaid(loan.id, lastMonth);
            
            return (
              <div key={loan.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                  {loan.name}
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>This Month:</span>
                    <span className={currentPaid ? 'text-green-600' : 'text-red-600'}>
                      {currentPaid ? '✅ Paid' : '❌ Pending'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Last Month:</span>
                    <span className={lastPaid ? 'text-green-600' : 'text-red-600'}>
                      {lastPaid ? '✅ Paid' : '❌ Overdue'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Amount:</span>
                    <span className="font-medium">
                      {formatCurrency(loan.monthly_payment)}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
