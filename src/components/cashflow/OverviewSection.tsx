import { formatCurrency } from '../../utils/currency';
import type { FinancialSituation, RiskMetrics } from '../../types/cashflow';
import { DollarSign, TrendingUp, TrendingDown, AlertTriangle } from 'lucide-react';

interface OverviewSectionProps {
  currentSituation: FinancialSituation;
  riskMetrics: RiskMetrics;
}

export function OverviewSection({ currentSituation, riskMetrics }: OverviewSectionProps) {
  return (
    <div className="space-y-6">
      {/* Current Situation Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Total CD Income</h3>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(currentSituation.total_cd_income)}
              </p>
            </div>
            <DollarSign className="h-8 w-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Loan Payments</h3>
              <p className="text-2xl font-bold text-red-600">
                {formatCurrency(currentSituation.total_loan_payments)}
              </p>
            </div>
            <TrendingDown className="h-8 w-8 text-red-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Monthly Shortfall</h3>
              <p className={`text-2xl font-bold ${
                currentSituation.monthly_shortfall > 0 ? 'text-red-600' : 'text-green-600'
              }`}>
                {formatCurrency(currentSituation.monthly_shortfall)}
              </p>
            </div>
            {currentSituation.monthly_shortfall > 0 ? (
              <AlertTriangle className="h-8 w-8 text-red-500" />
            ) : (
              <TrendingUp className="h-8 w-8 text-green-500" />
            )}
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">CD Coverage</h3>
              <p className={`text-2xl font-bold ${
                currentSituation.cd_coverage_percentage >= 100 ? 'text-green-600' : 'text-orange-600'
              }`}>
                {currentSituation.cd_coverage_percentage.toFixed(1)}%
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-blue-500" />
          </div>
        </div>
      </div>

      {/* Detailed Breakdown */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">💰 Income Breakdown</h3>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">CIB Income (Available for Living):</span>
            <span className="font-medium text-green-600">
              {formatCurrency(currentSituation.cib_available)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Other CD Income (For Loans):</span>
            <span className="font-medium text-blue-600">
              {formatCurrency(currentSituation.cd_for_loans)}
            </span>
          </div>
          <div className="border-t pt-2">
            <div className="flex justify-between font-semibold">
              <span>Total Monthly Income:</span>
              <span className="text-green-600">{formatCurrency(currentSituation.total_cd_income)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Risk Assessment */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">⚠️ Risk Assessment</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-gray-600 dark:text-gray-400">Risk Level:</span>
            <span className={`font-bold ${riskMetrics.color}`}>
              {riskMetrics.level} Risk
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600 dark:text-gray-400">Cash Runway:</span>
            <span className="font-medium">
              {riskMetrics.cashRunway} months
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600 dark:text-gray-400">Stress Level:</span>
            <span className={`font-medium ${riskMetrics.color}`}>
              {riskMetrics.stressLevel}
            </span>
          </div>
          {riskMetrics.maxDeficit > 0 && (
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">Max Deficit:</span>
              <span className="font-medium text-red-600">
                {formatCurrency(riskMetrics.maxDeficit)}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
