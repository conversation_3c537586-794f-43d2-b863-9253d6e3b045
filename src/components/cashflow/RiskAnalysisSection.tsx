import { formatCurrency } from '../../utils/currency';
import type { RiskMetrics, InjectionStrategy } from '../../types/cashflow';
import { AlertTriangle, Shield, Gauge, Zap } from 'lucide-react';

interface RiskAnalysisSectionProps {
  riskMetrics: RiskMetrics;
  injectionStrategy: InjectionStrategy | null;
  criticalAnalysis: {
    negativeMonths: number;
    totalNegativeFlow: number;
    averageNegativeFlow: number;
  };
}

export function RiskAnalysisSection({ 
  riskMetrics, 
  injectionStrategy, 
  criticalAnalysis 
}: RiskAnalysisSectionProps) {
  return (
    <div className="space-y-6">
      {/* Risk Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className={`p-6 rounded-lg shadow ${riskMetrics.bgColor}`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-700">Risk Level</h3>
              <p className={`text-2xl font-bold ${riskMetrics.color}`}>
                {riskMetrics.level}
              </p>
            </div>
            <AlertTriangle className={`h-8 w-8 ${riskMetrics.color.replace('text-', 'text-')}`} />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Cash Runway</h3>
              <p className="text-2xl font-bold text-blue-600">
                {riskMetrics.cashRunway} months
              </p>
            </div>
            <Gauge className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Max Deficit</h3>
              <p className="text-2xl font-bold text-red-600">
                {formatCurrency(riskMetrics.maxDeficit)}
              </p>
            </div>
            <Shield className="h-8 w-8 text-red-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Stress Level</h3>
              <p className={`text-2xl font-bold ${riskMetrics.color}`}>
                {riskMetrics.stressLevel.split(' ')[0]}
              </p>
            </div>
            <Zap className={`h-8 w-8 ${riskMetrics.color.replace('text-', 'text-')}`} />
          </div>
        </div>
      </div>

      {/* Critical Analysis */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <AlertTriangle className="h-6 w-6 mr-2 text-orange-600" />
          🔍 Critical Analysis
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-red-600">
              {criticalAnalysis.negativeMonths}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Negative Months</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600">
              {formatCurrency(criticalAnalysis.totalNegativeFlow)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Total Negative Flow</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-yellow-600">
              {formatCurrency(criticalAnalysis.averageNegativeFlow)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Average Negative Flow</div>
          </div>
        </div>
      </div>

      {/* Cash Injection Strategy */}
      {injectionStrategy && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Zap className="h-6 w-6 mr-2 text-blue-600" />
            💡 Recommended Cash Injection Strategy
          </h3>
          <div className={`p-4 rounded-lg border-l-4 ${
            injectionStrategy.type === 'emergency' 
              ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
              : injectionStrategy.type === 'strategic'
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
          }`}>
            <div className="flex items-start justify-between mb-2">
              <h4 className="font-semibold text-gray-900 dark:text-white">
                {injectionStrategy.name}
              </h4>
              <span className={`text-xs px-2 py-1 rounded ${
                injectionStrategy.type === 'emergency' ? 'bg-red-100 text-red-800' :
                injectionStrategy.type === 'strategic' ? 'bg-blue-100 text-blue-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {injectionStrategy.type}
              </span>
            </div>
            <p className="text-gray-700 dark:text-gray-300 mb-3">
              {injectionStrategy.description}
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Injection Amount:</span>
                <div className="text-lg font-bold text-green-600">
                  {formatCurrency(injectionStrategy.injection_amount)}
                </div>
              </div>
              <div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Suggested Date:</span>
                <div className="text-lg font-medium text-gray-900 dark:text-white">
                  {injectionStrategy.injection_date}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Risk Mitigation Recommendations */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        <h3 className="text-lg font-semibold mb-4">🛡️ Risk Mitigation Recommendations</h3>
        <div className="space-y-3">
          {riskMetrics.cashRunway <= 6 && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
                <span className="font-medium text-red-800 dark:text-red-200">
                  URGENT: Immediate action required - cash runway is critically low
                </span>
              </div>
            </div>
          )}
          
          {riskMetrics.cashRunway <= 12 && riskMetrics.cashRunway > 6 && (
            <div className="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-orange-600 mr-2" />
                <span className="font-medium text-orange-800 dark:text-orange-200">
                  HIGH PRIORITY: Consider debt restructuring or expense reduction
                </span>
              </div>
            </div>
          )}
          
          {riskMetrics.cashRunway > 24 && (
            <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
              <div className="flex items-center">
                <Shield className="h-5 w-5 text-green-600 mr-2" />
                <span className="font-medium text-green-800 dark:text-green-200">
                  STABLE: Financial position is secure, consider optimization opportunities
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
