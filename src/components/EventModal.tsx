/**
 * EventModal Component
 * Modal for creating and editing calendar events
 */

import { memo, useCallback } from 'react';
import { Modal } from './Modal';
import { EventForm } from './EventForm';
import { CalendarEvent, RecurrencePattern, ReminderUnit } from '../types/calendar';
import { X } from 'lucide-react';

interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate: Date | null;
  selectedEvent: CalendarEvent | null;
  // Form state
  eventTitle: string;
  eventDescription: string;
  eventAmount: string;
  eventColor: string;
  isRecurring: boolean;
  recurrencePattern: RecurrencePattern;
  recurrenceEndDate: string;
  recurrenceCount: string;
  reminder: boolean;
  reminderTime: number;
  reminderUnit: ReminderUnit;
  eventCategory: string;
  // Form handlers
  onEventTitleChange: (value: string) => void;
  onEventDescriptionChange: (value: string) => void;
  onEventAmountChange: (value: string) => void;
  onEventColorChange: (value: string) => void;
  onIsRecurringChange: (value: boolean) => void;
  onRecurrencePatternChange: (value: RecurrencePattern) => void;
  onRecurrenceEndDateChange: (value: string) => void;
  onRecurrenceCountChange: (value: string) => void;
  onReminderChange: (value: boolean) => void;
  onReminderTimeChange: (value: number) => void;
  onReminderUnitChange: (value: ReminderUnit) => void;
  onEventCategoryChange: (value: string) => void;
  // Actions
  onSave: () => void;
  onDelete?: () => void;
  // State
  isSaving: boolean;
  error: string | null;
}

export const EventModal = memo<EventModalProps>(({
  isOpen,
  onClose,
  selectedDate,
  selectedEvent,
  eventTitle,
  eventDescription,
  eventAmount,
  eventColor,
  isRecurring,
  recurrencePattern,
  recurrenceEndDate,
  recurrenceCount,
  reminder,
  reminderTime,
  reminderUnit,
  eventCategory,
  onEventTitleChange,
  onEventDescriptionChange,
  onEventAmountChange,
  onEventColorChange,
  onIsRecurringChange,
  onRecurrencePatternChange,
  onRecurrenceEndDateChange,
  onRecurrenceCountChange,
  onReminderChange,
  onReminderTimeChange,
  onReminderUnitChange,
  onEventCategoryChange,
  onSave,
  onDelete,
  isSaving,
  error
}) => {
  const isEditing = !!selectedEvent;
  const isCustomEvent = selectedEvent?.extendedProps.type === 'custom';

  const handleSave = useCallback(() => {
    onSave();
  }, [onSave]);

  const handleDelete = useCallback(() => {
    if (onDelete) {
      onDelete();
    }
  }, [onDelete]);

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {isEditing ? 'Edit Event' : 'Create New Event'}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Event Details for Non-Custom Events */}
          {isEditing && !isCustomEvent && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900">{selectedEvent.title}</h3>
                <span className="text-sm text-gray-500 capitalize">
                  {selectedEvent.extendedProps.type.replace('-', ' ')}
                </span>
              </div>
              
              {selectedEvent.extendedProps.amount && (
                <p className="text-sm text-gray-600 mb-2">
                  Amount: {formatCurrency(selectedEvent.extendedProps.amount)}
                </p>
              )}
              
              {selectedEvent.extendedProps.description && (
                <p className="text-sm text-gray-600 mb-2">
                  {selectedEvent.extendedProps.description}
                </p>
              )}
              
              {/* Loan Payment Details */}
              {selectedEvent.extendedProps.type === 'loan' && (
                <div className="grid grid-cols-2 gap-4 mt-3 text-sm">
                  {selectedEvent.extendedProps.principalPayment && (
                    <div>
                      <span className="text-gray-500">Principal:</span>{' '}
                      <span className="font-medium">
                        {formatCurrency(selectedEvent.extendedProps.principalPayment)}
                      </span>
                    </div>
                  )}
                  {selectedEvent.extendedProps.interestPayment && (
                    <div>
                      <span className="text-gray-500">Interest:</span>{' '}
                      <span className="font-medium">
                        {formatCurrency(selectedEvent.extendedProps.interestPayment)}
                      </span>
                    </div>
                  )}
                  {selectedEvent.extendedProps.remainingBalance && (
                    <div>
                      <span className="text-gray-500">Remaining Balance:</span>{' '}
                      <span className="font-medium">
                        {formatCurrency(selectedEvent.extendedProps.remainingBalance)}
                      </span>
                    </div>
                  )}
                  {selectedEvent.extendedProps.percentPaid && (
                    <div>
                      <span className="text-gray-500">Percent Paid:</span>{' '}
                      <span className="font-medium">
                        {selectedEvent.extendedProps.percentPaid.toFixed(1)}%
                      </span>
                    </div>
                  )}
                </div>
              )}

              <div className="mt-4 p-3 bg-blue-50 rounded-md">
                <p className="text-sm text-blue-800">
                  This is a system-generated event and cannot be edited directly. 
                  To modify this event, update the corresponding {selectedEvent.extendedProps.type} 
                  in the appropriate section of the app.
                </p>
              </div>
            </div>
          )}

          {/* Event Form for Custom Events */}
          {(!isEditing || isCustomEvent) && (
            <EventForm
              selectedDate={selectedDate}
              eventTitle={eventTitle}
              eventDescription={eventDescription}
              eventAmount={eventAmount}
              eventColor={eventColor}
              isRecurring={isRecurring}
              recurrencePattern={recurrencePattern}
              recurrenceEndDate={recurrenceEndDate}
              recurrenceCount={recurrenceCount}
              reminder={reminder}
              reminderTime={reminderTime}
              reminderUnit={reminderUnit}
              eventCategory={eventCategory}
              onEventTitleChange={onEventTitleChange}
              onEventDescriptionChange={onEventDescriptionChange}
              onEventAmountChange={onEventAmountChange}
              onEventColorChange={onEventColorChange}
              onIsRecurringChange={onIsRecurringChange}
              onRecurrencePatternChange={onRecurrencePatternChange}
              onRecurrenceEndDateChange={onRecurrenceEndDateChange}
              onRecurrenceCountChange={onRecurrenceCountChange}
              onReminderChange={onReminderChange}
              onReminderTimeChange={onReminderTimeChange}
              onReminderUnitChange={onReminderUnitChange}
              onEventCategoryChange={onEventCategoryChange}
            />
          )}

          {/* Error Message */}
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div>
            {isEditing && isCustomEvent && onDelete && (
              <button
                onClick={handleDelete}
                disabled={isSaving}
                className="px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 disabled:opacity-50"
              >
                Delete Event
              </button>
            )}
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={handleClose}
              disabled={isSaving}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
            >
              Cancel
            </button>
            
            {(!isEditing || isCustomEvent) && (
              <button
                onClick={handleSave}
                disabled={isSaving || !eventTitle.trim()}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving ? 'Saving...' : isEditing ? 'Update Event' : 'Create Event'}
              </button>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
});

EventModal.displayName = 'EventModal';

/**
 * Format currency for display
 */
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
}
