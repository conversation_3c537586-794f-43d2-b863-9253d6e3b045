/**
 * Improved Layout Component
 * Enhanced navigation with grouping, descriptions, and better UX
 */

import { useState } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import {
  LogOut,
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  Star,
  Zap
} from 'lucide-react';
import { useAuthStore } from '../store/localAuthStore';
import { navigationConfig, findNavigationItem } from '../config/navigation';

export function Layout() {
  const location = useLocation();
  const signOut = useAuthStore((state) => state.signOut);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [collapsedGroups, setCollapsedGroups] = useState<Set<string>>(
    new Set(
      navigationConfig
        .filter(group => group.isCollapsible && !group.defaultExpanded)
        .map(group => group.name)
    )
  );

  const toggleGroup = (groupName: string) => {
    setCollapsedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupName)) {
        newSet.delete(groupName);
      } else {
        newSet.add(groupName);
      }
      return newSet;
    });
  };

  const currentItem = findNavigationItem(location.pathname);

  const NavigationGroup = ({ group, isMobile = false }: { group: any; isMobile?: boolean }) => {
    const isCollapsed = group.isCollapsible && collapsedGroups.has(group.name);
    
    return (
      <div className={`${isMobile ? 'mb-4' : 'mb-6'}`}>
        {group.isCollapsible ? (
          <button
            onClick={() => toggleGroup(group.name)}
            className={`
              flex items-center justify-between w-full px-4 py-2 text-xs font-semibold 
              text-gray-500 dark:text-gray-400 uppercase tracking-wider hover:text-gray-700 
              dark:hover:text-gray-300 transition-colors
              ${isMobile ? 'px-2' : 'px-6'}
            `}
          >
            <span>{group.name}</span>
            {isCollapsed ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronUp className="h-4 w-4" />
            )}
          </button>
        ) : (
          <div className={`px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isMobile ? 'px-2' : 'px-6'}`}>
            {group.name}
          </div>
        )}
        
        {!isCollapsed && (
          <div className="space-y-1">
            {group.items.map((item: any) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.href;
              
              return (
                <div key={item.name} className="relative group">
                  <Link
                    to={item.href}
                    onClick={() => isMobile && setIsMobileMenuOpen(false)}
                    className={`
                      flex items-center py-2 text-sm rounded-md transition-all duration-200
                      ${isMobile ? 'px-4 mx-2' : 'px-6 mx-3'}
                      ${isActive
                        ? 'bg-indigo-50 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-400 shadow-sm'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700 hover:text-indigo-600 dark:hover:text-indigo-400'
                      }
                    `}
                  >
                    <div className="flex items-center flex-1">
                      <Icon className={`h-5 w-5 mr-3 ${isActive ? 'text-indigo-600 dark:text-indigo-400' : ''}`} />
                      <span className="font-medium">{item.name}</span>
                    </div>
                    
                    {/* Badges and indicators */}
                    <div className="flex items-center space-x-1">
                      {item.isNew && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                          New
                        </span>
                      )}
                      {item.isAdvanced && (
                        <div title="Advanced Feature">
                          <Zap className="h-3 w-3 text-amber-500" />
                        </div>
                      )}
                      {item.badge && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                          {item.badge}
                        </span>
                      )}
                    </div>
                  </Link>
                  
                  {/* Tooltip for descriptions */}
                  {item.description && !isMobile && (
                    <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 opacity-0 group-hover:opacity-100 transition-opacity delay-500 z-50 pointer-events-none">
                      <div className="bg-gray-900 dark:bg-gray-700 text-white text-xs rounded py-1 px-2 whitespace-nowrap shadow-lg">
                        {item.description}
                        <div className="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-full">
                          <div className="w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-900 dark:border-r-gray-700"></div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-900">
      {/* Mobile Header */}
      <div className="lg:hidden">
        <div className="flex items-center justify-between px-4 py-3 bg-white dark:bg-dark-800 shadow-sm">
          <div className="flex items-center space-x-3">
            <h1 className="text-xl font-bold text-indigo-600 dark:text-indigo-400">FinanceTrack</h1>
            {currentItem && (
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <span>•</span>
                <span className="ml-2">{currentItem.name}</span>
              </div>
            )}
          </div>
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="fixed inset-0 z-40 bg-black bg-opacity-25" onClick={() => setIsMobileMenuOpen(false)}>
            <div className="fixed inset-y-0 left-0 w-80 bg-white dark:bg-dark-800 shadow-lg overflow-y-auto" onClick={e => e.stopPropagation()}>
              <div className="h-full flex flex-col">
                {/* Mobile Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-dark-700">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Navigation</h2>
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
                
                {/* Mobile Navigation */}
                <div className="flex-1 overflow-y-auto py-4">
                  {navigationConfig.map((group) => (
                    <NavigationGroup key={group.name} group={group} isMobile={true} />
                  ))}
                </div>
                
                {/* Mobile Footer */}
                <div className="p-4 border-t border-gray-200 dark:border-dark-700">
                  <button
                    onClick={() => {
                      signOut();
                      setIsMobileMenuOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700 hover:text-indigo-600 dark:hover:text-indigo-400 rounded-md transition-colors"
                  >
                    <LogOut className="h-5 w-5 mr-3" />
                    Sign Out
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="flex h-screen">
        {/* Desktop Sidebar */}
        <div className="hidden lg:block w-72 bg-white dark:bg-dark-800 shadow-lg">
          {/* Header */}
          <div className="flex h-16 items-center justify-center border-b border-gray-200 dark:border-dark-700">
            <h1 className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">FinanceTrack</h1>
          </div>
          
          {/* Navigation */}
          <div className="flex-1 overflow-y-auto py-6">
            {navigationConfig.map((group) => (
              <NavigationGroup key={group.name} group={group} />
            ))}
          </div>
          
          {/* Footer */}
          <div className="absolute bottom-0 w-72 p-4 border-t border-gray-200 dark:border-dark-700 bg-white dark:bg-dark-800">
            <button
              onClick={() => signOut()}
              className="flex items-center px-6 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700 hover:text-indigo-600 dark:hover:text-indigo-400 w-full rounded-md transition-colors"
            >
              <LogOut className="h-5 w-5 mr-3" />
              Sign Out
            </button>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Page Header */}
          {currentItem && (
            <div className="hidden lg:block bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-8 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {currentItem.name}
                  </h1>
                  {currentItem.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {currentItem.description}
                    </p>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  {currentItem.isAdvanced && (
                    <div className="flex items-center px-3 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 rounded-full text-xs font-medium">
                      <Zap className="h-3 w-3 mr-1" />
                      Advanced
                    </div>
                  )}
                  {currentItem.isNew && (
                    <div className="flex items-center px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full text-xs font-medium">
                      <Star className="h-3 w-3 mr-1" />
                      New
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          
          {/* Main Content */}
          <main className="flex-1 overflow-y-auto p-4 lg:p-8">
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  );
}
