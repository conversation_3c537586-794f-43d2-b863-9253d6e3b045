/**
 * HelpTooltip Component
 * Provides contextual help and guidance throughout the app
 */

import { useState } from 'react';
import { HelpCircle, X, Info, Lightbulb, AlertCircle } from 'lucide-react';

interface HelpTooltipProps {
  content: string;
  title?: string;
  type?: 'info' | 'tip' | 'warning';
  size?: 'sm' | 'md' | 'lg';
  placement?: 'top' | 'bottom' | 'left' | 'right';
  className?: string;
}

export function HelpTooltip({ 
  content, 
  title, 
  type = 'info', 
  size = 'md', 
  placement = 'top',
  className = '' 
}: HelpTooltipProps) {
  const [isOpen, setIsOpen] = useState(false);

  const getIcon = () => {
    switch (type) {
      case 'tip':
        return <Lightbulb className="h-4 w-4" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4" />;
      case 'info':
      default:
        return <HelpCircle className="h-4 w-4" />;
    }
  };

  const getIconColor = () => {
    switch (type) {
      case 'tip':
        return 'text-yellow-500 hover:text-yellow-600';
      case 'warning':
        return 'text-orange-500 hover:text-orange-600';
      case 'info':
      default:
        return 'text-gray-400 hover:text-gray-600';
    }
  };

  const getTooltipColor = () => {
    switch (type) {
      case 'tip':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'warning':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      case 'info':
      default:
        return 'bg-gray-900 text-white';
    }
  };

  const getTooltipSize = () => {
    switch (size) {
      case 'sm':
        return 'max-w-xs text-xs';
      case 'lg':
        return 'max-w-md text-sm';
      case 'md':
      default:
        return 'max-w-sm text-sm';
    }
  };

  const getPlacementClasses = () => {
    switch (placement) {
      case 'bottom':
        return 'top-full mt-2 left-1/2 transform -translate-x-1/2';
      case 'left':
        return 'right-full mr-2 top-1/2 transform -translate-y-1/2';
      case 'right':
        return 'left-full ml-2 top-1/2 transform -translate-y-1/2';
      case 'top':
      default:
        return 'bottom-full mb-2 left-1/2 transform -translate-x-1/2';
    }
  };

  return (
    <div className={`relative inline-block ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        onMouseEnter={() => setIsOpen(true)}
        onMouseLeave={() => setIsOpen(false)}
        className={`transition-colors duration-200 ${getIconColor()}`}
        aria-label="Help information"
      >
        {getIcon()}
      </button>

      {isOpen && (
        <div
          className={`
            absolute z-50 p-3 rounded-lg shadow-lg border
            ${getTooltipColor()}
            ${getTooltipSize()}
            ${getPlacementClasses()}
            ${type === 'info' ? '' : 'border'}
          `}
          style={{ zIndex: 1000 }}
        >
          {title && (
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-sm">{title}</h4>
              <button
                onClick={() => setIsOpen(false)}
                className={`ml-2 ${type === 'info' ? 'text-gray-300 hover:text-white' : 'opacity-60 hover:opacity-100'}`}
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          )}
          <p className="leading-relaxed">{content}</p>
          
          {/* Arrow */}
          <div
            className={`
              absolute w-0 h-0
              ${placement === 'top' ? 'top-full left-1/2 transform -translate-x-1/2' : ''}
              ${placement === 'bottom' ? 'bottom-full left-1/2 transform -translate-x-1/2' : ''}
              ${placement === 'left' ? 'left-full top-1/2 transform -translate-y-1/2' : ''}
              ${placement === 'right' ? 'right-full top-1/2 transform -translate-y-1/2' : ''}
            `}
          >
            {placement === 'top' && (
              <div className={`border-l-4 border-r-4 border-t-4 border-transparent ${type === 'info' ? 'border-t-gray-900' : type === 'tip' ? 'border-t-yellow-200' : 'border-t-orange-200'}`} />
            )}
            {placement === 'bottom' && (
              <div className={`border-l-4 border-r-4 border-b-4 border-transparent ${type === 'info' ? 'border-b-gray-900' : type === 'tip' ? 'border-b-yellow-200' : 'border-b-orange-200'}`} />
            )}
            {placement === 'left' && (
              <div className={`border-t-4 border-b-4 border-l-4 border-transparent ${type === 'info' ? 'border-l-gray-900' : type === 'tip' ? 'border-l-yellow-200' : 'border-l-orange-200'}`} />
            )}
            {placement === 'right' && (
              <div className={`border-t-4 border-b-4 border-r-4 border-transparent ${type === 'info' ? 'border-r-gray-900' : type === 'tip' ? 'border-r-yellow-200' : 'border-r-orange-200'}`} />
            )}
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * PageHelp Component
 * Provides page-level help and guidance
 */
interface PageHelpProps {
  title: string;
  description: string;
  tips?: string[];
  warnings?: string[];
  className?: string;
}

export function PageHelp({ title, description, tips = [], warnings = [], className = '' }: PageHelpProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-medium text-blue-900">{title}</h3>
            <p className="text-blue-700 text-sm mt-1">{description}</p>
          </div>
        </div>
        {(tips.length > 0 || warnings.length > 0) && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium flex-shrink-0"
          >
            {isExpanded ? 'Show Less' : 'Learn More'}
          </button>
        )}
      </div>

      {isExpanded && (tips.length > 0 || warnings.length > 0) && (
        <div className="mt-4 pt-4 border-t border-blue-200">
          {tips.length > 0 && (
            <div className="mb-4">
              <h4 className="font-medium text-blue-900 mb-2 flex items-center">
                <Lightbulb className="h-4 w-4 mr-2" />
                Tips
              </h4>
              <ul className="space-y-1">
                {tips.map((tip, index) => (
                  <li key={index} className="text-blue-700 text-sm">
                    • {tip}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {warnings.length > 0 && (
            <div>
              <h4 className="font-medium text-blue-900 mb-2 flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                Important Notes
              </h4>
              <ul className="space-y-1">
                {warnings.map((warning, index) => (
                  <li key={index} className="text-blue-700 text-sm">
                    • {warning}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
