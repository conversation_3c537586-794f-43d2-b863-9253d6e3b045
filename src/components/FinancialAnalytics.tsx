import React, { useState, useEffect } from 'react';
import { format, parseISO, addMonths, isAfter, isBefore, isEqual } from 'date-fns';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line } from 'recharts';
import { localDataStore } from '../lib/local-data-store';
import { formatCurrency } from '../utils/currency';

interface AnalyticsProps {
  loans: any[];
  cds: any[];
  goals: any[];
  expenses: any[];
  customEvents: any[];
}

export function FinancialAnalytics({ loans, cds, goals, expenses, customEvents }: AnalyticsProps) {
  const [timeframe, setTimeframe] = useState<'month' | 'quarter' | 'year'>('month');
  const [cashFlowData, setCashFlowData] = useState<any[]>([]);
  const [upcomingObligations, setUpcomingObligations] = useState<any[]>([]);
  const [eventSummary, setEventSummary] = useState<any[]>([]);

  // Calculate financial analytics data when dependencies change
  useEffect(() => {
    generateCashFlowData();
    generateUpcomingObligations();
    generateEventSummary();
  }, [loans, cds, goals, expenses, customEvents, timeframe]);

  // Generate cash flow projection data
  const generateCashFlowData = () => {
    const today = new Date();
    const data = [];
    
    // Determine how many months to project based on timeframe
    const monthsToProject = timeframe === 'month' ? 1 : timeframe === 'quarter' ? 3 : 12;
    
    // Generate data for each month in the projection period
    for (let i = 0; i < monthsToProject; i++) {
      const currentMonth = addMonths(today, i);
      const monthLabel = format(currentMonth, 'MMM yyyy');
      
      // Calculate income for this month
      const income = calculateMonthlyIncome(currentMonth);
      
      // Calculate expenses for this month
      const expense = calculateMonthlyExpenses(currentMonth);
      
      // Calculate net cash flow
      const netCashFlow = income - expense;
      
      data.push({
        name: monthLabel,
        income: income,
        expenses: expense,
        netCashFlow: netCashFlow
      });
    }
    
    setCashFlowData(data);
  };

  // Calculate monthly income from custom events
  const calculateMonthlyIncome = (month: Date) => {
    const startOfMonth = new Date(month.getFullYear(), month.getMonth(), 1);
    const endOfMonth = new Date(month.getFullYear(), month.getMonth() + 1, 0);
    
    let totalIncome = 0;
    
    // Add income from custom events
    customEvents.forEach(event => {
      if (event.category === 'income') {
        const eventDate = parseISO(event.start_date);
        
        // Check if event falls within this month
        if (
          (isEqual(eventDate, startOfMonth) || isAfter(eventDate, startOfMonth)) && 
          (isEqual(eventDate, endOfMonth) || isBefore(eventDate, endOfMonth))
        ) {
          totalIncome += event.amount || 0;
        }
        
        // Handle recurring events
        if (event.isRecurring) {
          // Simplified recurrence handling for demo purposes
          if (event.recurrencePattern === 'monthly') {
            totalIncome += event.amount || 0;
          } else if (event.recurrencePattern === 'yearly' && 
                     eventDate.getMonth() === month.getMonth()) {
            totalIncome += event.amount || 0;
          }
        }
      }
    });
    
    return totalIncome;
  };

  // Calculate monthly expenses from loans, expenses, and custom events
  const calculateMonthlyExpenses = (month: Date) => {
    const startOfMonth = new Date(month.getFullYear(), month.getMonth(), 1);
    const endOfMonth = new Date(month.getFullYear(), month.getMonth() + 1, 0);
    
    let totalExpenses = 0;
    
    // Add loan payments
    loans.forEach(loan => {
      const nextPaymentDate = parseISO(loan.next_payment_date);
      
      // Check if payment falls within this month
      if (
        (isEqual(nextPaymentDate, startOfMonth) || isAfter(nextPaymentDate, startOfMonth)) && 
        (isEqual(nextPaymentDate, endOfMonth) || isBefore(nextPaymentDate, endOfMonth))
      ) {
        totalExpenses += loan.monthly_payment || 0;
      }
    });
    
    // Add expenses
    expenses.forEach(expense => {
      const expenseDate = parseISO(expense.date);
      
      // Check if expense falls within this month
      if (
        (isEqual(expenseDate, startOfMonth) || isAfter(expenseDate, startOfMonth)) && 
        (isEqual(expenseDate, endOfMonth) || isBefore(expenseDate, endOfMonth))
      ) {
        totalExpenses += expense.amount || 0;
      }
    });
    
    // Add custom expense events
    customEvents.forEach(event => {
      if (event.category === 'bills' || event.category === 'debt' || 
          event.category === 'taxes' || event.category === 'insurance') {
        const eventDate = parseISO(event.start_date);
        
        // Check if event falls within this month
        if (
          (isEqual(eventDate, startOfMonth) || isAfter(eventDate, startOfMonth)) && 
          (isEqual(eventDate, endOfMonth) || isBefore(eventDate, endOfMonth))
        ) {
          totalExpenses += event.amount || 0;
        }
        
        // Handle recurring events
        if (event.isRecurring) {
          // Simplified recurrence handling for demo purposes
          if (event.recurrencePattern === 'monthly') {
            totalExpenses += event.amount || 0;
          } else if (event.recurrencePattern === 'yearly' && 
                     eventDate.getMonth() === month.getMonth()) {
            totalExpenses += event.amount || 0;
          }
        }
      }
    });
    
    return totalExpenses;
  };

  // Generate upcoming financial obligations
  const generateUpcomingObligations = () => {
    const today = new Date();
    const threeMonthsFromNow = addMonths(today, 3);
    const obligations = [];
    
    // Add loan payments
    loans.forEach(loan => {
      const nextPaymentDate = parseISO(loan.next_payment_date);
      
      if (isAfter(nextPaymentDate, today) && isBefore(nextPaymentDate, threeMonthsFromNow)) {
        obligations.push({
          date: nextPaymentDate,
          formattedDate: format(nextPaymentDate, 'MMM d, yyyy'),
          title: `${loan.name} Payment`,
          amount: loan.monthly_payment,
          type: 'loan'
        });
      }
    });
    
    // Add CD maturities
    cds.forEach(cd => {
      const maturityDate = parseISO(cd.maturity_date);
      
      if (isAfter(maturityDate, today) && isBefore(maturityDate, threeMonthsFromNow)) {
        obligations.push({
          date: maturityDate,
          formattedDate: format(maturityDate, 'MMM d, yyyy'),
          title: `${cd.institution} CD Maturity`,
          amount: cd.principal,
          type: 'cd-maturity'
        });
      }
    });
    
    // Add custom events with financial impact
    customEvents.forEach(event => {
      if (event.amount && event.amount > 0) {
        const eventDate = parseISO(event.start_date);
        
        if (isAfter(eventDate, today) && isBefore(eventDate, threeMonthsFromNow)) {
          obligations.push({
            date: eventDate,
            formattedDate: format(eventDate, 'MMM d, yyyy'),
            title: event.title,
            amount: event.amount,
            type: 'custom',
            category: event.category
          });
        }
      }
    });
    
    // Sort by date
    obligations.sort((a, b) => a.date.getTime() - b.date.getTime());
    
    setUpcomingObligations(obligations);
  };

  // Generate event summary by category
  const generateEventSummary = () => {
    const categoryCounts: Record<string, number> = {};
    const categoryAmounts: Record<string, number> = {};
    
    // Count events and sum amounts by category
    customEvents.forEach(event => {
      const category = event.category || 'uncategorized';
      
      // Increment count
      categoryCounts[category] = (categoryCounts[category] || 0) + 1;
      
      // Add amount if available
      if (event.amount) {
        categoryAmounts[category] = (categoryAmounts[category] || 0) + event.amount;
      }
    });
    
    // Convert to array format for charts
    const summary = Object.keys(categoryCounts).map(category => ({
      name: category,
      count: categoryCounts[category],
      amount: categoryAmounts[category] || 0
    }));
    
    setEventSummary(summary);
  };

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 shadow-md rounded">
          <p className="font-medium text-sm">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {formatCurrency(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="bg-white dark:bg-dark-800 rounded-lg shadow-md p-4 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Financial Analytics</h3>
      
      {/* Timeframe selector */}
      <div className="mb-4">
        <div className="flex space-x-2">
          <button
            onClick={() => setTimeframe('month')}
            className={`px-3 py-1 text-sm rounded ${timeframe === 'month' ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700'}`}
          >
            Month
          </button>
          <button
            onClick={() => setTimeframe('quarter')}
            className={`px-3 py-1 text-sm rounded ${timeframe === 'quarter' ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700'}`}
          >
            Quarter
          </button>
          <button
            onClick={() => setTimeframe('year')}
            className={`px-3 py-1 text-sm rounded ${timeframe === 'year' ? 'bg-indigo-600 text-white' : 'bg-gray-200 text-gray-700'}`}
          >
            Year
          </button>
        </div>
      </div>
      
      {/* Cash Flow Projection */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">Cash Flow Projection</h4>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={cashFlowData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar dataKey="income" fill="#28a745" name="Income" />
              <Bar dataKey="expenses" fill="#dc3545" name="Expenses" />
              <Line type="monotone" dataKey="netCashFlow" stroke="#17a2b8" name="Net Cash Flow" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
      
      {/* Upcoming Financial Obligations */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">Upcoming Financial Obligations</h4>
        <div className="overflow-auto max-h-64">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-dark-600">
            <thead className="bg-gray-50 dark:bg-dark-700">
              <tr>
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Event</th>
                <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-dark-600">
              {upcomingObligations.length > 0 ? (
                upcomingObligations.map((obligation, index) => (
                  <tr key={index}>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{obligation.formattedDate}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">{obligation.title}</td>
                    <td className="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">{formatCurrency(obligation.amount)}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={3} className="px-3 py-4 text-sm text-center text-gray-500 dark:text-gray-400">No upcoming financial obligations</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      
      {/* Event Summary by Category */}
      <div>
        <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">Event Summary by Category</h4>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={eventSummary} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
              <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
              <Tooltip content={<CustomTooltip />} />
              <Legend />
              <Bar yAxisId="left" dataKey="count" fill="#8884d8" name="Event Count" />
              <Bar yAxisId="right" dataKey="amount" fill="#82ca9d" name="Total Amount" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
}
