/**
 * Enhanced Layout Component with Accessibility Features
 * Includes ARIA labels, semantic HTML, keyboard navigation, and screen reader support
 */

import { useState } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import {
  LogOut,
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  Star,
  Zap
} from 'lucide-react';
import { useAuthStore } from '../store/localAuthStore';
import { navigationConfig, findNavigationItem } from '../config/navigation';

export function Layout() {
  const location = useLocation();
  const signOut = useAuthStore((state) => state.signOut);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [collapsedGroups, setCollapsedGroups] = useState<Set<string>>(
    new Set(
      navigationConfig
        .filter(group => group.isCollapsible && !group.defaultExpanded)
        .map(group => group.name)
    )
  );

  const toggleGroup = (groupName: string) => {
    setCollapsedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupName)) {
        newSet.delete(groupName);
      } else {
        newSet.add(groupName);
      }
      return newSet;
    });
  };

  const currentItem = findNavigationItem(location.pathname);

  const NavigationGroup = ({ group, isMobile = false }: { group: any; isMobile?: boolean }) => {
    const isCollapsed = group.isCollapsible && collapsedGroups.has(group.name);
    const groupId = `nav-group-${group.name.toLowerCase().replace(/\s+/g, '-')}`;
    
    return (
      <div className={`${isMobile ? 'mb-4' : 'mb-6'}`} role="group" aria-labelledby={groupId}>
        {group.isCollapsible ? (
          <button
            id={groupId}
            onClick={() => toggleGroup(group.name)}
            className={`
              flex items-center justify-between w-full px-4 py-2 text-xs font-semibold 
              text-gray-500 dark:text-gray-400 uppercase tracking-wider hover:text-gray-700 
              dark:hover:text-gray-300 transition-colors focus:outline-none focus:ring-2 
              focus:ring-indigo-500 focus:ring-offset-2 rounded-md
              ${isMobile ? 'px-2' : 'px-6'}
            `}
            aria-expanded={!isCollapsed}
            aria-controls={`${groupId}-content`}
            aria-label={`${isCollapsed ? 'Expand' : 'Collapse'} ${group.name} navigation section`}
          >
            <span>{group.name}</span>
            {isCollapsed ? (
              <ChevronDown className="h-4 w-4" aria-hidden="true" />
            ) : (
              <ChevronUp className="h-4 w-4" aria-hidden="true" />
            )}
          </button>
        ) : (
          <div 
            id={groupId}
            className={`px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider ${isMobile ? 'px-2' : 'px-6'}`}
            role="heading"
            aria-level={2}
          >
            {group.name}
          </div>
        )}
        
        {!isCollapsed && (
          <ul 
            id={`${groupId}-content`}
            className="space-y-1"
            role="list"
            aria-labelledby={groupId}
          >
            {group.items.map((item: any) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.href;
              
              return (
                <li key={item.name} className="relative group" role="listitem">
                  <Link
                    to={item.href}
                    onClick={() => isMobile && setIsMobileMenuOpen(false)}
                    className={`
                      flex items-center py-2 text-sm rounded-md transition-all duration-200
                      focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2
                      ${isMobile ? 'px-4 mx-2' : 'px-6 mx-3'}
                      ${isActive
                        ? 'bg-indigo-50 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-400 shadow-sm'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700 hover:text-indigo-600 dark:hover:text-indigo-400'
                      }
                    `}
                    aria-current={isActive ? 'page' : undefined}
                    aria-label={`${item.name}${item.description ? `: ${item.description}` : ''}`}
                  >
                    <div className="flex items-center flex-1">
                      <Icon 
                        className={`h-5 w-5 mr-3 ${isActive ? 'text-indigo-600 dark:text-indigo-400' : ''}`} 
                        aria-hidden="true"
                      />
                      <span className="font-medium">{item.name}</span>
                    </div>
                    
                    {/* Badges and indicators */}
                    <div className="flex items-center space-x-1" aria-hidden="true">
                      {item.isNew && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                          New
                        </span>
                      )}
                      {item.isAdvanced && (
                        <div title="Advanced Feature">
                          <Zap className="h-3 w-3 text-amber-500" />
                        </div>
                      )}
                      {item.badge && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                          {item.badge}
                        </span>
                      )}
                    </div>
                  </Link>
                  
                  {/* Tooltip for descriptions */}
                  {item.description && !isMobile && (
                    <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 opacity-0 group-hover:opacity-100 transition-opacity delay-500 z-50 pointer-events-none">
                      <div className="bg-gray-900 dark:bg-gray-700 text-white text-xs rounded py-1 px-2 whitespace-nowrap shadow-lg">
                        {item.description}
                        <div className="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-full">
                          <div className="w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-900 dark:border-r-gray-700"></div>
                        </div>
                      </div>
                    </div>
                  )}
                </li>
              );
            })}
          </ul>
        )}
      </div>
    );
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-dark-900">
      {/* Sidebar */}
      <aside 
        className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 bg-white dark:bg-dark-800 shadow-lg"
        aria-label="Main navigation sidebar"
        role="navigation"
        id="navigation"
      >
        {/* Logo and Header */}
        <header className="flex items-center justify-between px-4 py-6 border-b border-gray-200 dark:border-dark-700">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
              <Star className="h-5 w-5 text-white" aria-hidden="true" />
            </div>
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
              Financial Advisor
            </h1>
          </div>
        </header>

        {/* Navigation Menu */}
        <nav className="flex-1 overflow-y-auto py-4 px-4" aria-label="Main navigation menu">
          {navigationConfig.map((group) => (
            <NavigationGroup 
              key={group.name} 
              group={group} 
              isMobile={false}
            />
          ))}
        </nav>

        {/* Footer */}
        <footer className="border-t border-gray-200 dark:border-dark-700 p-4">
          <button
            onClick={signOut}
            className="flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700 hover:text-indigo-600 dark:hover:text-indigo-400 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            aria-label="Sign out of your account"
          >
            <LogOut className="h-5 w-5 mr-3" aria-hidden="true" />
            Sign Out
          </button>
        </footer>
      </aside>

      {/* Main Content */}
      <div className="flex-1 lg:pl-64 flex flex-col">
        {/* Mobile Header */}
        <header className="lg:hidden bg-white dark:bg-dark-800 shadow-sm border-b border-gray-200 dark:border-dark-700">
          <div className="flex items-center justify-between px-4 py-3">
            {/* Current page indicator */}
            {currentItem && (
              <div className="flex items-center space-x-2">
                <currentItem.icon className="h-5 w-5 text-indigo-600 dark:text-indigo-400" aria-hidden="true" />
                <h1 className="text-lg font-semibold text-gray-900 dark:text-white">{currentItem.name}</h1>
              </div>
            )}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-md"
              aria-label={`${isMobileMenuOpen ? 'Close' : 'Open'} navigation menu`}
              aria-expanded={isMobileMenuOpen}
              aria-controls="mobile-navigation"
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" aria-hidden="true" />
              ) : (
                <Menu className="h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </header>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div 
            className="fixed inset-0 z-40 bg-black bg-opacity-25" 
            onClick={() => setIsMobileMenuOpen(false)}
            role="dialog"
            aria-modal="true"
            aria-labelledby="mobile-menu-title"
          >
            <div 
              id="mobile-navigation"
              className="fixed inset-y-0 left-0 w-80 bg-white dark:bg-dark-800 shadow-lg overflow-y-auto" 
              onClick={e => e.stopPropagation()}
              role="navigation"
              aria-label="Mobile navigation menu"
            >
              <div className="h-full flex flex-col">
                {/* Mobile Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-dark-700">
                  <h2 id="mobile-menu-title" className="text-lg font-semibold text-gray-900 dark:text-white">Navigation</h2>
                  <button
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-md"
                    aria-label="Close navigation menu"
                  >
                    <X className="h-5 w-5" aria-hidden="true" />
                  </button>
                </div>
                
                {/* Mobile Navigation */}
                <div className="flex-1 overflow-y-auto py-4">
                  {navigationConfig.map((group) => (
                    <NavigationGroup key={group.name} group={group} isMobile={true} />
                  ))}
                </div>
                
                {/* Mobile Footer */}
                <div className="p-4 border-t border-gray-200 dark:border-dark-700">
                  <button
                    onClick={() => {
                      signOut();
                      setIsMobileMenuOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700 hover:text-indigo-600 dark:hover:text-indigo-400 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    aria-label="Sign out of your account"
                  >
                    <LogOut className="h-5 w-5 mr-3" aria-hidden="true" />
                    Sign Out
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Main Content Area */}
        <main 
          id="main-content"
          className="flex-1 overflow-hidden bg-gray-50 dark:bg-dark-900"
          role="main"
          aria-label="Main content area"
          tabIndex={-1}
        >
          <div className="h-full overflow-y-auto">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
}
