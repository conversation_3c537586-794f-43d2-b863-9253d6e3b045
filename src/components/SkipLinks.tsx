import React from 'react'

interface SkipLinksProps {
  className?: string
}

/**
 * Skip Links component for keyboard navigation accessibility
 * Provides quick navigation to main content areas
 */
export const SkipLinks: React.FC<SkipLinksProps> = ({ className = '' }) => {
  return (
    <div 
      className={`skip-links sr-only focus-within:not-sr-only ${className}`}
      aria-label="Skip navigation links"
    >
      <a 
        href="#main-content" 
        className="skip-link absolute top-0 left-0 z-50 bg-blue-600 text-white px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform -translate-y-full focus:translate-y-0 transition-transform"
        aria-label="Skip to main content"
      >
        Skip to main content
      </a>
      <a 
        href="#navigation" 
        className="skip-link absolute top-0 left-20 z-50 bg-blue-600 text-white px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform -translate-y-full focus:translate-y-0 transition-transform"
        aria-label="Skip to navigation menu"
      >
        Skip to navigation
      </a>
      <a 
        href="#search" 
        className="skip-link absolute top-0 left-40 z-50 bg-blue-600 text-white px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform -translate-y-full focus:translate-y-0 transition-transform"
        aria-label="Skip to search"
      >
        Skip to search
      </a>
    </div>
  )
}

export default SkipLinks
