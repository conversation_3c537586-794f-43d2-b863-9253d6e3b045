import { useState, useEffect } from 'react';
import { Bell, Mail, Moon, Sun } from 'lucide-react';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { NotificationPreferences } from '../types/index';
import { useAuthStore } from '../store/authStore';
import { useThemeStore } from '../store/themeStore';

type NotificationSettings = {
  payment_reminders: boolean;
  monthly_reports: boolean;
  goal_updates: boolean;
};

export function Settings() {
  const user = useAuthStore((state) => state.user);
  const { data: preferences, updateItem, addItem } = useSupabaseQuery<NotificationPreferences>('notification_preferences');
  const { isDarkMode, toggleDarkMode } = useThemeStore();

  // Initialize default notification preferences
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    payment_reminders: false,
    monthly_reports: false,
    goal_updates: false
  });

  // Update notification settings when preferences data changes
  useEffect(() => {
    if (preferences?.[0]) {
      setNotificationSettings({
        payment_reminders: preferences[0].payment_reminders || false,
        monthly_reports: preferences[0].monthly_reports || false,
        goal_updates: preferences[0].goal_updates || false
      });
    }
  }, [preferences]);

  const handleNotificationChange = async (field: keyof NotificationSettings) => {
    const newValue = !notificationSettings[field];

    // Update local state immediately
    setNotificationSettings(prev => ({
      ...prev,
      [field]: newValue
    }));

    try {
      if (preferences?.[0]) {
        await updateItem(preferences[0].id, {
          [field]: newValue
        });
      } else {
        await addItem({
          user_id: user?.id!,
          payment_reminders: field === 'payment_reminders' ? newValue : false,
          goal_updates: field === 'goal_updates' ? newValue : false,
          monthly_reports: field === 'monthly_reports' ? newValue : false
        });
      }
    } catch (error) {
      // Revert local state on error
      setNotificationSettings(prev => ({
        ...prev,
        [field]: !newValue
      }));
      console.error('Error updating notification preferences:', error);
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>

      <div className="bg-white dark:bg-dark-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">
            Appearance
          </h3>
          <div className="mt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                {isDarkMode ? (
                  <Moon className="h-5 w-5 text-gray-400 dark:text-gray-300 mr-3" />
                ) : (
                  <Sun className="h-5 w-5 text-gray-400 dark:text-gray-300 mr-3" />
                )}
                <span className="text-sm text-gray-700 dark:text-gray-200">Dark Mode</span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={isDarkMode}
                  onChange={toggleDarkMode}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
              </label>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-dark-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">
            Notification Preferences
          </h3>
          <div className="mt-4 space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Bell className="h-5 w-5 text-gray-400 dark:text-gray-300 mr-3" />
                <span className="text-sm text-gray-700 dark:text-gray-200">Payment Reminders</span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.payment_reminders}
                  onChange={() => handleNotificationChange('payment_reminders')}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Mail className="h-5 w-5 text-gray-400 dark:text-gray-300 mr-3" />
                <span className="text-sm text-gray-700 dark:text-gray-200">Monthly Reports</span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.monthly_reports}
                  onChange={() => handleNotificationChange('monthly_reports')}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Bell className="h-5 w-5 text-gray-400 dark:text-gray-300 mr-3" />
                <span className="text-sm text-gray-700 dark:text-gray-200">Goal Updates</span>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={notificationSettings.goal_updates}
                  onChange={() => handleNotificationChange('goal_updates')}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}