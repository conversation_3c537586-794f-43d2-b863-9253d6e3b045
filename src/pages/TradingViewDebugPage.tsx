import { useState, useEffect } from 'react';
import { tradingViewService } from '../services/tradingViewService';
import { stockDataService } from '../services/stockDataService';

export default function TradingViewDebugPage() {
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [serverStatus, setServerStatus] = useState<string>('checking...');
  const [stockData, setStockData] = useState<any[]>([]);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    testConnection();
  }, []);

  const testConnection = async () => {
    addLog('🧪 Starting TradingView connection test...');
    
    try {
      // Test 1: Get debug info
      const info = tradingViewService.getDebugInfo();
      setDebugInfo(info);
      addLog(`📊 Debug info: ${JSON.stringify(info)}`);

      // Test 2: Check server availability
      addLog('🌐 Checking server availability...');
      const isAvailable = await tradingViewService.checkServerAvailability();
      setServerStatus(isAvailable ? 'Connected ✅' : 'Disconnected ❌');
      addLog(`🌐 Server status: ${isAvailable ? 'Available' : 'Not Available'}`);

      // Test 3: Try to get stock quotes
      if (isAvailable) {
        addLog('📈 Fetching stock data...');
        const symbols = ['COMI', 'SWDY', 'FWRY'];
        const quotes = await Promise.all(
          symbols.map(async (symbol) => {
            try {
              const quote = await tradingViewService.getStockQuote(symbol);
              addLog(`💰 ${symbol}: ${quote ? `EGP ${quote.price}` : 'No data'}`);
              return { symbol, quote };
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : String(error);
              addLog(`❌ Error getting ${symbol}: ${errorMessage}`);
              return { symbol, quote: null, error: errorMessage };
            }
          })
        );
        setStockData(quotes);
      }

      // Test 4: Test stock data service
      const dataSource = stockDataService.getDataSource();
      addLog(`📈 Stock data service source: ${dataSource}`);

    } catch (error) {
      addLog(`❌ Test failed: ${error}`);
    }
  };

  const refreshData = async () => {
    setLogs([]);
    await testConnection();
  };

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              🔧 TradingView Debug Panel
            </h1>
            <button
              onClick={refreshData}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              🔄 Refresh Test
            </button>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Server Status */}
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                Server Status
              </h2>
              <p className="text-2xl font-bold mb-2">
                <span className={serverStatus.includes('✅') ? 'text-green-600' : 'text-red-600'}>
                  {serverStatus}
                </span>
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                TradingView API Server: http://localhost:3000
              </p>
            </div>

            {/* Debug Info */}
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
                Service Debug Info
              </h2>
              {debugInfo ? (
                <pre className="text-xs bg-gray-800 text-green-400 p-2 rounded overflow-x-auto">
                  {JSON.stringify(debugInfo, null, 2)}
                </pre>
              ) : (
                <p className="text-gray-500">Loading...</p>
              )}
            </div>
          </div>
        </div>

        {/* Stock Data */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            📊 Live Stock Data
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {stockData.map(({ symbol, quote, error }) => (
              <div key={symbol} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 className="font-bold text-lg text-gray-900 dark:text-white">
                  {symbol}
                </h3>
                {quote ? (
                  <div className="space-y-1">
                    <p className="text-2xl font-bold text-blue-600">
                      EGP {quote.price.toFixed(2)}
                    </p>
                    <p className={`text-sm font-medium ${quote.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {quote.change >= 0 ? '+' : ''}{quote.change.toFixed(2)} 
                      ({quote.change_percentage.toFixed(2)}%)
                    </p>
                    <p className="text-xs text-gray-500">
                      Volume: {quote.volume.toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-500">
                      Updated: {new Date(quote.last_updated).toLocaleTimeString()}
                    </p>
                  </div>
                ) : (
                  <div>
                    <p className="text-red-600 font-medium">No Data</p>
                    {error && <p className="text-xs text-gray-500">{error}</p>}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Logs */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            📝 Test Logs
          </h2>
          <div className="bg-black p-4 rounded-lg max-h-96 overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index} className="text-green-400 text-sm font-mono">
                {log}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
