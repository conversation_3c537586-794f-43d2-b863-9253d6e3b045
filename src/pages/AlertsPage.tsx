import { useState, useEffect } from 'react';
import {
  Plus,
  Bell,
  BellOff,
  X,
  TrendingUp,
  Volume2,
  Calendar,
  AlertTriangle
} from 'lucide-react';
import { PageLoading, EmptyState } from '../components/LoadingStates';
import { Alert, Stock } from '../types/investment';
import { stockDataService } from '../services/stockDataService';

const mockAlerts: Alert[] = [
  {
    id: '1',
    user_id: 'user-1',
    stock_id: '1',
    type: 'price',
    condition: {
      operator: 'greater_than',
      value: 55,
      field: 'price'
    },
    active: true,
    created_at: '2024-01-10T00:00:00Z',
    updated_at: '2024-01-10T00:00:00Z'
  },
  {
    id: '2',
    user_id: 'user-1',
    stock_id: '1',
    type: 'volume',
    condition: {
      operator: 'greater_than',
      value: 2000000,
      field: 'volume'
    },
    active: false,
    triggered_at: '2024-01-12T10:30:00Z',
    created_at: '2024-01-08T00:00:00Z',
    updated_at: '2024-01-12T10:30:00Z'
  }
];

export default function AlertsPage() {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [stocks, setStocks] = useState<{ [key: string]: Stock }>({});
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newAlert, setNewAlert] = useState({
    stock_symbol: '',
    type: 'price' as Alert['type'],
    operator: 'greater_than' as Alert['condition']['operator'],
    value: '',
    field: 'price'
  });

  useEffect(() => {
    loadAlertsAndStocks();
  }, []);

  const loadAlertsAndStocks = async () => {
    console.log('🔔 Loading alerts and stock data...');
    setIsLoading(true);
    try {
      // Load alerts (using mock data for now)
      setAlerts(mockAlerts);
      
      // Load stock data for the alerts
      const stockMap: { [key: string]: Stock } = {};
      const allStocks = await stockDataService.getAllStocks();
      
      // Create a lookup map by stock ID and symbol
      allStocks.forEach(stock => {
        stockMap[stock.id] = stock;
        stockMap[stock.symbol] = stock;
      });
      
      setStocks(stockMap);
    } catch (error) {
      console.error('❌ Error loading alerts and stocks:', error);
      setAlerts([]);
      setStocks({});
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateAlert = () => {
    if (newAlert.stock_symbol && newAlert.value) {
      const alert: Alert = {
        id: Date.now().toString(),
        user_id: 'user-1',
        stock_id: '1', // Would find by symbol in real app
        type: newAlert.type,
        condition: {
          operator: newAlert.operator,
          value: parseFloat(newAlert.value),
          field: newAlert.field
        },
        active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      setAlerts([...alerts, alert]);
      setNewAlert({
        stock_symbol: '',
        type: 'price',
        operator: 'greater_than',
        value: '',
        field: 'price'
      });
      setShowCreateForm(false);
    }
  };

  const toggleAlert = (alertId: string) => {
    setAlerts(alerts.map(alert => 
      alert.id === alertId 
        ? { ...alert, active: !alert.active, updated_at: new Date().toISOString() }
        : alert
    ));
  };

  const deleteAlert = (alertId: string) => {
    setAlerts(alerts.filter(alert => alert.id !== alertId));
  };

  const getAlertIcon = (type: Alert['type']) => {
    switch (type) {
      case 'price': return TrendingUp;
      case 'volume': return Volume2;
      case 'technical': return AlertTriangle;
      case 'news': return Calendar;
      default: return Bell;
    }
  };

  const getAlertTypeColor = (type: Alert['type']) => {
    switch (type) {
      case 'price': return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-400';
      case 'volume': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400';
      case 'technical': return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-400';
      case 'news': return 'text-purple-600 bg-purple-100 dark:bg-purple-900 dark:text-purple-400';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400';
    }
  };

  const formatCondition = (alert: Alert) => {
    const operatorText = {
      'greater_than': 'above',
      'less_than': 'below',
      'equal_to': 'equals'
    };
    
    return `${alert.condition.field} ${operatorText[alert.condition.operator]} ${alert.condition.value.toLocaleString()}`;
  };

  if (isLoading) {
    return <PageLoading message="Loading alerts..." icon="financial" />;
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Price Alerts</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Set up alerts to be notified when stocks meet your criteria
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowCreateForm(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Alert
          </button>
        </div>
      </div>

      {/* Alert Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Alerts</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {alerts.filter(alert => alert.active).length}
              </p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
              <Bell className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Triggered Today</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {alerts.filter(alert => 
                  alert.triggered_at && 
                  new Date(alert.triggered_at).toDateString() === new Date().toDateString()
                ).length}
              </p>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
              <AlertTriangle className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Price Alerts</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {alerts.filter(alert => alert.type === 'price').length}
              </p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
              <TrendingUp className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Volume Alerts</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {alerts.filter(alert => alert.type === 'volume').length}
              </p>
            </div>
            <div className="p-3 bg-orange-100 dark:bg-orange-900 rounded-full">
              <Volume2 className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Create Alert Form */}
      {showCreateForm && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Create New Alert</h3>
            <button
              onClick={() => setShowCreateForm(false)}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Stock Symbol
              </label>
              <input
                type="text"
                value={newAlert.stock_symbol}
                onChange={(e) => setNewAlert({...newAlert, stock_symbol: e.target.value.toUpperCase()})}
                placeholder="e.g., COMI"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Alert Type
              </label>
              <select
                value={newAlert.type}
                onChange={(e) => setNewAlert({...newAlert, type: e.target.value as Alert['type']})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="price">Price</option>
                <option value="volume">Volume</option>
                <option value="technical">Technical</option>
                <option value="news">News</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Condition
              </label>
              <select
                value={newAlert.operator}
                onChange={(e) => setNewAlert({...newAlert, operator: e.target.value as Alert['condition']['operator']})}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="greater_than">Above</option>
                <option value="less_than">Below</option>
                <option value="equal_to">Equals</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Value
              </label>
              <input
                type="number"
                step="0.01"
                value={newAlert.value}
                onChange={(e) => setNewAlert({...newAlert, value: e.target.value})}
                placeholder="55.00"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={handleCreateAlert}
                className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
              >
                Create Alert
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Alerts List */}
      {alerts.length === 0 ? (
        <EmptyState 
          title="No alerts set up"
          description="Create your first alert to get notified when stocks meet your criteria"
          action={{
            label: "Create Your First Alert",
            onClick: () => setShowCreateForm(true)
          }}
        />
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Stock
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Condition
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Last Triggered
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {alerts.map((alert) => {
                  const IconComponent = getAlertIcon(alert.type);
                  return (
                    <tr key={alert.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className={`p-2 rounded-full mr-3 ${getAlertTypeColor(alert.type)}`}>
                            <IconComponent className="w-4 h-4" />
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {stocks[alert.stock_id]?.symbol || 'Unknown'}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {stocks[alert.stock_id]?.name || 'Unknown Stock'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${getAlertTypeColor(alert.type)}`}>
                          {alert.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {formatCondition(alert)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {alert.active ? (
                            <span className="px-2 py-1 text-xs font-medium rounded-full text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400">
                              Active
                            </span>
                          ) : alert.triggered_at ? (
                            <span className="px-2 py-1 text-xs font-medium rounded-full text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-400">
                              Triggered
                            </span>
                          ) : (
                            <span className="px-2 py-1 text-xs font-medium rounded-full text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400">
                              Inactive
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                        {new Date(alert.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                        {alert.triggered_at ? new Date(alert.triggered_at).toLocaleDateString() : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => toggleAlert(alert.id)}
                            className={`p-1 rounded ${alert.active ? 'text-orange-600 hover:text-orange-800' : 'text-green-600 hover:text-green-800'}`}
                            title={alert.active ? 'Disable alert' : 'Enable alert'}
                          >
                            {alert.active ? <BellOff className="w-4 h-4" /> : <Bell className="w-4 h-4" />}
                          </button>
                          <button
                            onClick={() => deleteAlert(alert.id)}
                            className="p-1 text-red-600 hover:text-red-800 dark:hover:text-red-300"
                            title="Delete alert"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
