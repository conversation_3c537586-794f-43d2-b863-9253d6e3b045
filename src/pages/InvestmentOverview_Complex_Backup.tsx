import { useState, useEffect } from 'react';
import { 
  Plus, 
  TrendingUp, 
  TrendingDown, 
  Briefcase, 
  DollarSign,
  PieChart,
  Activity,
  Eye,
  Calculator,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { PageLoading, EmptyState } from '../components/LoadingStates';
import { CreateRealPortfolioModal, AddStockToPortfolioModal } from '../components/RealPortfolioModals';
import { Portfolio, PortfolioSummary, MarketOverview } from '../types/investment';
import { realPortfolioService } from '../services/realPortfolioService';
import { stockDataService } from '../services/stockDataService';

interface InvestmentOverviewProps {}

export default function InvestmentOverview({}: InvestmentOverviewProps) {
  const [portfolios, setPortfolios] = useState<Portfolio[]>([]);
  const [portfolioSummaries, setPortfolioSummaries] = useState<PortfolioSummary[]>([]);
  const [marketOverview, setMarketOverview] = useState<MarketOverview | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showCreatePortfolio, setShowCreatePortfolio] = useState(false);
  const [showAddStock, setShowAddStock] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async (isRefresh = false) => {
    console.log('🔄 Loading real portfolio data...');
    if (isRefresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }
    setError(null);
    
    try {
      // Load portfolios with real calculated values
      console.log('📂 Loading portfolios...');
      const loadedPortfolios = await realPortfolioService.getPortfolios();
      setPortfolios(loadedPortfolios);
      console.log('✅ Portfolios loaded:', loadedPortfolios.length);

      // Load detailed summaries for each portfolio
      const summaries = [];
      for (const portfolio of loadedPortfolios) {
        try {
          const summary = await realPortfolioService.getPortfolioSummary(portfolio.id);
          summaries.push(summary);
          console.log(`📊 Summary for ${portfolio.name}:`, summary);
        } catch (error) {
          console.error(`❌ Error loading summary for ${portfolio.name}:`, error);
          // Continue with other portfolios even if one fails
        }
      }
      setPortfolioSummaries(summaries);

      // Load market overview
      try {
        console.log('📈 Loading market overview...');
        const overview = await stockDataService.getMarketOverview();
        setMarketOverview(overview);
        console.log('✅ Market overview loaded:', overview);
      } catch (error) {
        console.error('❌ Error loading market overview:', error);
        // Set a default market overview if the API fails
        setMarketOverview({
          market_status: 'closed',
          major_indices: [],
          market_sentiment: 'neutral',
          top_gainers: [],
          top_losers: [],
          most_active: []
        });
      }

      setLastUpdateTime(new Date());
      console.log('✅ All data loaded successfully');
    } catch (error) {
      console.error('❌ Error loading data:', error);
      setError('Failed to load portfolio data. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    loadData(true);
  };

  const handleCreatePortfolio = async (portfolioData: Omit<Portfolio, 'id' | 'user_id' | 'created_at' | 'updated_at' | 'current_balance'>) => {
    console.log('🎯 Creating real portfolio:', portfolioData);
    try {
      await realPortfolioService.createPortfolio(portfolioData);
      console.log('✅ Portfolio created, reloading data...');
      await loadData();
      console.log('🎉 Portfolio created and data reloaded successfully');
    } catch (error) {
      console.error('❌ Error creating portfolio:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      alert(`Error creating portfolio: ${errorMessage}. Please try again.`);
    }
  };

  const handleAddStock = async (transaction: {
    portfolio_id: string;
    symbol: string;
    shares: number;
    price: number;
    fees?: number;
    notes?: string;
  }) => {
    try {
      await realPortfolioService.buyStock({
        ...transaction,
        transaction_type: 'buy'
      });
      await loadData(); // Reload to see updated values
      console.log('✅ Stock added successfully');
    } catch (error) {
      console.error('❌ Error adding stock:', error);
      alert('Error adding stock. Please try again.');
    }
  };

  const calculateTotalValue = () => {
    return portfolioSummaries.reduce((total, summary) => total + summary.current_value, 0);
  };

  const calculateTotalInvested = () => {
    return portfolioSummaries.reduce((total, summary) => total + summary.total_invested, 0);
  };

  const calculateTotalGain = () => {
    return portfolioSummaries.reduce((total, summary) => total + summary.total_gain_loss, 0);
  };

  const calculateTotalGainPercentage = () => {
    const totalInvested = calculateTotalInvested();
    const totalGain = calculateTotalGain();
    return totalInvested > 0 ? (totalGain / totalInvested) * 100 : 0;
  };

  if (isLoading) {
    return <PageLoading message="Loading investment data..." icon="financial" />;
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Investment Portfolio</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your investment portfolios and track performance
          </p>
          {lastUpdateTime && (
            <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
              Last updated: {lastUpdateTime.toLocaleTimeString()}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-3">
          <button 
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button 
            onClick={() => setShowAddStock(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Stock
          </button>
          <button 
            onClick={() => setShowCreatePortfolio(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Portfolio
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <AlertCircle className="w-5 h-5 text-red-400 mr-3 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">Error Loading Data</h3>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">{error}</p>
              <button 
                onClick={() => loadData()}
                className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline mt-2"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Portfolio Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Value</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {calculateTotalValue().toLocaleString()} EGP
              </p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
              <DollarSign className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Gain/Loss</p>
              <p className={`text-2xl font-bold ${calculateTotalGain() >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {calculateTotalGain() >= 0 ? '+' : ''}{calculateTotalGain().toLocaleString()} EGP
              </p>
              <p className={`text-sm ${calculateTotalGain() >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {calculateTotalGain() >= 0 ? '+' : ''}{calculateTotalGainPercentage().toFixed(2)}%
              </p>
            </div>
            <div className={`p-3 rounded-full ${calculateTotalGain() >= 0 ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900'}`}>
              {calculateTotalGain() >= 0 ? 
                <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" /> :
                <TrendingDown className="w-6 h-6 text-red-600 dark:text-red-400" />
              }
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Portfolios</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {portfolios.length}
              </p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
              <Briefcase className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Market Status</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white capitalize">
                {marketOverview?.market_status}
              </p>
              <p className={`text-sm ${marketOverview?.market_sentiment === 'bullish' ? 'text-green-600' : marketOverview?.market_sentiment === 'bearish' ? 'text-red-600' : 'text-gray-600'}`}>
                {marketOverview?.market_sentiment} sentiment
              </p>
            </div>
            <div className="p-3 bg-orange-100 dark:bg-orange-900 rounded-full">
              <Activity className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Market Overview */}
      {marketOverview && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Market Overview</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {marketOverview.major_indices.map((index, i) => (
              <div key={i} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="font-medium text-gray-900 dark:text-white">{index.name}</h3>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {index.value.toLocaleString()}
                </p>
                <div className="flex items-center mt-1">
                  {index.change >= 0 ? (
                    <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                  ) : (
                    <TrendingDown className="w-4 h-4 text-red-600 mr-1" />
                  )}
                  <span className={`text-sm font-medium ${index.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {index.change >= 0 ? '+' : ''}{index.change.toFixed(2)} ({index.change_percentage >= 0 ? '+' : ''}{index.change_percentage.toFixed(2)}%)
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Debug Information (Development Only) */}
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
            🔧 Debug Information
          </h3>
          <button
            onClick={() => loadData()}
            className="px-3 py-1 text-xs bg-yellow-200 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md hover:bg-yellow-300 dark:hover:bg-yellow-700"
          >
            Refresh Data
          </button>
        </div>
        <div className="text-xs text-yellow-700 dark:text-yellow-300 space-y-1">
          <p><strong>Data Source:</strong> Live TradingView EGX Data</p>
          <p><strong>TradingView Debug:</strong> {JSON.stringify((stockDataService as any).getTradingViewDebugInfo?.() || 'N/A')}</p>
          <p><strong>Portfolios Loaded:</strong> {portfolios.length}</p>
          <p><strong>Market Data:</strong> {marketOverview ? 'Available' : 'Not Available'}</p>
        </div>
      </div>

      {/* Portfolio List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Your Portfolios</h2>
        </div>
        
        {portfolios.length === 0 ? (
          <EmptyState 
            title="No portfolios found"
            description="Create your first portfolio to start tracking your investments"
            action={{
              label: "Create Your First Portfolio",
              onClick: () => setShowCreatePortfolio(true)
            }}
          />
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {portfolios.map((portfolio) => {
              const gain = portfolio.current_balance - portfolio.initial_balance;
              const gainPercentage = (gain / portfolio.initial_balance) * 100;
              
              return (
                <div key={portfolio.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-full">
                          <PieChart className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                            {portfolio.name}
                          </h3>
                          {portfolio.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {portfolio.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-8">
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Value</p>
                        <p className="text-xl font-bold text-gray-900 dark:text-white">
                          {portfolio.current_balance.toLocaleString()} {portfolio.currency}
                        </p>
                      </div>
                      
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Gain/Loss</p>
                        <p className={`text-lg font-bold ${gain >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {gain >= 0 ? '+' : ''}{gain.toLocaleString()} {portfolio.currency}
                        </p>
                        <p className={`text-sm ${gain >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {gain >= 0 ? '+' : ''}{gainPercentage.toFixed(2)}%
                        </p>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">
                          <Eye className="w-5 h-5" />
                        </button>
                        <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">
                          <Calculator className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full text-left p-3 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors"
              onClick={() => setShowAddStock(true)}
            >
              <div className="flex items-center">
                <Plus className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3" />
                <span className="text-blue-600 dark:text-blue-400 font-medium">Add New Trade</span>
              </div>
            </button>
            <button className="w-full text-left p-3 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-colors">
              <div className="flex items-center">
                <Eye className="w-5 h-5 text-green-600 dark:text-green-400 mr-3" />
                <span className="text-green-600 dark:text-green-400 font-medium">View Watchlist</span>
              </div>
            </button>
            <button className="w-full text-left p-3 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-colors">
              <div className="flex items-center">
                <Activity className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-3" />
                <span className="text-purple-600 dark:text-purple-400 font-medium">Market Analysis</span>
              </div>
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Recent Activity</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">Bought COMI</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">2 hours ago</p>
              </div>
              <p className="text-sm font-medium text-green-600">+100 shares</p>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">Sold ETEL</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">1 day ago</p>
              </div>
              <p className="text-sm font-medium text-red-600">-50 shares</p>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">Added to Watchlist</p>
                <p className="text-xs text-gray-600 dark:text-gray-400">3 days ago</p>
              </div>
              <p className="text-sm font-medium text-blue-600">TALAAT</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Performance Summary</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-600 dark:text-gray-400">Best Performer</span>
                <span className="text-green-600 font-medium">+15.2%</span>
              </div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">Growth Portfolio</p>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-600 dark:text-gray-400">Total Return</span>
                <span className="text-green-600 font-medium">+{calculateTotalGainPercentage().toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full" 
                  style={{ width: `${Math.min(Math.abs(calculateTotalGainPercentage()), 100)}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <CreateRealPortfolioModal
        isOpen={showCreatePortfolio}
        onClose={() => setShowCreatePortfolio(false)}
        onSave={handleCreatePortfolio}
      />
      
      <AddStockToPortfolioModal
        isOpen={showAddStock}
        onClose={() => setShowAddStock(false)}
        onAddStock={handleAddStock}
        portfolios={portfolios}
      />
    </div>
  );
}
