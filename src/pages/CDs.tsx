import React, { useState } from 'react';
import { Plus<PERSON>ir<PERSON>, Pencil, Trash2 } from 'lucide-react';
import { format, differenceInDays, addMonths, differenceInMonths } from 'date-fns';
import { Modal } from '../components/Modal';
import { ConfirmDialog } from '../components/ConfirmDialog';
import { useAuthStore } from '../store/localAuthStore';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { CD } from '../types/index';
import { formatCurrency } from '../utils/currency';

function getMaturityPercentage(cd: CD): number {
  const daysToMaturity = differenceInDays(new Date(cd.maturity_date), new Date());
  const totalDays = differenceInDays(new Date(cd.maturity_date), addMonths(new Date(cd.maturity_date), -cd.term_months));
  return (1 - (daysToMaturity / totalDays)) * 100;
}

export function CDs() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedCD, setSelectedCD] = useState<CD | null>(null);
  const [institution, setInstitution] = useState('');
  const [principal, setPrincipal] = useState('');
  const [interestRate, setInterestRate] = useState('');
  const [startDate, setStartDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [endDate, setEndDate] = useState(format(addMonths(new Date(), 12), 'yyyy-MM-dd'));
  const user = useAuthStore((state) => state.user);

  const { data: cds, loading, addItem, deleteItem, updateItem } = useSupabaseQuery<CD>('cds', {
    orderBy: { column: 'created_at', ascending: false }
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const termMonths = differenceInMonths(end, start);

      if (termMonths <= 0) {
        throw new Error('End date must be after start date');
      }

      // Generate a name for the CD
      const cdName = `${institution} ${termMonths}-Month CD`;

      if (selectedCD) {
        await updateItem(selectedCD.id, {
          name: cdName,
          institution,
          principal: Number(principal),
          interest_rate: Number(interestRate),
          term_months: termMonths,
          maturity_date: endDate
        });
      } else {
        // Add a new CD using the addItem function from useSupabaseQuery
        await addItem({
          user_id: user?.id || 'default-user',
          name: cdName,
          institution,
          principal: Number(principal),
          interest_rate: Number(interestRate),
          term_months: termMonths,
          maturity_date: endDate
        });
      }

      setIsModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error saving CD:', error);
    }
  };

  const handleEdit = (cd: CD) => {
    setSelectedCD(cd);
    setInstitution(cd.institution);
    setPrincipal(cd.principal.toString());
    setInterestRate(cd.interest_rate.toString());
    setEndDate(format(new Date(cd.maturity_date), 'yyyy-MM-dd'));
    setStartDate(format(addMonths(new Date(cd.maturity_date), -cd.term_months), 'yyyy-MM-dd'));
    setIsModalOpen(true);
  };

  const handleDelete = (cd: CD) => {
    setSelectedCD(cd);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedCD) {
      try {
        await deleteItem(selectedCD.id);
      } catch (error) {
        console.error('Error deleting CD:', error);
      }
    }
  };

  const resetForm = () => {
    setSelectedCD(null);
    setInstitution('');
    setPrincipal('');
    setInterestRate('');
    setStartDate(format(new Date(), 'yyyy-MM-dd'));
    setEndDate(format(addMonths(new Date(), 12), 'yyyy-MM-dd'));
  };

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">CD Management</h1>
        <button
          onClick={() => {
            resetForm();
            setIsModalOpen(true);
          }}
          className="flex items-center px-4 py-2 bg-indigo-600 dark:bg-indigo-500 text-white rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600"
        >
          <PlusCircle className="w-5 h-5 mr-2" />
          Add CD
        </button>
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          resetForm();
        }}
        title={selectedCD ? "Edit CD" : "Add New CD"}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="institution" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Institution
            </label>
            <input
              type="text"
              id="institution"
              value={institution}
              onChange={(e) => setInstitution(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-500 shadow-sm focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-indigo-500 dark:focus:ring-indigo-400"
              required
            />
          </div>

          <div>
            <label htmlFor="principal" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Principal Amount
            </label>
            <input
              type="number"
              id="principal"
              value={principal}
              onChange={(e) => setPrincipal(e.target.value)}
              min="0"
              step="0.01"
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-500 shadow-sm focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-indigo-500 dark:focus:ring-indigo-400"
              required
            />
          </div>

          <div>
            <label htmlFor="interestRate" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Interest Rate (% APY)
            </label>
            <input
              type="number"
              id="interestRate"
              value={interestRate}
              onChange={(e) => setInterestRate(e.target.value)}
              min="0"
              step="0.01"
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-500 shadow-sm focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-indigo-500 dark:focus:ring-indigo-400"
              required
            />
          </div>

          <div>
            <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Start Date
            </label>
            <input
              type="date"
              id="startDate"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-500 shadow-sm focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-indigo-500 dark:focus:ring-indigo-400"
              required
            />
          </div>

          <div>
            <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              End Date (Maturity)
            </label>
            <input
              type="date"
              id="endDate"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-500 shadow-sm focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-indigo-500 dark:focus:ring-indigo-400"
              required
            />
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <button
              type="button"
              onClick={() => {
                setIsModalOpen(false);
                resetForm();
              }}
              className="px-4 py-2 border rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-indigo-600 dark:bg-indigo-500 text-white rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600"
            >
              {selectedCD ? "Save Changes" : "Add CD"}
            </button>
          </div>
        </form>
      </Modal>

      <ConfirmDialog
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="Delete CD"
        message="Are you sure you want to delete this CD? This action cannot be undone."
      />

      <div className="grid gap-6">
        {cds?.map((cd) => {
          return (
            <div key={cd.id} className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">{cd.institution}</h3>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {cd.term_months} months @ {cd.interest_rate}% APR
                  </p>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleEdit(cd)}
                    className="p-2 text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 rounded-full hover:bg-gray-100 dark:hover:bg-dark-700"
                  >
                    <Pencil className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => handleDelete(cd)}
                    className="p-2 text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-full hover:bg-gray-100 dark:hover:bg-dark-700"
                  >
                    <Trash2 className="h-5 w-5" />
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div>
                  <p className="text-gray-700 dark:text-gray-300 font-medium">Principal</p>
                  <p className="font-semibold text-gray-900 dark:text-white">{formatCurrency(cd.principal)}</p>
                </div>
                <div>
                  <p className="text-gray-700 dark:text-gray-300 font-medium">Maturity Date</p>
                  <p className="font-semibold text-gray-900 dark:text-white">
                    {format(new Date(cd.maturity_date), 'MMM d, yyyy')}
                  </p>
                </div>
                <div>
                  <p className="text-gray-700 dark:text-gray-300 font-medium">Annual Return</p>
                  <p className="font-semibold text-gray-900 dark:text-white">
                    {formatCurrency((cd.principal * cd.interest_rate) / 100)}
                  </p>
                </div>
                <div>
                  <p className="text-gray-700 dark:text-gray-300 font-medium">Term</p>
                  <p className="font-semibold text-gray-900 dark:text-white">{cd.term_months} months</p>
                </div>
              </div>

              <div className="mt-4">
                <div className="flex justify-between text-sm mb-1">
                  <span className="font-medium text-gray-800 dark:text-gray-200">Time to Maturity</span>
                  <span className="font-medium text-gray-800 dark:text-gray-200">{getMaturityPercentage(cd)}%</span>
                </div>
                <div className="h-2 bg-gray-200 dark:bg-dark-700 rounded-full">
                  <div
                    className="h-full bg-blue-500 dark:bg-blue-400 rounded-full"
                    style={{ width: `${getMaturityPercentage(cd)}%` }}
                  />
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}