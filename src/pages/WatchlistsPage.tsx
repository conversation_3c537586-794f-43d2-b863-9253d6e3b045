import { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Eye,
  X,
  TrendingUp,
  TrendingDown,
  Star,
  Bell,
  Trash2
} from 'lucide-react';
import { PageLoading, EmptyState } from '../components/LoadingStates';
import { Watchlist, WatchlistItem } from '../types/investment';
import { stockDataService } from '../services/stockDataService';

const mockWatchlists: Watchlist[] = [
  {
    id: '1',
    user_id: 'user-1',
    name: 'Banking Sector',
    description: 'Egyptian banking stocks to watch',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z'
  },
  {
    id: '2',
    user_id: 'user-1',
    name: 'High Growth Potential',
    description: 'Stocks with strong growth prospects',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z'
  }
];

const mockWatchlistItems: WatchlistItem[] = [
  {
    id: '1',
    watchlist_id: '1',
    stock_id: '1',
    notes: 'Strong Q4 performance expected',
    added_at: '2024-01-10T00:00:00Z',
    stock_details: undefined // Will be loaded dynamically
  }
];

export default function WatchlistsPage() {
  const [watchlists, setWatchlists] = useState<Watchlist[]>([]);
  const [selectedWatchlist, setSelectedWatchlist] = useState<Watchlist | null>(null);
  const [watchlistItems, setWatchlistItems] = useState<WatchlistItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newWatchlistName, setNewWatchlistName] = useState('');
  const [newWatchlistDescription, setNewWatchlistDescription] = useState('');

  useEffect(() => {
    loadWatchlists();
  }, []);

  const loadWatchlists = async () => {
    console.log('📋 Loading watchlists...');
    setIsLoading(true);
    try {
      // Simulate API call for now - in real app this would come from backend
      setWatchlists(mockWatchlists);
      if (mockWatchlists.length > 0) {
        setSelectedWatchlist(mockWatchlists[0]);
        await loadWatchlistItems(mockWatchlists[0].id);
      }
    } catch (error) {
      console.error('❌ Error loading watchlists:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadWatchlistItems = async (watchlistId: string) => {
    console.log(`📊 Loading watchlist items for watchlist: ${watchlistId}`);
    try {
      const items = mockWatchlistItems.filter(item => item.watchlist_id === watchlistId);
      
      // Load real stock data for each item
      const itemsWithStockData = await Promise.all(
        items.map(async (item) => {
          try {
            // Try to search for stocks that might match
            const searchResults = await stockDataService.searchStocks('COMI'); // Use a common EGX symbol
            const stockData = searchResults.length > 0 ? searchResults[0] : undefined;
            
            return {
              ...item,
              stock_details: stockData
            };
          } catch (error) {
            console.warn(`⚠️ Could not load stock data for item ${item.id}:`, error);
            return item;
          }
        })
      );
      
      setWatchlistItems(itemsWithStockData);
    } catch (error) {
      console.error('❌ Error loading watchlist items:', error);
      setWatchlistItems([]);
    }
  };

  useEffect(() => {
    if (selectedWatchlist) {
      loadWatchlistItems(selectedWatchlist.id);
    }
  }, [selectedWatchlist]);

  const handleCreateWatchlist = () => {
    if (newWatchlistName.trim()) {
      const newWatchlist: Watchlist = {
        id: Date.now().toString(),
        user_id: 'user-1',
        name: newWatchlistName,
        description: newWatchlistDescription,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      setWatchlists([...watchlists, newWatchlist]);
      setNewWatchlistName('');
      setNewWatchlistDescription('');
      setShowCreateForm(false);
      setSelectedWatchlist(newWatchlist);
    }
  };

  const handleDeleteWatchlist = (watchlistId: string) => {
    setWatchlists(watchlists.filter(w => w.id !== watchlistId));
    if (selectedWatchlist?.id === watchlistId) {
      setSelectedWatchlist(watchlists.length > 1 ? watchlists[0] : null);
    }
  };

  const removeFromWatchlist = (itemId: string) => {
    setWatchlistItems(watchlistItems.filter(item => item.id !== itemId));
  };

  if (isLoading) {
    return <PageLoading message="Loading watchlists..." icon="chart" />;
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Watchlists</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Track your favorite stocks and monitor their performance
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowCreateForm(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Watchlist
          </button>
        </div>
      </div>

      {/* Create Watchlist Form */}
      {showCreateForm && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Create New Watchlist</h3>
            <button
              onClick={() => setShowCreateForm(false)}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Watchlist Name
              </label>
              <input
                type="text"
                value={newWatchlistName}
                onChange={(e) => setNewWatchlistName(e.target.value)}
                placeholder="e.g., Tech Stocks, Dividend Champions"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description (Optional)
              </label>
              <textarea
                value={newWatchlistDescription}
                onChange={(e) => setNewWatchlistDescription(e.target.value)}
                placeholder="Describe what this watchlist is for..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowCreateForm(false)}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateWatchlist}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
              >
                Create Watchlist
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Watchlists Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Your Watchlists</h3>
            </div>
            {watchlists.length === 0 ? (
              <div className="p-6">
                <EmptyState 
                  title="No watchlists yet"
                  description="Create your first watchlist to start tracking stocks"
                  action={{
                    label: "Create Watchlist",
                    onClick: () => setShowCreateForm(true)
                  }}
                />
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {watchlists.map((watchlist) => (
                  <div
                    key={watchlist.id}
                    className={`p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${
                      selectedWatchlist?.id === watchlist.id ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-600' : ''
                    }`}
                    onClick={() => setSelectedWatchlist(watchlist)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {watchlist.name}
                        </h4>
                        {watchlist.description && (
                          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 truncate">
                            {watchlist.description}
                          </p>
                        )}
                        <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                          {mockWatchlistItems.filter(item => item.watchlist_id === watchlist.id).length} stocks
                        </p>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteWatchlist(watchlist.id);
                        }}
                        className="text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Watchlist Content */}
        <div className="lg:col-span-3">
          {selectedWatchlist ? (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                      {selectedWatchlist.name}
                    </h2>
                    {selectedWatchlist.description && (
                      <p className="text-gray-600 dark:text-gray-400 mt-1">
                        {selectedWatchlist.description}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-3">
                    <button className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                      <Search className="w-5 h-5" />
                    </button>
                    <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Stock
                    </button>
                  </div>
                </div>
              </div>

              {watchlistItems.length === 0 ? (
                <div className="p-6">
                  <EmptyState 
                    title="No stocks in this watchlist"
                    description="Add stocks to this watchlist to start tracking their performance"
                    action={{
                      label: "Browse Stocks",
                      onClick: () => console.log('Navigate to stocks')
                    }}
                  />
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Stock
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Price
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Change
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          AI Score
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Recommendation
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Notes
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {watchlistItems.map((item) => (
                        <tr key={item.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {item.stock_details?.symbol}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
                                {item.stock_details?.name}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {item.stock_details?.price.toFixed(2)} EGP
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className={`flex items-center ${item.stock_details?.change && item.stock_details.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {item.stock_details?.change && item.stock_details.change >= 0 ? (
                                <TrendingUp className="w-4 h-4 mr-1" />
                              ) : (
                                <TrendingDown className="w-4 h-4 mr-1" />
                              )}
                              <span className="text-sm font-medium">
                                {item.stock_details?.change && item.stock_details.change >= 0 ? '+' : ''}{item.stock_details?.change.toFixed(2)}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <Star className="w-4 h-4 text-yellow-400 mr-1" />
                              <span className="text-sm font-medium text-gray-900 dark:text-white">
                                {item.stock_details?.ai_score.toFixed(1)}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                              item.stock_details?.recommendation === 'Buy' ? 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400' :
                              item.stock_details?.recommendation === 'Sell' ? 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400' :
                              'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400'
                            }`}>
                              {item.stock_details?.recommendation}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 max-w-xs truncate">
                            {item.notes || '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center space-x-2">
                              <button 
                                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                                title="View details"
                              >
                                <Eye className="w-4 h-4" />
                              </button>
                              <button 
                                className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300"
                                title="Set alert"
                              >
                                <Bell className="w-4 h-4" />
                              </button>
                              <button 
                                onClick={() => removeFromWatchlist(item.id)}
                                className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                                title="Remove from watchlist"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <EmptyState 
                title="Select a watchlist"
                description="Choose a watchlist from the sidebar to view its contents, or create a new one to get started."
                action={{
                  label: "Create Your First Watchlist",
                  onClick: () => setShowCreateForm(true)
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
