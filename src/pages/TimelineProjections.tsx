import { useState, useEffect } from 'react';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { Loan, CD } from '../types/index';
import { useAuthStore } from '../store/localAuthStore';
import { formatCurrency } from '../utils/currency';
import { addMonths, format, compareAsc, differenceInMonths } from 'date-fns';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface Milestone {
  id: string;
  name: string;
  type: 'debt-free' | 'loan-paid' | 'net-worth' | 'savings';
  date: Date;
  value: number;
  description: string;
}

export function TimelineProjections() {
  const { user } = useAuthStore();
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [additionalPayment, setAdditionalPayment] = useState<number>(0);
  const [priorityLoan, setPriorityLoan] = useState<string>('');
  const [isCalculating, setIsCalculating] = useState<boolean>(false);
  const [debtFreeDate, setDebtFreeDate] = useState<Date | null>(null);
  const [debtFreeCountdown, setDebtFreeCountdown] = useState<number>(0);

  // Get all loans
  const { data: loans, loading: loansLoading } = useSupabaseQuery<Loan>('loans', {
    filters: user?.id ? [{ column: 'user_id', value: user.id }] : []
  });

  // Get all CDs
  const { data: cds, loading: cdsLoading } = useSupabaseQuery<CD>('cds', {
    filters: user?.id ? [{ column: 'user_id', value: user.id }] : []
  });

  // Calculate projections when loans or CDs change
  useEffect(() => {
    if (loans && loans.length > 0) {
      calculateProjections();
    }
  }, [loans, cds, additionalPayment, priorityLoan]);

  // Calculate all projections and milestones
  const calculateProjections = () => {
    if (!loans || loans.length === 0) return;

    setIsCalculating(true);

    // Clone loans to work with
    const loansCopy = JSON.parse(JSON.stringify(loans));

    // Sort loans by the selected priority (for applying additional payments)
    if (priorityLoan === 'highest-interest') {
      loansCopy.sort((a: Loan, b: Loan) => b.interest_rate - a.interest_rate);
    } else if (priorityLoan === 'smallest-balance') {
      loansCopy.sort((a: Loan, b: Loan) => a.remaining_balance - b.remaining_balance);
    }
    // 'proportional' doesn't need sorting

    // Set up milestone tracking
    const projectedMilestones: Milestone[] = [];
    const today = new Date();
    let simulationMonth = 0;
    let allPaidOff = false;
    const monthlyResults: {month: number, totalRemaining: number, date: Date}[] = [];

    // Continue until all loans are paid off or we reach 30 years
    while (!allPaidOff && simulationMonth < 360) {
      const currentDate = addMonths(today, simulationMonth);
      let totalRemainingBalance = 0;

      // Calculate extra payment distribution
      let extraPayment = additionalPayment;

      // Process each loan for this month
      for (let i = 0; i < loansCopy.length; i++) {
        const loan = loansCopy[i];
        if (loan.remaining_balance <= 0) continue;

        // Calculate interest and principal for this loan
        const monthlyRate = (loan.interest_rate / 100) / 12;
        const interestPayment = loan.remaining_balance * monthlyRate;

        // Determine payment amount based on strategy
        let paymentAmount = loan.monthly_payment;

        // Apply extra payment according to selected strategy
        if (priorityLoan === 'proportional') {
          // Distribute additional payment proportionally
          const proportion = loan.monthly_payment / loansCopy.reduce((sum: number, l: Loan) =>
            l.remaining_balance > 0 ? sum + l.monthly_payment : sum, 0);
          paymentAmount += extraPayment * proportion;
        } else if (i === 0 && extraPayment > 0) {
          // For highest-interest or smallest-balance, apply all extra to the first loan
          paymentAmount += extraPayment;
          extraPayment = 0;
        }

        // Calculate this month's principal payment
        const principalPayment = Math.min(paymentAmount - interestPayment, loan.remaining_balance);

        // Update loan
        const oldBalance = loan.remaining_balance;
        loan.remaining_balance -= principalPayment;

        // Check if loan was paid off this month
        if (oldBalance > 0 && loan.remaining_balance <= 0) {
          // Add loan payoff milestone
          projectedMilestones.push({
            id: `loan-paid-${loan.id}-${simulationMonth}`,
            name: `${loan.name} Paid Off`,
            type: 'loan-paid',
            date: currentDate,
            value: loan.principal,
            description: `${loan.name} loan will be completely paid off`
          });

          // If this loan is paid off, redistribute its payment to the next loan
          if (priorityLoan !== 'proportional') {
            extraPayment = loan.monthly_payment;
          }
        }

        // Add to total remaining balance
        totalRemainingBalance += Math.max(0, loan.remaining_balance);
      }

      // Record this month's results
      monthlyResults.push({
        month: simulationMonth,
        totalRemaining: totalRemainingBalance,
        date: currentDate
      });

      // Check if all loans are paid off
      allPaidOff = loansCopy.every((loan: Loan) => loan.remaining_balance <= 0);

      // If all loans are paid off, add debt-free milestone
      if (allPaidOff) {
        projectedMilestones.push({
          id: `debt-free-${simulationMonth}`,
          name: 'Completely Debt Free',
          type: 'debt-free',
          date: currentDate,
          value: 0,
          description: 'All loans will be completely paid off'
        });

        setDebtFreeDate(currentDate);
        setDebtFreeCountdown(simulationMonth);
      }

      // Move to next month
      simulationMonth++;
    }

    // Sort milestones by date
    projectedMilestones.sort((a, b) => compareAsc(a.date, b.date));

    setMilestones(projectedMilestones);
    setIsCalculating(false);
  };

  // Format the date difference for human readability
  const formatTimeDifference = (months: number): string => {
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;

    if (years === 0) {
      return `${remainingMonths} month${remainingMonths !== 1 ? 's' : ''}`;
    } else if (remainingMonths === 0) {
      return `${years} year${years !== 1 ? 's' : ''}`;
    } else {
      return `${years} year${years !== 1 ? 's' : ''} and ${remainingMonths} month${remainingMonths !== 1 ? 's' : ''}`;
    }
  };

  // Get chart data for projection visualization
  const getChartData = () => {
    if (!loans || loans.length === 0) return null;

    // Clone loans to work with
    const loansCopy = JSON.parse(JSON.stringify(loans));

    // Sort loans for the simulation
    if (priorityLoan === 'highest-interest') {
      loansCopy.sort((a: Loan, b: Loan) => b.interest_rate - a.interest_rate);
    } else if (priorityLoan === 'smallest-balance') {
      loansCopy.sort((a: Loan, b: Loan) => a.remaining_balance - b.remaining_balance);
    }

    const today = new Date();
    const projectionData: {date: Date, remainingBalance: number}[] = [];
    let simulationMonth = 0;
    let allPaidOff = false;
    let totalRemainingBalance = loansCopy.reduce((sum: number, loan: Loan) => sum + loan.remaining_balance, 0);

    // Add starting point
    projectionData.push({
      date: today,
      remainingBalance: totalRemainingBalance
    });

    // Continue until all loans are paid off or we reach 30 years
    while (!allPaidOff && simulationMonth < 360) {
      simulationMonth++;
      const currentDate = addMonths(today, simulationMonth);
      let extraPayment = additionalPayment;
      totalRemainingBalance = 0;

      // Process each loan for this month
      for (let i = 0; i < loansCopy.length; i++) {
        const loan = loansCopy[i];
        if (loan.remaining_balance <= 0) continue;

        // Calculate interest and principal for this loan
        const monthlyRate = (loan.interest_rate / 100) / 12;
        const interestPayment = loan.remaining_balance * monthlyRate;

        // Determine payment amount based on strategy
        let paymentAmount = loan.monthly_payment;

        // Apply extra payment according to selected strategy
        if (priorityLoan === 'proportional') {
          // Distribute additional payment proportionally
          const proportion = loan.monthly_payment / loansCopy.reduce((sum: number, l: Loan) =>
            l.remaining_balance > 0 ? sum + l.monthly_payment : sum, 0);
          paymentAmount += extraPayment * proportion;
        } else if (i === 0 && extraPayment > 0) {
          // For highest-interest or smallest-balance, apply all extra to the first loan
          paymentAmount += extraPayment;
          extraPayment = 0;
        }

        // Calculate this month's principal payment
        const principalPayment = Math.min(paymentAmount - interestPayment, loan.remaining_balance);

        // Update loan
        const oldBalance = loan.remaining_balance;
        loan.remaining_balance -= principalPayment;

        // Check if loan was paid off this month
        if (oldBalance > 0 && loan.remaining_balance <= 0) {
          // If this loan is paid off, redistribute its payment to the next loan
          if (priorityLoan !== 'proportional') {
            extraPayment = loan.monthly_payment;
          }
        }

        // Add to total remaining balance
        totalRemainingBalance += Math.max(0, loan.remaining_balance);
      }

      // Record this month's results if it's a quarter point (every 3 months)
      // or the final month of a loan payoff
      if (simulationMonth % 3 === 0 || loansCopy.some((loan: Loan) => loan.remaining_balance === 0)) {
        projectionData.push({
          date: currentDate,
          remainingBalance: totalRemainingBalance
        });
      }

      // Check if all loans are paid off
      allPaidOff = loansCopy.every((loan: Loan) => loan.remaining_balance <= 0);
    }

    // Add final point if not already added
    if (!allPaidOff) {
      projectionData.push({
        date: addMonths(today, simulationMonth),
        remainingBalance: totalRemainingBalance
      });
    }

    // Prepare chart data
    return {
      labels: projectionData.map(d => format(d.date, 'MMM yyyy')),
      datasets: [
        {
          label: 'Total Debt Remaining',
          data: projectionData.map(d => d.remainingBalance),
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
          fill: true
        }
      ]
    };
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Debt Reduction Projection',
        color: 'rgb(107, 114, 128)'
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `Total Debt: ${formatCurrency(context.parsed.y)}`;
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Date',
          color: 'rgb(107, 114, 128)'
        },
        ticks: {
          maxTicksLimit: 12
        }
      },
      y: {
        title: {
          display: true,
          text: 'Remaining Balance',
          color: 'rgb(107, 114, 128)'
        },
        ticks: {
          callback: function(value: any) {
            return formatCurrency(value);
          }
        }
      }
    }
  };

  // Loading state
  if (loansLoading || cdsLoading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  // No loans state
  if (!loans || loans.length === 0) {
    return (
      <div className="p-6 bg-white dark:bg-dark-800 rounded-lg shadow">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Timeline Projections</h2>
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-blue-800 dark:text-blue-200">
          <p>You don't have any active loans. Add loans on the Loan Management page to use this feature.</p>
        </div>
      </div>
    );
  }

  // Calculate total current debt
  const totalCurrentDebt = loans.reduce((sum, loan) => sum + loan.remaining_balance, 0);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Timeline Projections</h1>
      </div>

      {debtFreeDate && (
        <div className={`p-6 rounded-lg shadow-sm ${
          debtFreeCountdown <= 12
            ? 'bg-green-50 border border-green-200 dark:bg-green-900/20 dark:border-green-800'
            : 'bg-indigo-50 border border-indigo-200 dark:bg-indigo-900/20 dark:border-indigo-800'
        }`}>
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div>
              <h2 className={`text-xl font-bold ${
                debtFreeCountdown <= 12
                  ? 'text-green-800 dark:text-green-300'
                  : 'text-indigo-800 dark:text-indigo-300'
              }`}>
                Debt-Free Countdown
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                Based on your current payment strategy
              </p>
            </div>
            <div className="text-center">
              <div className={`text-4xl font-bold ${
                debtFreeCountdown <= 12
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-indigo-600 dark:text-indigo-400'
              }`}>
                {formatTimeDifference(debtFreeCountdown)}
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Debt-free by {format(debtFreeDate, 'MMMM d, yyyy')}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow space-y-6">
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Payment Strategy</h2>

          {/* Additional Payment Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Additional Monthly Payment
            </label>
            <div className="flex items-center">
              <input
                type="number"
                value={additionalPayment}
                onChange={(e) => setAdditionalPayment(Number(e.target.value))}
                min="0"
                step="1000"
                className="mt-1 block w-full sm:w-64 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-500 dark:text-white"
              />
            </div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              Enter additional amount to add to your monthly payments
            </p>
          </div>

          {/* Payment Priority Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Payment Priority
            </label>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={() => setPriorityLoan('proportional')}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  priorityLoan === 'proportional'
                    ? 'bg-indigo-100 text-indigo-800 border border-indigo-300 dark:bg-indigo-900 dark:text-indigo-100 dark:border-indigo-700'
                    : 'bg-white text-gray-700 border border-gray-300 dark:bg-dark-700 dark:text-gray-300 dark:border-dark-500'
                }`}
              >
                Proportional
              </button>
              <button
                onClick={() => setPriorityLoan('highest-interest')}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  priorityLoan === 'highest-interest'
                    ? 'bg-indigo-100 text-indigo-800 border border-indigo-300 dark:bg-indigo-900 dark:text-indigo-100 dark:border-indigo-700'
                    : 'bg-white text-gray-700 border border-gray-300 dark:bg-dark-700 dark:text-gray-300 dark:border-dark-500'
                }`}
              >
                Highest Interest First
              </button>
              <button
                onClick={() => setPriorityLoan('smallest-balance')}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  priorityLoan === 'smallest-balance'
                    ? 'bg-indigo-100 text-indigo-800 border border-indigo-300 dark:bg-indigo-900 dark:text-indigo-100 dark:border-indigo-700'
                    : 'bg-white text-gray-700 border border-gray-300 dark:bg-dark-700 dark:text-gray-300 dark:border-dark-500'
                }`}
              >
                Smallest Balance First
              </button>
            </div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              Select how to prioritize your additional payments
            </p>
          </div>

          <button
            onClick={calculateProjections}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            disabled={isCalculating}
          >
            {isCalculating ? 'Calculating...' : 'Update Projections'}
          </button>
        </div>

        {/* Debt Projection Chart */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Debt Reduction Projection</h3>
          <div className="h-80">
            <Line data={getChartData() as any} options={chartOptions} />
          </div>
        </div>

        {/* Milestone Section */}
        <div className="mt-8">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Projected Milestones</h3>

          {milestones.length === 0 ? (
            <div className="bg-gray-50 dark:bg-dark-700 p-4 rounded-lg text-center">
              <p className="text-gray-700 dark:text-gray-300">
                No milestones projected yet. Update your payment strategy to see your debt-free timeline.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {milestones.map((milestone) => (
                <div
                  key={milestone.id}
                  className={`p-4 rounded-lg border ${
                    milestone.type === 'debt-free'
                      ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                      : 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className={`text-lg font-semibold ${
                        milestone.type === 'debt-free'
                          ? 'text-green-800 dark:text-green-300'
                          : 'text-blue-800 dark:text-blue-300'
                      }`}>
                        {milestone.name}
                      </h4>
                      <p className="text-gray-600 dark:text-gray-300 mt-1">
                        {milestone.description}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-medium ${
                        milestone.type === 'debt-free'
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-blue-600 dark:text-blue-400'
                      }`}>
                        {format(milestone.date, 'MMM d, yyyy')}
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {formatTimeDifference(differenceInMonths(milestone.date, new Date()))} from now
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Current Loans Table */}
      <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Your Current Loans</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-dark-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Balance</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Monthly Payment</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Interest Rate</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Months Remaining</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-gray-700">
              {loans.map((loan) => (
                <tr key={loan.id} className="hover:bg-gray-50 dark:hover:bg-dark-700">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{loan.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">{formatCurrency(loan.remaining_balance)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">{formatCurrency(loan.monthly_payment)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">{loan.interest_rate}%</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">{loan.term_months}</td>
                </tr>
              ))}
              <tr className="bg-gray-50 dark:bg-dark-700">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900 dark:text-white">Total</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-right text-gray-900 dark:text-white">{formatCurrency(totalCurrentDebt)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-right text-gray-900 dark:text-white">{formatCurrency(loans.reduce((sum, loan) => sum + loan.monthly_payment, 0))}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">-</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">-</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
