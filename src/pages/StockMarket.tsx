import { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  TrendingUp,
  TrendingDown,
  Eye,
  Bell,
  Heart,
  BarChart3,
  Star,
  RefreshCw
} from 'lucide-react';
import { PageLoading, EmptyState } from '../components/LoadingStates';
import { Stock, InvestmentSearchFilters } from '../types/investment';
import { stockDataService } from '../services/stockDataService';

export default function StockMarket() {
  const [stocks, setStocks] = useState<Stock[]>([]);
  const [filteredStocks, setFilteredStocks] = useState<Stock[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<InvestmentSearchFilters>({});
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [sortBy, setSortBy] = useState<'symbol' | 'price' | 'change' | 'volume' | 'ai_score'>('symbol');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    loadStocks();
  }, []);

  const loadStocks = async () => {
    console.log('📊 Loading stocks from TradingView...');
    setIsLoading(true);
    try {
      const realStocks = await stockDataService.getAllStocks();
      console.log('✅ Loaded stocks:', realStocks);
      setStocks(realStocks);
      if (!isSearching) {
        setFilteredStocks(realStocks);
      }
    } catch (error) {
      console.error('❌ Error loading stocks:', error);
      // Fallback to empty array or show error message
      setStocks([]);
      setFilteredStocks([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async () => {
    if (searchQuery.trim()) {
      console.log(`🔍 Searching for: "${searchQuery}"`);
      setIsLoading(true);
      setIsSearching(true);
      try {
        const searchResults = await stockDataService.searchStocks(searchQuery.trim());
        console.log('📋 Search results:', searchResults.length, 'stocks found');
        setFilteredStocks(searchResults);
      } catch (error) {
        console.error('❌ Search error:', error);
        setFilteredStocks([]);
      } finally {
        setIsLoading(false);
      }
    } else {
      // If no search query, show all stocks
      setIsSearching(false);
      setFilteredStocks(stocks);
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    setIsSearching(false);
    setFilteredStocks(stocks);
  };

  useEffect(() => {
    // Only apply local filtering if we're not in search mode
    if (!isSearching) {
      let filtered = stocks.filter(stock => {
        const matchesRecommendation = !filters.recommendation || 
          stock.recommendation === filters.recommendation;
        
        const matchesPriceRange = (!filters.price_min || stock.price >= filters.price_min) &&
          (!filters.price_max || stock.price <= filters.price_max);
        
        return matchesRecommendation && matchesPriceRange;
      });

      // Sort filtered results
      filtered.sort((a, b) => {
        let aValue: number | string;
        let bValue: number | string;
        
        switch (sortBy) {
          case 'symbol':
            aValue = a.symbol;
            bValue = b.symbol;
            break;
          case 'price':
            aValue = a.price;
            bValue = b.price;
            break;
          case 'change':
            aValue = a.change;
            bValue = b.change;
            break;
          case 'volume':
            aValue = a.volume;
            bValue = b.volume;
            break;
          case 'ai_score':
            aValue = a.ai_score;
            bValue = b.ai_score;
            break;
          default:
            aValue = a.symbol;
            bValue = b.symbol;
        }
        
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
        } else {
          return sortOrder === 'asc' ? Number(aValue) - Number(bValue) : Number(bValue) - Number(aValue);
        }
      });

      setFilteredStocks(filtered);
    }
  }, [stocks, filters, sortBy, sortOrder, isSearching]);

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'Buy': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400';
      case 'Sell': return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400';
      case 'Hold': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400';
    }
  };

  const refreshData = async () => {
    console.log('🔄 Refreshing stock data...');
    await loadStocks();
  };

  if (isLoading) {
    return <PageLoading message="Loading stock data..." icon="chart" />;
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Stock Market</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Browse, analyze, and track Egyptian stocks
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button 
            onClick={refreshData}
            className="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
          >
            <RefreshCw className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative flex">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search stocks by symbol or name..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 border-r-0 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <button
                onClick={handleSearch}
                className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 border border-blue-600"
              >
                Search
              </button>
              <button
                onClick={clearSearch}
                className="px-3 py-2 bg-gray-500 text-white rounded-r-lg hover:bg-gray-600 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 border border-gray-500"
              >
                All
              </button>
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-4">
            <select
              value={filters.recommendation || ''}
              onChange={(e) => setFilters({...filters, recommendation: e.target.value as any})}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Recommendations</option>
              <option value="Buy">Buy</option>
              <option value="Hold">Hold</option>
              <option value="Sell">Sell</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="symbol">Symbol</option>
              <option value="price">Price</option>
              <option value="change">Change</option>
              <option value="volume">Volume</option>
              <option value="ai_score">AI Score</option>
            </select>

            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="p-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded ${viewMode === 'list' ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'}`}
              >
                <Filter className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'}`}
              >
                <BarChart3 className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stock List/Grid */}
      {filteredStocks.length === 0 ? (
        <EmptyState 
          title="No stocks found"
          description="No stocks match your search criteria. Try adjusting your filters."
        />
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
          {viewMode === 'list' ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Stock
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Price
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Change
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Volume
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      AI Score
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Recommendation
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredStocks.map((stock) => (
                    <tr key={stock.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {stock.symbol}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
                            {stock.name}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {(() => {
                          console.log(`💰 Rendering price for ${stock.symbol}: ${stock.price} (type: ${typeof stock.price})`);
                          return stock.price ? `${stock.price.toFixed(2)} EGP` : 'N/A';
                        })()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`flex items-center ${stock.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {stock.change >= 0 ? (
                            <TrendingUp className="w-4 h-4 mr-1" />
                          ) : (
                            <TrendingDown className="w-4 h-4 mr-1" />
                          )}
                          <span className="text-sm font-medium">
                            {stock.change >= 0 ? '+' : ''}{stock.change.toFixed(2)} ({((stock.change / (stock.price - stock.change)) * 100).toFixed(2)}%)
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {stock.volume.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-400 mr-1" />
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {stock.ai_score.toFixed(1)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRecommendationColor(stock.recommendation)}`}>
                          {stock.recommendation}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button 
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                            title="View detailed analysis"
                          >
                            <BarChart3 className="w-4 h-4" />
                          </button>
                          <button 
                            className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
                            title="Add to watchlist"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button 
                            className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300"
                            title="Set price alert"
                          >
                            <Bell className="w-4 h-4" />
                          </button>
                          <button 
                            className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                            title="Add to favorites"
                          >
                            <Heart className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
              {filteredStocks.map((stock) => (
                <div key={stock.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">{stock.symbol}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 truncate">{stock.name}</p>
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRecommendationColor(stock.recommendation)}`}>
                      {stock.recommendation}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {stock.price.toFixed(2)} EGP
                      </p>
                      <div className={`flex items-center ${stock.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {stock.change >= 0 ? (
                          <TrendingUp className="w-4 h-4 mr-1" />
                        ) : (
                          <TrendingDown className="w-4 h-4 mr-1" />
                        )}
                        <span className="text-sm font-medium">
                          {stock.change >= 0 ? '+' : ''}{stock.change.toFixed(2)} ({((stock.change / (stock.price - stock.change)) * 100).toFixed(2)}%)
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center mb-1">
                        <Star className="w-4 h-4 text-yellow-400 mr-1" />
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {stock.ai_score.toFixed(1)}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400">AI Score</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-4">
                    <span>Volume: {stock.volume.toLocaleString()}</span>
                    <span>MCap: {(stock.market_cap / 1000000000).toFixed(1)}B</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <button className="p-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                        <BarChart3 className="w-4 h-4" />
                      </button>
                      <button className="p-1 text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-1 text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300">
                        <Bell className="w-4 h-4" />
                      </button>
                    </div>
                    <button className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded">
                      Trade
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
