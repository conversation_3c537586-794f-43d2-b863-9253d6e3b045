import React, { useState } from 'react';
import { PlusCircle, Pencil, Trash2, Target, TrendingUp, AlertCircle } from 'lucide-react';
import { format, differenceInDays, parseISO } from 'date-fns';
import { Modal } from '../components/Modal';
import { ConfirmDialog } from '../components/ConfirmDialog';
import { useAuthStore } from '../store/localAuthStore';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { FinancialGoal } from '../types/index';
import { formatCurrency } from '../utils/currency';

const GOAL_CATEGORIES = [
  { value: 'savings', label: 'Savings', icon: Target },
  { value: 'debt', label: 'Debt Payoff', icon: Target },
  { value: 'investment', label: 'Investment', icon: TrendingUp },
  { value: 'emergency', label: 'Emergency Fund', icon: AlertCircle },
  { value: 'custom', label: 'Custom Goal', icon: Target }
];

const PRIORITIES = [
  { value: 'low', label: 'Low', color: 'bg-blue-100 text-blue-800' },
  { value: 'medium', label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'high', label: 'High', color: 'bg-red-100 text-red-800' }
];

export function Goals() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState<FinancialGoal | null>(null);
  const [name, setName] = useState('');
  const [targetAmount, setTargetAmount] = useState('');
  const [currentAmount, setCurrentAmount] = useState('');
  const [category, setCategory] = useState<FinancialGoal['category']>('savings');
  const [targetDate, setTargetDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [priority, setPriority] = useState<FinancialGoal['priority']>('medium');
  const [notes, setNotes] = useState('');
  const user = useAuthStore((state) => state.user);

  const { data: goals, loading, deleteItem, updateItem, addItem } = useSupabaseQuery<FinancialGoal>('financial_goals', {
    orderBy: { column: 'created_at', ascending: false }
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (selectedGoal) {
        await updateItem(selectedGoal.id, {
          name,
          target_amount: Number(targetAmount),
          current_amount: Number(currentAmount),
          category,
          target_date: targetDate,
          priority,
          notes
        });
      } else {
        await addItem({
          user_id: user?.id!,
          name,
          target_amount: Number(targetAmount),
          current_amount: Number(currentAmount),
          category,
          target_date: targetDate,
          priority,
          notes
        });
      }

      setIsModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error saving goal:', error);
    }
  };

  const handleEdit = (goal: FinancialGoal) => {
    setSelectedGoal(goal);
    setName(goal.name);
    setTargetAmount(goal.target_amount.toString());
    setCurrentAmount(goal.current_amount.toString());
    setCategory(goal.category);
    setTargetDate(format(new Date(goal.target_date), 'yyyy-MM-dd'));
    setPriority(goal.priority);
    setNotes(goal.notes || '');
    setIsModalOpen(true);
  };

  const handleDelete = (goal: FinancialGoal) => {
    setSelectedGoal(goal);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedGoal) {
      try {
        await deleteItem(selectedGoal.id);
        setIsDeleteModalOpen(false);
      } catch (error) {
        console.error('Error deleting goal:', error);
      }
    }
  };

  const resetForm = () => {
    setSelectedGoal(null);
    setName('');
    setTargetAmount('');
    setCurrentAmount('');
    setCategory('savings');
    setTargetDate(format(new Date(), 'yyyy-MM-dd'));
    setPriority('medium');
    setNotes('');
  };

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Financial Goals</h1>
        <button
          onClick={() => {
            resetForm();
            setIsModalOpen(true);
          }}
          className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          <PlusCircle className="w-5 h-5 mr-2" />
          Add Goal
        </button>
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          resetForm();
        }}
        title={selectedGoal ? "Edit Goal" : "Create New Goal"}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Goal Name
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700">
              Category
            </label>
            <select
              id="category"
              value={category}
              onChange={(e) => setCategory(e.target.value as FinancialGoal['category'])}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              {GOAL_CATEGORIES.map(cat => (
                <option key={cat.value} value={cat.value}>{cat.label}</option>
              ))}
            </select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="targetAmount" className="block text-sm font-medium text-gray-700">
                Target Amount
              </label>
              <input
                type="number"
                id="targetAmount"
                value={targetAmount}
                onChange={(e) => setTargetAmount(e.target.value)}
                min="0"
                step="0.01"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                required
              />
            </div>

            <div>
              <label htmlFor="currentAmount" className="block text-sm font-medium text-gray-700">
                Current Amount
              </label>
              <input
                type="number"
                id="currentAmount"
                value={currentAmount}
                onChange={(e) => setCurrentAmount(e.target.value)}
                min="0"
                step="0.01"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="targetDate" className="block text-sm font-medium text-gray-700">
                Target Date
              </label>
              <input
                type="date"
                id="targetDate"
                value={targetDate}
                onChange={(e) => setTargetDate(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                required
              />
            </div>

            <div>
              <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
                Priority
              </label>
              <select
                id="priority"
                value={priority}
                onChange={(e) => setPriority(e.target.value as FinancialGoal['priority'])}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              >
                {PRIORITIES.map(p => (
                  <option key={p.value} value={p.value}>{p.label}</option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
              Notes
            </label>
            <textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <button
              type="button"
              onClick={() => {
                setIsModalOpen(false);
                resetForm();
              }}
              className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              {selectedGoal ? "Save Changes" : "Create Goal"}
            </button>
          </div>
        </form>
      </Modal>

      <ConfirmDialog
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="Delete Goal"
        message="Are you sure you want to delete this goal? This action cannot be undone."
      />

      <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {goals?.map((goal) => {
          const progress = (goal.current_amount / goal.target_amount) * 100;
          const daysRemaining = differenceInDays(new Date(goal.target_date), new Date());
          const CategoryIcon = GOAL_CATEGORIES.find(cat => cat.value === goal.category)?.icon || Target;
          const priorityColor = PRIORITIES.find(p => p.value === goal.priority)?.color || '';

          return (
            <div key={goal.id} className="bg-white rounded-lg shadow-sm">
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-indigo-100 rounded-lg">
                      <CategoryIcon className="h-6 w-6 text-indigo-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{goal.name}</h3>
                      <p className="text-sm text-gray-500 capitalize">{goal.category}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => handleEdit(goal)}
                      className="p-2 text-gray-400 hover:text-indigo-600 rounded-full hover:bg-gray-100"
                    >
                      <Pencil className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleDelete(goal)}
                      className="p-2 text-gray-400 hover:text-red-600 rounded-full hover:bg-gray-100"
                    >
                      <Trash2 className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span>{progress.toFixed(1)}%</span>
                    </div>
                    <div className="h-2 bg-gray-200 rounded-full">
                      <div
                        className="h-full bg-indigo-600 rounded-full"
                        style={{ width: `${Math.min(progress, 100)}%` }}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Current Amount</p>
                      <p className="font-medium">{formatCurrency(goal.current_amount)}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Target Amount</p>
                      <p className="font-medium">{formatCurrency(goal.target_amount)}</p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center text-sm">
                    <div>
                      <p className="text-gray-500">Target Date</p>
                      <p className="font-medium">
                        {format(parseISO(goal.target_date), 'MMM d, yyyy')}
                      </p>
                      <p className="text-xs text-indigo-600">
                        {daysRemaining} days remaining
                      </p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs ${priorityColor}`}>
                      {goal.priority} priority
                    </span>
                  </div>

                  {goal.notes && (
                    <div className="text-sm">
                      <p className="text-gray-500">Notes</p>
                      <p className="mt-1 text-gray-700">{goal.notes}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {(!goals || goals.length === 0) && (
        <div className="text-center py-12">
          <Target className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No goals yet</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating your first financial goal.</p>
          <div className="mt-6">
            <button
              onClick={() => {
                resetForm();
                setIsModalOpen(true);
              }}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <PlusCircle className="h-5 w-5 mr-2" />
              New Goal
            </button>
          </div>
        </div>
      )}
    </div>
  );
}