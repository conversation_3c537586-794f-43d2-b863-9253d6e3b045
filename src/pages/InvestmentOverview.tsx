import { useState, useEffect } from 'react';
import { 
  Plus, 
  TrendingUp, 
  TrendingDown, 
  Briefcase,
  DollarSign,
  RefreshCw,
  Eye,
  PieChart,
  Edit,
  Trash2
} from 'lucide-react';
import { PageLoading, EmptyState } from '../components/LoadingStates';
import { CreateRealPortfolioModal, AddStockToPortfolioModal } from '../components/RealPortfolioModals';
import { Portfolio } from '../types/investment';
import { investmentDataManager } from '../services/investmentDataManager';

interface PortfolioSummary {
  portfolio: Portfolio;
  totalValue: number;
  totalCost: number;
  gainLoss: number;
  gainLossPercent: number;
  holdings: Array<{
    id: string;
    symbol: string;
    shares: number;
    averagePrice: number;
    currentPrice: number;
    value: number;
    gainLoss: number;
  }>;
}

export default function InvestmentOverview() {
  const [portfolioSummaries, setPortfolioSummaries] = useState<PortfolioSummary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showCreatePortfolio, setShowCreatePortfolio] = useState(false);
  const [showAddStock, setShowAddStock] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<string>('');

  useEffect(() => {
    loadPortfolios();
  }, []);

  const loadPortfolios = async (isRefresh = false) => {
    console.log('🔄 Loading portfolios...');
    console.log('🔍 Environment check - Desktop app:', typeof window !== 'undefined' && (window as any).pywebview);
    
    if (isRefresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      // Get portfolios from investmentDataManager
      const portfolios = await investmentDataManager.getPortfolios();
      console.log(`📂 Found ${portfolios.length} portfolios`);

      // Calculate summary for each portfolio
      const summaries: PortfolioSummary[] = [];
      
      for (const portfolio of portfolios) {
        try {
          const summary = await calculatePortfolioSummary(portfolio);
          summaries.push(summary);
          console.log(`✅ Calculated summary for ${portfolio.name}`);
        } catch (error) {
          console.error(`❌ Error calculating ${portfolio.name}:`, error);
        }
      }

      setPortfolioSummaries(summaries);
      setLastUpdate(new Date().toLocaleTimeString());
      console.log('✅ All portfolios loaded');
      
    } catch (error) {
      console.error('❌ Error loading portfolios:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const calculatePortfolioSummary = async (portfolio: Portfolio): Promise<PortfolioSummary> => {
    // Get holdings for this portfolio using investmentDataManager
    const holdings = await investmentDataManager.getPortfolioHoldings(portfolio.id);
    
    let totalValue = 0;
    let totalCost = 0;
    const holdingsWithPrices = [];

    for (const holding of holdings) {
      try {
        // Get current price from API
        const currentPrice = await getCurrentStockPrice(holding.symbol);
        
        const value = currentPrice * holding.shares;
        const cost = holding.average_cost * holding.shares;
        const gainLoss = value - cost;

        totalValue += value;
        totalCost += cost;

        holdingsWithPrices.push({
          id: holding.id,
          symbol: holding.symbol,
          shares: holding.shares,
          averagePrice: holding.average_cost,
          currentPrice: currentPrice,
          value: value,
          gainLoss: gainLoss
        });
        
      } catch (error) {
        console.error(`Error getting price for ${holding.symbol}:`, error);
        // Add holding with zero current price if API fails
        const cost = holding.average_cost * holding.shares;
        totalCost += cost;
        
        holdingsWithPrices.push({
          id: holding.id,
          symbol: holding.symbol,
          shares: holding.shares,
          averagePrice: holding.average_cost,
          currentPrice: 0,
          value: 0,
          gainLoss: -cost
        });
      }
    }

    const gainLoss = totalValue - totalCost;
    const gainLossPercent = totalCost > 0 ? (gainLoss / totalCost) * 100 : 0;

    return {
      portfolio,
      totalValue,
      totalCost,
      gainLoss,
      gainLossPercent,
      holdings: holdingsWithPrices
    };
  };

  const getCurrentStockPrice = async (symbol: string): Promise<number> => {
    try {
      const response = await fetch(`http://localhost:3000/search?query=${symbol}`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      
      // The API returns { stocks: [...] } format
      if (data.stocks && data.stocks.length > 0) {
        const stock = data.stocks.find((s: any) => s.symbol === symbol) || data.stocks[0];
        console.log(`💰 ${symbol} price: ${stock.price} EGP`);
        return parseFloat(stock.price) || 0;
      }
      
      console.warn(`⚠️ No price data found for ${symbol}`);
      return 0;
    } catch (error) {
      console.error(`❌ Error fetching price for ${symbol}:`, error);
      return 0;
    }
  };

  const handleCreatePortfolio = async (portfolioData: Omit<Portfolio, 'id' | 'user_id' | 'created_at' | 'updated_at' | 'current_balance'>) => {
    try {
      // Add current_balance for investmentDataManager
      const portfolioWithBalance = {
        ...portfolioData,
        current_balance: 0 // Default starting balance
      };
      await investmentDataManager.createPortfolio(portfolioWithBalance);
      await loadPortfolios();
      console.log('✅ Portfolio created successfully');
    } catch (error) {
      console.error('❌ Error creating portfolio:', error);
      alert('Error creating portfolio. Please try again.');
    }
  };

  const handleAddStock = async (transaction: {
    portfolio_id: string;
    symbol: string;
    shares: number;
    price: number;
    fees?: number;
    notes?: string;
  }) => {
    try {
      await investmentDataManager.addStockHolding({
        portfolio_id: transaction.portfolio_id,
        symbol: transaction.symbol,
        shares: transaction.shares,
        purchase_price: transaction.price,
        fees: transaction.fees || 0,
        // notes: transaction.notes // The addStockHolding method doesn't support notes directly
      });
      await loadPortfolios();
      console.log('✅ Stock added successfully');
    } catch (error) {
      console.error('❌ Error adding stock:', error);
      alert('Error adding stock. Please try again.');
    }
  };

  const handleRemoveStock = async (holdingId: string, symbol: string) => {
    if (!confirm(`Are you sure you want to remove ${symbol} from your portfolio?`)) {
      return;
    }

    try {
      console.log(`🗑️ Removing holding: ${holdingId} (${symbol})`);
      const success = await investmentDataManager.removeStockHolding(holdingId);
      
      if (success) {
        console.log('✅ Stock holding removed successfully');
        await loadPortfolios(true); // Refresh the data
      } else {
        throw new Error('Failed to remove holding');
      }
    } catch (error) {
      console.error('❌ Error removing stock holding:', error);
      alert('Error removing stock. Please try again.');
    }
  };

  const handleEditStock = async (holdingId: string, symbol: string, updates: any) => {
    try {
      console.log(`✏️ Updating holding: ${holdingId} (${symbol})`, updates);
      const result = await investmentDataManager.updateStockHolding(holdingId, updates);
      
      if (result) {
        console.log('✅ Stock holding updated successfully');
        await loadPortfolios(true); // Refresh the data
      } else {
        throw new Error('Failed to update holding');
      }
    } catch (error) {
      console.error('❌ Error updating stock holding:', error);
      alert('Error updating stock. Please try again.');
    }
  };

  // Calculate overall totals
  const overallTotalValue = portfolioSummaries.reduce((sum, p) => sum + p.totalValue, 0);
  const overallTotalCost = portfolioSummaries.reduce((sum, p) => sum + p.totalCost, 0);
  const overallGainLoss = overallTotalValue - overallTotalCost;
  const overallGainLossPercent = overallTotalCost > 0 ? (overallGainLoss / overallTotalCost) * 100 : 0;

  if (isLoading) {
    return <PageLoading message="Loading your portfolios..." icon="financial" />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">My Investment Portfolios</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Track your EGX stock investments with live prices
          </p>
          {lastUpdate && (
            <p className="text-xs text-gray-500 mt-1">
              Last updated: {lastUpdate}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-3">
          <button 
            onClick={() => loadPortfolios(true)}
            disabled={isRefreshing}
            className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg flex items-center transition-colors"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh Prices
          </button>
          <button 
            onClick={() => setShowAddStock(true)}
            disabled={portfolioSummaries.length === 0}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Stock
          </button>
          <button 
            onClick={() => setShowCreatePortfolio(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Portfolio
          </button>
        </div>
      </div>

      {/* Overall Summary - Only show if portfolios exist */}
      {portfolioSummaries.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Portfolio Value</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {overallTotalValue.toLocaleString()} EGP
                </p>
                <p className="text-sm text-gray-500">
                  Invested: {overallTotalCost.toLocaleString()} EGP
                </p>
              </div>
              <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                <DollarSign className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Gain/Loss</p>
                <p className={`text-2xl font-bold ${overallGainLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {overallGainLoss >= 0 ? '+' : ''}{overallGainLoss.toLocaleString()} EGP
                </p>
                <p className={`text-sm ${overallGainLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {overallGainLoss >= 0 ? '+' : ''}{overallGainLossPercent.toFixed(2)}%
                </p>
              </div>
              <div className={`p-3 rounded-full ${overallGainLoss >= 0 ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900'}`}>
                {overallGainLoss >= 0 ? 
                  <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" /> :
                  <TrendingDown className="w-6 h-6 text-red-600 dark:text-red-400" />
                }
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Portfolios</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {portfolioSummaries.length}
                </p>
                <p className="text-sm text-gray-500">
                  {portfolioSummaries.reduce((sum, p) => sum + p.holdings.length, 0)} stocks total
                </p>
              </div>
              <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
                <Briefcase className="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Portfolios List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Your Portfolios</h2>
        </div>
        
        {portfolioSummaries.length === 0 ? (
          <EmptyState 
            title="No portfolios yet"
            description="Create your first investment portfolio to start tracking your EGX stocks"
            action={{
              label: "Create Your First Portfolio",
              onClick: () => setShowCreatePortfolio(true)
            }}
          />
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {portfolioSummaries.map((summary) => (
              <div key={summary.portfolio.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-full">
                      <PieChart className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {summary.portfolio.name}
                      </h3>
                      {summary.portfolio.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {summary.portfolio.description}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-6">
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Value</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white">
                        {summary.totalValue.toLocaleString()} EGP
                      </p>
                    </div>
                    
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Gain/Loss</p>
                      <p className={`text-lg font-bold ${summary.gainLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {summary.gainLoss >= 0 ? '+' : ''}{summary.gainLoss.toLocaleString()} EGP
                      </p>
                      <p className={`text-sm ${summary.gainLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {summary.gainLoss >= 0 ? '+' : ''}{summary.gainLossPercent.toFixed(2)}%
                      </p>
                    </div>
                    
                    <button className="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400">
                      <Eye className="w-5 h-5" />
                    </button>
                  </div>
                </div>

                {/* Holdings Preview */}
                {summary.holdings.length > 0 && (
                  <div className="ml-10">
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      Holdings ({summary.holdings.length} stocks):
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {summary.holdings.map((holding) => (
                        <div key={holding.symbol} className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex-1">
                              <p className="font-medium text-gray-900 dark:text-white">{holding.symbol}</p>
                              <p className="text-xs text-gray-600 dark:text-gray-400">
                                {holding.shares} shares @ {holding.averagePrice.toFixed(2)} EGP
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm font-medium text-gray-900 dark:text-white">
                                {holding.currentPrice.toFixed(2)} EGP
                              </p>
                              <p className={`text-xs ${holding.gainLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {holding.gainLoss >= 0 ? '+' : ''}{holding.gainLoss.toFixed(0)} EGP
                              </p>
                            </div>
                          </div>
                          
                          {/* Action Buttons */}
                          <div className="flex items-center justify-end space-x-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                            <button
                              onClick={() => {
                                const newShares = prompt(`Edit shares for ${holding.symbol} (currently ${holding.shares}):`);
                                const newPrice = prompt(`Edit average price for ${holding.symbol} (currently ${holding.averagePrice.toFixed(2)} EGP):`);
                                
                                if (newShares && newPrice) {
                                  const shares = parseInt(newShares);
                                  const price = parseFloat(newPrice);
                                  
                                  if (shares > 0 && price > 0) {
                                    handleEditStock(holding.id, holding.symbol, {
                                      shares: shares,
                                      average_cost: price,
                                      total_cost: shares * price
                                    });
                                  } else {
                                    alert('Please enter valid positive numbers for shares and price.');
                                  }
                                }
                              }}
                              className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded"
                              title="Edit holding"
                            >
                              <Edit className="w-3 h-3" />
                            </button>
                            
                            <button
                              onClick={() => handleRemoveStock(holding.id, holding.symbol)}
                              className="p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded"
                              title="Remove holding"
                            >
                              <Trash2 className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modals */}
      <CreateRealPortfolioModal
        isOpen={showCreatePortfolio}
        onClose={() => setShowCreatePortfolio(false)}
        onSave={handleCreatePortfolio}
      />
      
      <AddStockToPortfolioModal
        isOpen={showAddStock}
        onClose={() => setShowAddStock(false)}
        onAddStock={handleAddStock}
        portfolios={portfolioSummaries.map(s => s.portfolio)}
      />
    </div>
  );
}
