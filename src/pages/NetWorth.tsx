import React, { useState } from 'react';
import { Plus<PERSON><PERSON><PERSON>, Pencil, Trash2, TrendingUp, TrendingDown, DollarSign } from 'lucide-react';
import { format } from 'date-fns';
import { Modal } from '../components/Modal';
import { ConfirmDialog } from '../components/ConfirmDialog';
import { HelpTooltip, PageHelp } from '../components/HelpTooltip';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { useAuthStore } from '../store/localAuthStore';
import { Asset, Liability } from '../types/index';
import { formatCurrency } from '../utils/currency';

const ASSET_CATEGORIES = [
  'Real Estate',
  'Vehicles',
  'Investments',
  'Collectibles',
  'Business',
  'Other'
];

const LIABILITY_CATEGORIES = [
  'Mortgage',
  'Auto Loan',
  'Credit Card',
  'Personal Loan',
  'Student Loan',
  'Other'
];

export function NetWorth() {
  const [isAssetModalOpen, setIsAssetModalOpen] = useState(false);
  const [isLiabilityModalOpen, setIsLiabilityModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [selectedLiability, setSelectedLiability] = useState<Liability | null>(null);
  const [itemToDelete, setItemToDelete] = useState<{ type: 'asset' | 'liability'; id: string } | null>(null);

  const { user } = useAuthStore();

  // Asset form state
  const [assetName, setAssetName] = useState('');
  const [assetCategory, setAssetCategory] = useState(ASSET_CATEGORIES[0]);
  const [assetValue, setAssetValue] = useState('');
  const [acquisitionDate, setAcquisitionDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [assetNotes, setAssetNotes] = useState('');

  // Liability form state
  const [liabilityName, setLiabilityName] = useState('');
  const [liabilityCategory, setLiabilityCategory] = useState(LIABILITY_CATEGORIES[0]);
  const [liabilityAmount, setLiabilityAmount] = useState('');
  const [interestRate, setInterestRate] = useState('');
  const [liabilityNotes, setLiabilityNotes] = useState('');

  const {
    data: assets,
    addItem: addAsset,
    updateItem: updateAsset,
    deleteItem: deleteAsset
  } = useSupabaseQuery<Asset>('assets');

  const {
    data: liabilities,
    addItem: addLiability,
    updateItem: updateLiability,
    deleteItem: deleteLiability
  } = useSupabaseQuery<Liability>('liabilities');

  const totalAssets = assets?.reduce((sum, asset) => sum + asset.value, 0) || 0;
  const totalLiabilities = liabilities?.reduce((sum, liability) => sum + liability.amount, 0) || 0;
  const netWorth = totalAssets - totalLiabilities;

  const handleAssetSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (selectedAsset) {
        await updateAsset(selectedAsset.id, {
          name: assetName,
          category: assetCategory,
          value: Number(assetValue),
          acquisition_date: acquisitionDate,
          notes: assetNotes
        });
      } else {
        await addAsset({
          user_id: user?.id || 'default-user',
          name: assetName,
          category: assetCategory,
          value: Number(assetValue),
          acquisition_date: acquisitionDate,
          notes: assetNotes
        });
      }
      setIsAssetModalOpen(false);
      resetAssetForm();
    } catch (error) {
      console.error('Error saving asset:', error);
    }
  };

  const handleLiabilitySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (selectedLiability) {
        await updateLiability(selectedLiability.id, {
          name: liabilityName,
          category: liabilityCategory,
          amount: Number(liabilityAmount),
          interest_rate: Number(interestRate),
          notes: liabilityNotes
        });
      } else {
        await addLiability({
          user_id: user?.id || 'default-user',
          name: liabilityName,
          category: liabilityCategory,
          amount: Number(liabilityAmount),
          interest_rate: Number(interestRate),
          notes: liabilityNotes
        });
      }
      setIsLiabilityModalOpen(false);
      resetLiabilityForm();
    } catch (error) {
      console.error('Error saving liability:', error);
    }
  };

  const handleDelete = async () => {
    if (!itemToDelete) return;

    try {
      if (itemToDelete.type === 'asset') {
        await deleteAsset(itemToDelete.id);
      } else {
        await deleteLiability(itemToDelete.id);
      }
      setIsDeleteModalOpen(false);
      setItemToDelete(null);
    } catch (error) {
      console.error('Error deleting item:', error);
    }
  };

  const resetAssetForm = () => {
    setSelectedAsset(null);
    setAssetName('');
    setAssetCategory(ASSET_CATEGORIES[0]);
    setAssetValue('');
    setAcquisitionDate(format(new Date(), 'yyyy-MM-dd'));
    setAssetNotes('');
  };

  const resetLiabilityForm = () => {
    setSelectedLiability(null);
    setLiabilityName('');
    setLiabilityCategory(LIABILITY_CATEGORIES[0]);
    setLiabilityAmount('');
    setInterestRate('');
    setLiabilityNotes('');
  };

  return (
    <div className="space-y-6">
      {/* Header with Help */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center space-x-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              💰 Net Worth Tracker
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Track your assets and liabilities to monitor financial health
            </p>
          </div>
          <HelpTooltip content="Net Worth = Total Assets - Total Liabilities. Track your progress over time by recording all your assets and debts." />
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={() => {
              resetAssetForm();
              setIsAssetModalOpen(true);
            }}
            className="flex items-center px-4 py-2 bg-emerald-600 dark:bg-emerald-700 text-white rounded-lg hover:bg-emerald-700 dark:hover:bg-emerald-600 transition-colors shadow-sm"
          >
            <PlusCircle className="w-5 h-5 mr-2" />
            Add Asset
          </button>
          <button
            onClick={() => {
              resetLiabilityForm();
              setIsLiabilityModalOpen(true);
            }}
            className="flex items-center px-4 py-2 bg-rose-600 dark:bg-rose-700 text-white rounded-lg hover:bg-rose-700 dark:hover:bg-rose-600 transition-colors shadow-sm"
          >
            <PlusCircle className="w-5 h-5 mr-2" />
            Add Liability
          </button>
        </div>
      </div>

      {/* Page Help */}
      <PageHelp
        title="Net Worth Tracking"
        description="Monitor your financial health by tracking assets and liabilities over time."
        tips={[
          "Add all your assets including real estate, investments, vehicles, and valuables",
          "Record all debts and liabilities with current balances and interest rates",
          "Your net worth is automatically calculated as Assets - Liabilities",
          "Update values regularly to track your financial progress",
          "Use categories to organize and analyze different types of assets and debts"
        ]}
        warnings={[
          "Keep asset valuations realistic and up-to-date",
          "Don't forget to include all debts for an accurate picture"
        ]}
      />

      {/* Net Worth Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 dark:from-emerald-600 dark:to-emerald-700 p-6 rounded-lg shadow-sm text-white">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-emerald-100 font-medium text-sm">Total Assets</h3>
            <div className="p-2 bg-emerald-400/20 rounded-lg">
              <TrendingUp className="h-4 w-4 text-emerald-100" />
            </div>
          </div>
          <p className="text-3xl font-bold mb-1">{formatCurrency(totalAssets)}</p>
          <p className="text-emerald-200 text-sm">{assets?.length || 0} asset{assets?.length !== 1 ? 's' : ''}</p>
        </div>

        <div className="bg-gradient-to-br from-rose-500 to-rose-600 dark:from-rose-600 dark:to-rose-700 p-6 rounded-lg shadow-sm text-white">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-rose-100 font-medium text-sm">Total Liabilities</h3>
            <div className="p-2 bg-rose-400/20 rounded-lg">
              <TrendingDown className="h-4 w-4 text-rose-100" />
            </div>
          </div>
          <p className="text-3xl font-bold mb-1">{formatCurrency(totalLiabilities)}</p>
          <p className="text-rose-200 text-sm">{liabilities?.length || 0} liabilit{liabilities?.length !== 1 ? 'ies' : 'y'}</p>
        </div>

        <div className={`bg-gradient-to-br ${netWorth >= 0 ? 'from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700' : 'from-orange-500 to-orange-600 dark:from-orange-600 dark:to-orange-700'} p-6 rounded-lg shadow-sm text-white`}>
          <div className="flex items-center justify-between mb-2">
            <h3 className={`${netWorth >= 0 ? 'text-blue-100' : 'text-orange-100'} font-medium text-sm`}>Net Worth</h3>
            <div className={`p-2 ${netWorth >= 0 ? 'bg-blue-400/20' : 'bg-orange-400/20'} rounded-lg`}>
              <DollarSign className={`h-4 w-4 ${netWorth >= 0 ? 'text-blue-100' : 'text-orange-100'}`} />
            </div>
          </div>
          <p className="text-3xl font-bold mb-1">{formatCurrency(netWorth)}</p>
          <p className={`${netWorth >= 0 ? 'text-blue-200' : 'text-orange-200'} text-sm`}>
            {netWorth >= 0 ? 'Positive net worth' : 'Negative net worth'}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Assets List */}
        <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white flex items-center gap-2">
              📈 Assets
            </h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {assets?.map(asset => {
                const getCategoryIcon = (category: string) => {
                  switch (category) {
                    case 'Real Estate': return '🏠';
                    case 'Vehicles': return '🚗';
                    case 'Investments': return '💼';
                    case 'Collectibles': return '🎨';
                    case 'Business': return '🏢';
                    default: return '📦';
                  }
                };

                return (
                  <div key={asset.id} className="p-4 bg-emerald-50 dark:bg-emerald-900/10 rounded-lg border border-emerald-200 dark:border-emerald-800 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <div className="p-2 bg-emerald-100 dark:bg-emerald-900/20 rounded-lg">
                          <span className="text-lg">{getCategoryIcon(asset.category)}</span>
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 dark:text-white">{asset.name}</h3>
                          <p className="text-sm text-emerald-600 dark:text-emerald-400 font-medium">{asset.category}</p>
                          <p className="text-xl font-bold text-emerald-600 dark:text-emerald-400 mt-1">{formatCurrency(asset.value)}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Acquired: {format(new Date(asset.acquisition_date), 'MMM d, yyyy')}
                          </p>
                          {asset.notes && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">{asset.notes}</p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-1 ml-2">
                        <button
                          onClick={() => {
                            setSelectedAsset(asset);
                            setAssetName(asset.name);
                            setAssetCategory(asset.category);
                            setAssetValue(asset.value.toString());
                            setAcquisitionDate(format(new Date(asset.acquisition_date), 'yyyy-MM-dd'));
                            setAssetNotes(asset.notes || '');
                            setIsAssetModalOpen(true);
                          }}
                          className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            setItemToDelete({ type: 'asset', id: asset.id });
                            setIsDeleteModalOpen(true);
                          }}
                          className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
              {(!assets || assets.length === 0) && (
                <div className="text-center py-8">
                  <div className="mx-auto w-16 h-16 bg-emerald-100 dark:bg-emerald-900/20 rounded-full flex items-center justify-center mb-4">
                    <TrendingUp className="h-8 w-8 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No assets yet</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">Start building your wealth by adding your first asset</p>
                  <button
                    onClick={() => {
                      resetAssetForm();
                      setIsAssetModalOpen(true);
                    }}
                    className="inline-flex items-center px-4 py-2 bg-emerald-600 dark:bg-emerald-700 text-white rounded-lg hover:bg-emerald-700 dark:hover:bg-emerald-600 transition-colors"
                  >
                    <PlusCircle className="w-4 h-4 mr-2" />
                    Add Your First Asset
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Liabilities List */}
        <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white flex items-center gap-2">
              📉 Liabilities
            </h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {liabilities?.map(liability => {
                const getCategoryIcon = (category: string) => {
                  switch (category) {
                    case 'Mortgage': return '🏠';
                    case 'Auto Loan': return '🚗';
                    case 'Credit Card': return '💳';
                    case 'Personal Loan': return '💰';
                    case 'Student Loan': return '🎓';
                    default: return '📋';
                  }
                };

                return (
                  <div key={liability.id} className="p-4 bg-rose-50 dark:bg-rose-900/10 rounded-lg border border-rose-200 dark:border-rose-800 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <div className="p-2 bg-rose-100 dark:bg-rose-900/20 rounded-lg">
                          <span className="text-lg">{getCategoryIcon(liability.category)}</span>
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 dark:text-white">{liability.name}</h3>
                          <p className="text-sm text-rose-600 dark:text-rose-400 font-medium">{liability.category}</p>
                          <p className="text-xl font-bold text-rose-600 dark:text-rose-400 mt-1">{formatCurrency(liability.amount)}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {liability.interest_rate}% APR
                          </p>
                          {liability.notes && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">{liability.notes}</p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-1 ml-2">
                        <button
                          onClick={() => {
                            setSelectedLiability(liability);
                            setLiabilityName(liability.name);
                            setLiabilityCategory(liability.category);
                            setLiabilityAmount(liability.amount.toString());
                            setInterestRate(liability.interest_rate.toString());
                            setLiabilityNotes(liability.notes || '');
                            setIsLiabilityModalOpen(true);
                          }}
                          className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            setItemToDelete({ type: 'liability', id: liability.id });
                            setIsDeleteModalOpen(true);
                          }}
                          className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
              {(!liabilities || liabilities.length === 0) && (
                <div className="text-center py-8">
                  <div className="mx-auto w-16 h-16 bg-rose-100 dark:bg-rose-900/20 rounded-full flex items-center justify-center mb-4">
                    <TrendingDown className="h-8 w-8 text-rose-600 dark:text-rose-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No liabilities yet</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">Great! You have no recorded debts</p>
                  <button
                    onClick={() => {
                      resetLiabilityForm();
                      setIsLiabilityModalOpen(true);
                    }}
                    className="inline-flex items-center px-4 py-2 bg-rose-600 dark:bg-rose-700 text-white rounded-lg hover:bg-rose-700 dark:hover:bg-rose-600 transition-colors"
                  >
                    <PlusCircle className="w-4 h-4 mr-2" />
                    Add Liability
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Asset Modal */}
      <Modal
        isOpen={isAssetModalOpen}
        onClose={() => {
          setIsAssetModalOpen(false);
          resetAssetForm();
        }}
        title={selectedAsset ? "Edit Asset" : "Add New Asset"}
      >
        <form onSubmit={handleAssetSubmit} className="space-y-4">
          <div>
            <label htmlFor="assetName" className="block text-sm font-medium text-gray-700">
              Asset Name
            </label>
            <input
              type="text"
              id="assetName"
              value={assetName}
              onChange={(e) => setAssetName(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
          </div>

          <div>
            <label htmlFor="assetCategory" className="block text-sm font-medium text-gray-700">
              Category
            </label>
            <select
              id="assetCategory"
              value={assetCategory}
              onChange={(e) => setAssetCategory(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              {ASSET_CATEGORIES.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="assetValue" className="block text-sm font-medium text-gray-700">
              Value
            </label>
            <input
              type="number"
              id="assetValue"
              value={assetValue}
              onChange={(e) => setAssetValue(e.target.value)}
              min="0"
              step="0.01"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
          </div>

          <div>
            <label htmlFor="acquisitionDate" className="block text-sm font-medium text-gray-700">
              Acquisition Date
            </label>
            <input
              type="date"
              id="acquisitionDate"
              value={acquisitionDate}
              onChange={(e) => setAcquisitionDate(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
          </div>

          <div>
            <label htmlFor="assetNotes" className="block text-sm font-medium text-gray-700">
              Notes
            </label>
            <textarea
              id="assetNotes"
              value={assetNotes}
              onChange={(e) => setAssetNotes(e.target.value)}
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>

          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={() => {
                setIsAssetModalOpen(false);
                resetAssetForm();
              }}
              className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              {selectedAsset ? "Save Changes" : "Add Asset"}
            </button>
          </div>
        </form>
      </Modal>

      {/* Liability Modal */}
      <Modal
        isOpen={isLiabilityModalOpen}
        onClose={() => {
          setIsLiabilityModalOpen(false);
          resetLiabilityForm();
        }}
        title={selectedLiability ? "Edit Liability" : "Add New Liability"}
      >
        <form onSubmit={handleLiabilitySubmit} className="space-y-4">
          <div>
            <label htmlFor="liabilityName" className="block text-sm font-medium text-gray-700">
              Liability Name
            </label>
            <input
              type="text"
              id="liabilityName"
              value={liabilityName}
              onChange={(e) => setLiabilityName(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
          </div>

          <div>
            <label htmlFor="liabilityCategory" className="block text-sm font-medium text-gray-700">
              Category
            </label>
            <select
              id="liabilityCategory"
              value={liabilityCategory}
              onChange={(e) => setLiabilityCategory(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            >
              {LIABILITY_CATEGORIES.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="liabilityAmount" className="block text-sm font-medium text-gray-700">
              Amount
            </label>
            <input
              type="number"
              id="liabilityAmount"
              value={liabilityAmount}
              onChange={(e) => setLiabilityAmount(e.target.value)}
              min="0"
              step="0.01"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
          </div>

          <div>
            <label htmlFor="interestRate" className="block text-sm font-medium text-gray-700">
              Interest Rate (%)
            </label>
            <input
              type="number"
              id="interestRate"
              value={interestRate}
              onChange={(e) => setInterestRate(e.target.value)}
              min="0"
              step="0.01"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
            />
          </div>

          <div>
            <label htmlFor="liabilityNotes" className="block text-sm font-medium text-gray-700">
              Notes
            </label>
            <textarea
              id="liabilityNotes"
              value={liabilityNotes}
              onChange={(e) => setLiabilityNotes(e.target.value)}
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>

          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={() => {
                setIsLiabilityModalOpen(false);
                resetLiabilityForm();
              }}
              className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              {selectedLiability ? "Save Changes" : "Add Liability"}
            </button>
          </div>
        </form>
      </Modal>

      <ConfirmDialog
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setItemToDelete(null);
        }}
        onConfirm={handleDelete}
        title={`Delete ${itemToDelete?.type === 'asset' ? 'Asset' : 'Liability'}`}
        message={`Are you sure you want to delete this ${itemToDelete?.type}? This action cannot be undone.`}
      />
    </div>
  );
}