import { useState, useEffect } from 'react';
import {
  Search,
  TrendingUp,
  TrendingDown,
  Eye,
  Bell,
  Shield,
  Percent,
  Building2
} from 'lucide-react';
import { PageLoading, EmptyState } from '../components/LoadingStates';
import { Bond } from '../types/investment';
import { bondDataService } from '../services/bondDataService';

export default function BondsPage() {
  const [bonds, setBonds] = useState<Bond[]>([]);
  const [filteredBonds, setFilteredBonds] = useState<Bond[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedBondType, setSelectedBondType] = useState<'all' | 'government' | 'corporate' | 'municipal'>('all');
  const [selectedRating, setSelectedRating] = useState<string>('');
  const [sortBy, setSortBy] = useState<'name' | 'yield' | 'maturity' | 'rating'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  useEffect(() => {
    loadBonds();
  }, []);

  const loadBonds = async () => {
    console.log('💰 Loading bonds from TradingView server...');
    setIsLoading(true);
    try {
      const realBonds = await bondDataService.getAllBonds();
      console.log('✅ Loaded bonds:', realBonds);
      setBonds(realBonds);
      setFilteredBonds(realBonds);
    } catch (error) {
      console.error('❌ Error loading bonds:', error);
      setBonds([]);
      setFilteredBonds([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async () => {
    if (searchQuery.trim()) {
      console.log(`🔍 Searching bonds for: "${searchQuery}"`);
      setIsLoading(true);
      try {
        const searchResults = await bondDataService.searchBonds(searchQuery.trim());
        console.log('📋 Bond search results:', searchResults);
        setFilteredBonds(searchResults);
      } catch (error) {
        console.error('❌ Bond search error:', error);
        setFilteredBonds([]);
      } finally {
        setIsLoading(false);
      }
    } else {
      // If no search query, show all bonds
      setFilteredBonds(bonds);
    }
  };

  const refreshBonds = async () => {
    console.log('🔄 Refreshing bond data...');
    await bondDataService.refreshCache();
    await loadBonds();
  };

  useEffect(() => {
    let filtered = bonds.filter(bond => {
      const matchesSearch = searchQuery === '' || 
        bond.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        bond.issuer.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesBondType = selectedBondType === 'all' || 
        bond.bond_type === selectedBondType;
      
      const matchesRating = selectedRating === '' || 
        bond.rating === selectedRating;
      
      return matchesSearch && matchesBondType && matchesRating;
    });

    // Sort filtered results
    filtered.sort((a, b) => {
      let aValue: number | string;
      let bValue: number | string;
      
      switch (sortBy) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'yield':
          aValue = a.yield_to_maturity;
          bValue = b.yield_to_maturity;
          break;
        case 'maturity':
          aValue = new Date(a.maturity_date).getTime();
          bValue = new Date(b.maturity_date).getTime();
          break;
        case 'rating':
          aValue = a.rating;
          bValue = b.rating;
          break;
        default:
          aValue = a.name;
          bValue = b.name;
      }
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      } else {
        return sortOrder === 'asc' ? Number(aValue) - Number(bValue) : Number(bValue) - Number(aValue);
      }
    });

    setFilteredBonds(filtered);
  }, [bonds, searchQuery, selectedBondType, selectedRating, sortBy, sortOrder]);

  const getBondTypeColor = (bondType: string) => {
    switch (bondType) {
      case 'government': return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-400';
      case 'corporate': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400';
      case 'municipal': return 'text-purple-600 bg-purple-100 dark:bg-purple-900 dark:text-purple-400';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400';
    }
  };

  const getRatingColor = (rating: string) => {
    const ratingLetter = rating.charAt(0);
    switch (ratingLetter) {
      case 'A': return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400';
      case 'B': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400';
      case 'C': return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-400';
      case 'D': return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getYearsToMaturity = (maturityDate: string) => {
    const now = new Date();
    const maturity = new Date(maturityDate);
    const yearsDiff = (maturity.getTime() - now.getTime()) / (1000 * 60 * 60 * 24 * 365);
    return yearsDiff.toFixed(1);
  };

  if (isLoading) {
    return <PageLoading message="Loading bonds data..." icon="financial" />;
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Fixed Income Bonds</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Explore government and corporate bonds for steady income
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button 
            onClick={refreshBonds}
            className="p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
            title="Refresh bond data"
          >
            <TrendingUp className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Market Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Available Bonds</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {bonds.length}
              </p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
              <Shield className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg. Yield</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {bonds.length > 0 ? 
                  (bonds.reduce((sum, bond) => sum + bond.yield_to_maturity, 0) / bonds.length).toFixed(2) : 
                  '0.00'
                }%
              </p>
            </div>
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
              <Percent className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Government Bonds</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {bonds.filter(bond => bond.bond_type === 'government').length}
              </p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
              <Building2 className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Corporate Bonds</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {bonds.filter(bond => bond.bond_type === 'corporate').length}
              </p>
            </div>
            <div className="p-3 bg-orange-100 dark:bg-orange-900 rounded-full">
              <Building2 className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative flex">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search bonds by name or issuer..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 border-r-0 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <button
                onClick={handleSearch}
                className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 border border-blue-600"
              >
                Search
              </button>
              <button
                onClick={() => {
                  setSearchQuery('');
                  setFilteredBonds(bonds);
                }}
                className="px-3 py-2 bg-gray-500 text-white rounded-r-lg hover:bg-gray-600 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 border border-gray-500"
              >
                All
              </button>
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-4">
            <select
              value={selectedBondType}
              onChange={(e) => setSelectedBondType(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Types</option>
              <option value="government">Government</option>
              <option value="corporate">Corporate</option>
              <option value="municipal">Municipal</option>
            </select>

            <select
              value={selectedRating}
              onChange={(e) => setSelectedRating(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">All Ratings</option>
              <option value="A+">A+</option>
              <option value="A">A</option>
              <option value="A-">A-</option>
              <option value="B+">B+</option>
              <option value="B">B</option>
              <option value="B-">B-</option>
              <option value="BB+">BB+</option>
              <option value="BB">BB</option>
              <option value="BB-">BB-</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="name">Name</option>
              <option value="yield">Yield</option>
              <option value="maturity">Maturity</option>
              <option value="rating">Rating</option>
            </select>

            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="p-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>
        </div>
      </div>

      {/* Bonds List */}
      {filteredBonds.length === 0 ? (
        <EmptyState 
          title="No bonds found"
          description="No bonds match your search criteria. Try adjusting your filters."
        />
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Bond
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Face Value
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Current Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Coupon Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Yield to Maturity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Maturity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Rating
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredBonds.map((bond) => (
                  <tr key={bond.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {bond.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {bond.issuer}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${getBondTypeColor(bond.bond_type)}`}>
                        {bond.bond_type}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {bond.face_value.toLocaleString()} EGP
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {bond.current_price.toLocaleString()} EGP
                      </div>
                      <div className={`text-xs ${bond.current_price > bond.face_value ? 'text-green-600' : bond.current_price < bond.face_value ? 'text-red-600' : 'text-gray-600'}`}>
                        {bond.current_price > bond.face_value ? 'Premium' : bond.current_price < bond.face_value ? 'Discount' : 'At Par'}
                      </div>
                      {bond.price_change !== undefined && (
                        <div className={`text-xs flex items-center ${(bond.price_change || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {(bond.price_change || 0) >= 0 ? (
                            <TrendingUp className="w-3 h-3 mr-1" />
                          ) : (
                            <TrendingDown className="w-3 h-3 mr-1" />
                          )}
                          <span>
                            {(bond.price_change || 0) >= 0 ? '+' : ''}{(bond.price_change || 0).toFixed(0)} 
                            ({(bond.price_change_percent || 0).toFixed(2)}%)
                          </span>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      {bond.coupon_rate.toFixed(2)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {bond.yield_to_maturity > bond.coupon_rate ? (
                          <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                        ) : (
                          <TrendingDown className="w-4 h-4 text-red-600 mr-1" />
                        )}
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {bond.yield_to_maturity.toFixed(2)}%
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {formatDate(bond.maturity_date)}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">
                        {getYearsToMaturity(bond.maturity_date)} years
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRatingColor(bond.rating)}`}>
                        {bond.rating}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button 
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                          title="View details"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button 
                          className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
                          title="Add to portfolio"
                        >
                          <Shield className="w-4 h-4" />
                        </button>
                        <button 
                          className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300"
                          title="Set yield alert"
                        >
                          <Bell className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Bond Information Panel */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Understanding Bond Investments</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Government Bonds</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Issued by the government, these bonds typically offer lower yields but higher safety and stability.
            </p>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Corporate Bonds</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Issued by companies, these bonds usually offer higher yields but come with increased credit risk.
            </p>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Yield vs. Coupon</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Coupon rate is fixed, but yield to maturity changes with market price and time to maturity.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
