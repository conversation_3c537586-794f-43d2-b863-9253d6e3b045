import { useState, useMemo, useEffect } from 'react';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { Line } from 'react-chartjs-2';
import { format, addMonths, parseISO, differenceInMonths } from 'date-fns';
import { formatCurrency } from '../utils/currency';
import { 
  AlertTriangle, 
  TrendingDown, 
  TrendingUp, 
  Calculator, 
  PieChart,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Brain,
  Gauge,
  Calendar,
  Zap,
  Shield,
  TrendingDown as TrendingDownIcon
} from 'lucide-react';

// Types for the cash flow analysis
interface Loan {
  id: string;
  name: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  remaining_balance: number;
  monthly_payment: number;
  start_date: string;
  next_payment_date: string;
  end_date: string;
}

interface CD {
  id: string;
  institution: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  maturity_date: string;
}

interface CashFlowProjection {
  period: number;
  date: string;
  starting_balance: number;
  monthly_income: number;
  living_expenses: number;
  loan_payments: number;
  cd_income_for_loans: number;
  loan_shortfall: number;
  total_outflow: number;
  net_change: number;
  ending_balance: number;
  active_loans: number;
  status: 'Positive' | 'Negative';
}

interface FinancialSituation {
  total_cd_income: number;
  cib_available: number;
  cd_for_loans: number;
  total_loan_payments: number;
  funding_gap: number;
  estimated_living_expenses: number;
  monthly_shortfall: number;
  cd_coverage_percentage: number;
}

interface RiskMetrics {
  level: string;
  color: string;
  bgColor: string;
  monthsToNegative: number;
  maxDeficit: number;
  cashRunway: number;
  stressLevel: string;
}

interface InjectionStrategy {
  name: string;
  description: string;
  injection_amount: number;
  injection_period: number;
  injection_date: string;
  type: 'emergency' | 'strategic' | 'staged' | 'restructuring';
}

interface ScenarioResult {
  name: string;
  monthly_savings: number;
  annual_savings: number;
  new_monthly_expense: number;
  months_to_breakeven: number;
  achieves_positive_flow: boolean;
  projection: CashFlowProjection[];
}

interface PaidMonth {
  id?: string; // Add id for persistence
  loan_id: string;
  month: string; // Format: 'YYYY-MM'
}

interface CashFlowConfig {
  id?: string;
  starting_balance: number;
  custom_living_expenses: number;
  months_to_project: number;
}



export function CashFlowAnalysis() {
  const { data: loans } = useSupabaseQuery<Loan>('loans');
  const { data: cds } = useSupabaseQuery<CD>('cds');
  
  // Add persistence for paid months using the same storage system
  const { 
    data: paidMonthsData, 
    addItem: addPaidMonth, 
    deleteItem: deletePaidMonth
  } = useSupabaseQuery<PaidMonth>('paid_months');

  // Add persistence for configuration settings
  const { 
    data: configData, 
    addItem: addConfig, 
    updateItem: updateConfig
  } = useSupabaseQuery<CashFlowConfig>('cash_flow_config');

  // Get current config or use defaults
  const currentConfig = configData?.[0] || {
    starting_balance: 987641,
    custom_living_expenses: 55000,
    months_to_project: 120
  };

  const [activeTab, setActiveTab] = useState('overview');
  
  // Use persisted configuration values
  const monthsToProject = currentConfig.months_to_project;
  const customLivingExpenses = currentConfig.custom_living_expenses;
  const startingBalance = currentConfig.starting_balance;
  
  // Use persisted paid months data instead of local state
  const paidMonths = paidMonthsData || [];

  // Helper functions for updating configuration with persistence
  const updateConfiguration = async (updates: Partial<CashFlowConfig>) => {
    try {
      if (configData?.[0]?.id) {
        // Update existing config
        await updateConfig(configData[0].id, updates);
      } else {
        // Create new config
        await addConfig({
          starting_balance: currentConfig.starting_balance,
          custom_living_expenses: currentConfig.custom_living_expenses,
          months_to_project: currentConfig.months_to_project,
          ...updates
        });
      }
    } catch (error) {
      console.error('Error updating configuration:', error);
    }
  };

  const setStartingBalance = (value: number) => {
    updateConfiguration({ starting_balance: value });
  };

  const setCustomLivingExpenses = (value: number) => {
    updateConfiguration({ custom_living_expenses: value });
  };

  const setMonthsToProject = (value: number) => {
    updateConfiguration({ months_to_project: value });
  };

  // Helper functions for managing paid months with persistence
  const markMonthAsPaid = async (loanId: string, month: string) => {
    const exists = paidMonths.some(p => p.loan_id === loanId && p.month === month);
    if (!exists) {
      try {
        await addPaidMonth({ loan_id: loanId, month });
      } catch (error) {
        console.error('Error adding paid month:', error);
      }
    }
  };

  const markMonthAsUnpaid = async (loanId: string, month: string) => {
    const existingItem = paidMonths.find(p => p.loan_id === loanId && p.month === month);
    if (existingItem && 'id' in existingItem) {
      try {
        await deletePaidMonth(existingItem.id as string);
      } catch (error) {
        console.error('Error removing paid month:', error);
      }
    }
  };

  const isMonthPaid = (loanId: string, month: string) => {
    return paidMonths.some(p => p.loan_id === loanId && p.month === month);
  };

  // Auto-mark past months as paid (before next_payment_date)
  useEffect(() => {
    if (!loans || loans.length === 0 || !paidMonthsData) return;
    
    // Only run initial auto-marking if no paid months exist yet
    if (paidMonths.length > 0) return;
    
    const autoMarkPaidMonths = async () => {
      for (const loan of loans) {
        try {
          const nextPaymentDate = parseISO(loan.next_payment_date);
          const startDate = parseISO(loan.start_date);
          
          // Mark all months from start_date to the month BEFORE next_payment_date as paid
          let checkDate = new Date(startDate);
          while (checkDate < nextPaymentDate) {
            const monthKey = format(checkDate, 'yyyy-MM');
            await markMonthAsPaid(loan.id, monthKey);
            checkDate = addMonths(checkDate, 1);
          }
        } catch (error) {
          console.error('Error auto-marking paid months for', loan.name, error);
        }
      }
    };
    
    autoMarkPaidMonths();
  }, [loans, paidMonthsData]); // Depend on both loans and paidMonthsData

  // Helper function to fix loan dates
  const getCorrectLoanDates = (loan: Loan) => {
    try {
      const currentDate = new Date();
      const startDate = parseISO(loan.start_date);
      
      // Calculate how many payments have been made
      const monthsSinceStart = differenceInMonths(currentDate, startDate);
      
      // Calculate next payment date (first unpaid month)
      const nextPaymentDate = addMonths(startDate, Math.max(0, monthsSinceStart));
      
      // Calculate actual end date based on start date + term
      const actualEndDate = addMonths(startDate, loan.term_months);
      
      return {
        nextPaymentDate,
        actualEndDate,
        monthsRemaining: Math.max(0, differenceInMonths(actualEndDate, currentDate))
      };
    } catch (error) {
      console.error('Error calculating loan dates for', loan.name, error);
      return {
        nextPaymentDate: new Date(),
        actualEndDate: new Date(),
        monthsRemaining: 0
      };
    }
  };

  // Configuration state - Dynamic calculation based on original loan end dates  
  const defaultMonthsToProject = useMemo(() => {
    if (!loans || loans.length === 0) return 120; // Default 10 years if no loans
    
    // Find the loan with the latest original end date
    const latestEndDate = loans.reduce((latest, loan) => {
      try {
        const endDate = parseISO(loan.end_date);
        return endDate > latest ? endDate : latest;
      } catch (error) {
        console.error('Error parsing end date for loan:', loan.name, error);
        return latest;
      }
    }, new Date());
    
    // Calculate months from now to the latest loan end date
    const monthsToLatestLoan = Math.max(0, differenceInMonths(latestEndDate, new Date()));
    
    // Add some buffer months for analysis (12 months)
    return Math.max(120, monthsToLatestLoan + 12);
  }, [loans]);

  // Update monthsToProject when loans change (only if user hasn't customized it)
  useEffect(() => {
    // Only auto-update if using the default value
    if (monthsToProject === 120) {
      setMonthsToProject(defaultMonthsToProject);
    }
  }, [defaultMonthsToProject, monthsToProject]);

  // Calculate current financial situation
  const currentSituation = useMemo<FinancialSituation>(() => {
    const totalLoanPayments = loans?.reduce((sum, loan) => sum + loan.monthly_payment, 0) || 0;
    
    // Separate CIB income (available for living) from other CD income (for loans)
    const cibIncome = cds?.filter(cd => cd.institution.toLowerCase().includes('cib'))
      .reduce((sum, cd) => sum + (cd.principal * (cd.interest_rate / 100) / 12), 0) || 0;
    
    const otherCdIncome = cds?.filter(cd => !cd.institution.toLowerCase().includes('cib'))
      .reduce((sum, cd) => sum + (cd.principal * (cd.interest_rate / 100) / 12), 0) || 0;
    
    const totalCdIncome = cibIncome + otherCdIncome;
    const fundingGap = Math.max(0, totalLoanPayments - otherCdIncome);
    const monthlyShortfall = fundingGap; // Only loan shortfall since CIB covers living expenses

    return {
      total_cd_income: totalCdIncome,
      cib_available: cibIncome,
      cd_for_loans: otherCdIncome,
      total_loan_payments: totalLoanPayments,
      funding_gap: fundingGap,
      estimated_living_expenses: customLivingExpenses,
      monthly_shortfall: monthlyShortfall,
      cd_coverage_percentage: (otherCdIncome / totalLoanPayments * 100) || 0
    };
  }, [loans, cds, customLivingExpenses]);

  // Generate dynamic cash flow projection
  const cashFlowProjection = useMemo<CashFlowProjection[]>(() => {
    if (!loans || !cds) return [];
    
    // Debug: Log original loan data and configuration
    console.log('Original Loan Data:', loans.map(loan => ({
      name: loan.name,
      next_payment_date: loan.next_payment_date,
      end_date: loan.end_date,
      monthly_payment: loan.monthly_payment
    })));
    console.log('Current Configuration:', currentConfig);

    try {
      const projections: CashFlowProjection[] = [];
      let currentBalance = startingBalance;
      const startDate = new Date();

      // Create loan payment schedule using original loan data (not recalculated dates)
      const loanSchedule = loans.map(loan => {
        try {
          // Use the original loan data instead of recalculating dates
          const endDate = parseISO(loan.end_date);
          const remainingMonths = Math.max(0, differenceInMonths(endDate, new Date()));
          
          return {
            id: loan.id, // Add loan ID
            monthly_payment: loan.monthly_payment || 0,
            end_date: endDate,
            next_payment_date: parseISO(loan.next_payment_date), // Use original date
            remaining_months: remainingMonths,
            name: loan.name
          };
        } catch (error) {
          console.error('Error processing loan:', loan.name, error);
          // Return a safe default
          return {
            id: loan.id,
            monthly_payment: 0,
            end_date: new Date(),
            next_payment_date: new Date(),
            remaining_months: 0,
            name: loan.name
          };
        }
      });

      console.log('Loan Schedule:', loanSchedule); // Debug log

      for (let month = 0; month < monthsToProject; month++) {
        try {
          const projectionDate = addMonths(startDate, month);
          
          // Calculate active loans for this month (loans that haven't ended yet)
          const activeLoans = loanSchedule.filter(loan => 
            projectionDate < loan.end_date
          );
          
          // Calculate loan payments excluding paid months
          const monthKey = format(projectionDate, 'yyyy-MM');
          const monthlyLoanPayments = activeLoans.reduce((sum, loan) => {
            try {
              // Skip payment if this month is marked as paid
              const isPaid = isMonthPaid(loan.id, monthKey);
              if (isPaid) {
                return sum;
              }
              return sum + (loan.monthly_payment || 0);
            } catch (error) {
              console.error('Error calculating payment for loan:', loan.name, error);
              return sum;
            }
          }, 0);
          
          const loanShortfall = Math.max(0, monthlyLoanPayments - currentSituation.cd_for_loans);

          // Fixed logic: CIB income covers living expenses, only loan shortfall is additional outflow
          const monthlyIncome = currentSituation.cib_available; // CIB income to account
          const livingExpensesCovered = Math.min(monthlyIncome, customLivingExpenses);
          const remainingCibIncome = Math.max(0, monthlyIncome - livingExpensesCovered);

          // Total outflow is only the loan shortfall (living expenses are covered by CIB income)
          const totalOutflow = loanShortfall;
          const netChange = remainingCibIncome - loanShortfall;
          
          const startingBalanceForPeriod = currentBalance;
          currentBalance += netChange;

          projections.push({
            period: month + 1,
            date: format(projectionDate, 'yyyy-MM-dd'),
            starting_balance: startingBalanceForPeriod,
            monthly_income: monthlyIncome,
            living_expenses: livingExpensesCovered, // Only the amount actually covered by CIB
            loan_payments: monthlyLoanPayments,
            cd_income_for_loans: currentSituation.cd_for_loans,
            loan_shortfall: loanShortfall,
            total_outflow: totalOutflow, // Only loan shortfall, not living expenses
            net_change: netChange,
            ending_balance: currentBalance,
            active_loans: activeLoans.length,
            status: currentBalance >= 0 ? 'Positive' : 'Negative'
          });
        } catch (error) {
          console.error('Error calculating projection for month:', month, error);
          // Continue with next month
        }
      }

      return projections;
    } catch (error) {
      console.error('Error generating cash flow projection:', error);
      return [];
    }
  }, [loans, cds, monthsToProject, customLivingExpenses, startingBalance, currentSituation, paidMonths]);

  // Critical dates analysis
  const criticalAnalysis = useMemo(() => {
    const negativeMonths = cashFlowProjection.filter(p => p.status === 'Negative');
    const firstNegative = negativeMonths[0];
    const veryNegative = cashFlowProjection.find(p => p.ending_balance < -500000);
    const lastPositive = cashFlowProjection.filter(p => p.status === 'Positive').pop();

    return {
      firstNegative,
      veryNegative,
      lastPositive,
      totalNegativeMonths: negativeMonths.length
    };
  }, [cashFlowProjection]);

  // Advanced risk metrics calculation
  const riskMetrics = useMemo<RiskMetrics>(() => {
    const monthsToNegative = criticalAnalysis.firstNegative?.period || Infinity;
    const maxDeficit = Math.min(...cashFlowProjection.map(p => p.ending_balance));
    const monthlyBurnRate = currentSituation.monthly_shortfall;
    const cashRunway = monthlyBurnRate > 0 ? startingBalance / monthlyBurnRate : Infinity;
    
    let level, color, bgColor, stressLevel;
    
    if (monthsToNegative <= 3) {
      level = 'CRITICAL';
      color = 'text-red-600';
      bgColor = 'bg-red-50';
      stressLevel = '🔴 CRITICAL';
    } else if (monthsToNegative <= 12) {
      level = 'HIGH';
      color = 'text-orange-600';
      bgColor = 'bg-orange-50';
      stressLevel = '🟠 HIGH';
    } else if (monthsToNegative <= 24) {
      level = 'MEDIUM';
      color = 'text-yellow-600';
      bgColor = 'bg-yellow-50';
      stressLevel = '🟡 MODERATE';
    } else {
      level = 'LOW';
      color = 'text-green-600';
      bgColor = 'bg-green-50';
      stressLevel = '🟢 GOOD';
    }

    return {
      level,
      color,
      bgColor,
      monthsToNegative: monthsToNegative === Infinity ? 0 : monthsToNegative,
      maxDeficit: Math.abs(maxDeficit),
      cashRunway: cashRunway === Infinity ? 60 : Math.min(cashRunway, 60),
      stressLevel
    };
  }, [criticalAnalysis, cashFlowProjection, currentSituation, startingBalance]);

  // Cash injection strategy calculation
  const injectionStrategy = useMemo<InjectionStrategy | null>(() => {
    if (!criticalAnalysis.firstNegative) return null;

    const firstNegativePeriod = criticalAnalysis.firstNegative.period;
    const minInjection = Math.abs(criticalAnalysis.firstNegative.ending_balance) + 50000; // 50K buffer
    
    return {
      name: 'Emergency Cash Injection',
      description: `Inject before going negative in period ${firstNegativePeriod}`,
      injection_amount: minInjection,
      injection_period: Math.max(1, firstNegativePeriod - 1),
      injection_date: cashFlowProjection[Math.max(0, firstNegativePeriod - 2)]?.date || new Date().toISOString(),
      type: 'emergency'
    };
  }, [criticalAnalysis, cashFlowProjection]);

  // Scenario analysis
  const scenarios = useMemo<ScenarioResult[]>(() => {
    const scenarioResults: ScenarioResult[] = [];
    
    // Test different expense reduction scenarios
    [5, 10, 15, 20, 25].forEach(reductionPct => {
      const monthlySavings = customLivingExpenses * (reductionPct / 100);
      const newMonthlyExpense = customLivingExpenses - monthlySavings;
      
      // Calculate new projection with reduced expenses
      const newProjection = cashFlowProjection.map(p => ({
        ...p,
        living_expenses: newMonthlyExpense,
        net_change: p.net_change + monthlySavings,
        ending_balance: p.ending_balance + (monthlySavings * p.period)
      }));
      
      const achievesPositiveFlow = newProjection.every(p => p.ending_balance >= 0);
      const monthsToBreakeven = newProjection.findIndex(p => p.ending_balance >= 0 && p.period > 1);
      
      scenarioResults.push({
        name: `Reduce Expenses by ${reductionPct}%`,
        monthly_savings: monthlySavings,
        annual_savings: monthlySavings * 12,
        new_monthly_expense: newMonthlyExpense,
        months_to_breakeven: monthsToBreakeven > 0 ? monthsToBreakeven : 0,
        achieves_positive_flow: achievesPositiveFlow,
        projection: newProjection
      });
    });

    return scenarioResults;
  }, [customLivingExpenses, cashFlowProjection]);

  // AI-powered recommendations
  const aiRecommendations = useMemo(() => {
    const recommendations = [];
    
    if (riskMetrics.level === 'CRITICAL') {
      recommendations.push({
        priority: '🚨 URGENT',
        action: 'Immediate Cash Injection Required',
        details: `Secure ${formatCurrency(injectionStrategy?.injection_amount || 0)} within ${riskMetrics.monthsToNegative} months to prevent account deficit.`,
        category: 'liquidity'
      });
    }
    
    if (currentSituation.cd_coverage_percentage < 80) {
      recommendations.push({
        priority: '⚠️ HIGH',
        action: 'Loan Restructuring Strategy',
        details: `CD income only covers ${currentSituation.cd_coverage_percentage.toFixed(1)}% of loan payments. Consider refinancing or extending loan terms.`,
        category: 'debt'
      });
    }
    
    // Best scenario recommendation
    const bestScenario = scenarios.find(s => s.achieves_positive_flow);
    if (bestScenario) {
      recommendations.push({
        priority: '💡 STRATEGIC',
        action: 'Expense Optimization Opportunity',
        details: `${bestScenario.name} would save ${formatCurrency(bestScenario.annual_savings)} annually and achieve positive cash flow.`,
        category: 'optimization'
      });
    }
    
    if (cds && cds.length > 0) {
      const totalCdValue = cds.reduce((sum, cd) => sum + cd.principal, 0);
      if (totalCdValue >= (injectionStrategy?.injection_amount || 0)) {
        recommendations.push({
          priority: '💰 OPTION',
          action: 'CD Liquidation Strategy',
          details: `Total CD value of ${formatCurrency(totalCdValue)} can cover funding needs. Consider early withdrawal penalties vs. benefits.`,
          category: 'assets'
        });
      }
    }

    return recommendations;
  }, [riskMetrics, currentSituation, injectionStrategy, scenarios, cds]);

  // Chart data for cash flow visualization
  const chartData = useMemo(() => {
    const labels = cashFlowProjection.slice(0, 60).map(p => format(parseISO(p.date), 'MMM yyyy'));
    const balanceData = cashFlowProjection.slice(0, 60).map(p => p.ending_balance);
    
    return {
      labels,
      datasets: [
        {
          label: 'Account Balance',
          data: balanceData,
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          tension: 0.1,
          pointBackgroundColor: balanceData.map(balance => balance < 0 ? '#ef4444' : '#10b981'),
        }
      ]
    };
  }, [cashFlowProjection]);

  const chartOptions = {
    responsive: true,
    plugins: {
      title: {
        display: true,
        text: 'Cash Flow Projection (24 Months)'
      },
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: false,
        ticks: {
          callback: function(value: any) {
            return formatCurrency(value);
          }
        }
      }
    },
    elements: {
      point: {
        radius: 4,
        hoverRadius: 6
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          💹 Advanced Cash Flow Analysis
        </h1>
        <div className={`px-4 py-2 rounded-lg ${riskMetrics.bgColor}`}>
          <span className={`font-semibold ${riskMetrics.color}`}>
            Risk Level: {riskMetrics.level}
          </span>
        </div>
      </div>

      {/* Risk Alert */}
      {criticalAnalysis.firstNegative && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
            <div>
              <h3 className="font-semibold text-red-800 dark:text-red-200">Critical Alert</h3>
              <p className="text-red-700 dark:text-red-300">
                Account will go negative in Month {criticalAnalysis.firstNegative.period} 
                ({format(parseISO(criticalAnalysis.firstNegative.date), 'MMM yyyy')}) with deficit of{' '}
                {formatCurrency(Math.abs(criticalAnalysis.firstNegative.ending_balance))}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: PieChart },
            { id: 'risk-assessment', name: 'Risk Assessment', icon: Gauge },
            { id: 'projections', name: 'Projections', icon: TrendingUp },
            { id: 'scenarios', name: 'Scenarios', icon: Calculator },
            { id: 'ai-recommendations', name: 'AI Insights', icon: Brain }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`
                flex items-center py-2 px-1 border-b-2 font-medium text-sm
                ${activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                }
              `}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Current Situation Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total CD Income</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                    {formatCurrency(currentSituation.total_cd_income)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
              <div className="flex items-center">
                <TrendingDown className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Monthly Loan Payments</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                    {formatCurrency(currentSituation.total_loan_payments)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
              <div className="flex items-center">
                <AlertCircle className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Monthly Shortfall</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                    {formatCurrency(currentSituation.monthly_shortfall)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">CD Coverage</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-white">
                    {currentSituation.cd_coverage_percentage.toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Income vs Expenses Breakdown */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg border border-green-200 dark:border-green-800">
              <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-4">💵 Income Sources</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-green-700 dark:text-green-300">CIB CD (Available for Living)</span>
                  <span className="font-semibold">{formatCurrency(currentSituation.cib_available)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-700 dark:text-green-300">Other CDs (For Loans)</span>
                  <span className="font-semibold">{formatCurrency(currentSituation.cd_for_loans)}</span>
                </div>
                <div className="border-t border-green-300 dark:border-green-700 pt-2">
                  <div className="flex justify-between font-bold">
                    <span>Total CD Income</span>
                    <span>{formatCurrency(currentSituation.total_cd_income)}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg border border-red-200 dark:border-red-800">
              <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-4">💸 Monthly Expenses</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-red-700 dark:text-red-300">Loan Payments</span>
                  <span className="font-semibold">{formatCurrency(currentSituation.total_loan_payments)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-red-700 dark:text-red-300">Living Expenses</span>
                  <span className="font-semibold">{formatCurrency(currentSituation.estimated_living_expenses)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-red-700 dark:text-red-300">Funding Gap (From Account)</span>
                  <span className="font-semibold">{formatCurrency(currentSituation.funding_gap)}</span>
                </div>
                <div className="border-t border-red-300 dark:border-red-700 pt-2">
                  <div className="flex justify-between font-bold">
                    <span>CD Coverage of Loans</span>
                    <span>{currentSituation.cd_coverage_percentage.toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Loan Timeline */}
          {loans && loans.length > 0 && (
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-4">📅 Loan End Dates</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {loans
                  .map(loan => {
                    const { actualEndDate } = getCorrectLoanDates(loan);
                    return { ...loan, correctedEndDate: actualEndDate };
                  })
                  .sort((a, b) => a.correctedEndDate.getTime() - b.correctedEndDate.getTime())
                  .map(loan => {
                    const monthsRemaining = Math.max(0, differenceInMonths(loan.correctedEndDate, new Date()));
                    return (
                      <div key={loan.id} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <div className="font-semibold text-gray-900 dark:text-white">{loan.name}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          Ends: {format(loan.correctedEndDate, 'MMM dd, yyyy')}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {monthsRemaining} months remaining
                        </div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {formatCurrency(loan.monthly_payment)}/month
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>
          )}

          {/* Cash Flow Chart */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <Line data={chartData} options={chartOptions} />
          </div>
        </div>
      )}

      {activeTab === 'risk-assessment' && (
        <div className="space-y-6">
          {/* Risk Dashboard */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Months to Negative */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">Months Until Negative</h3>
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {riskMetrics.monthsToNegative || 'Safe'}
              </div>
              <div className={`text-sm ${riskMetrics.color}`}>
                {riskMetrics.level} Risk
              </div>
            </div>

            {/* Maximum Deficit */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">Maximum Deficit</h3>
                <TrendingDownIcon className="h-5 w-5 text-gray-400" />
              </div>
              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {formatCurrency(riskMetrics.maxDeficit)}
              </div>
              <div className="text-sm text-red-600">
                Peak deficit amount
              </div>
            </div>

            {/* Cash Runway */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">Cash Runway</h3>
                <Zap className="h-5 w-5 text-gray-400" />
              </div>
              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {riskMetrics.cashRunway.toFixed(1)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Months remaining
              </div>
            </div>

            {/* Stress Level */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">Financial Stress</h3>
                <Shield className="h-5 w-5 text-gray-400" />
              </div>
              <div className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                {riskMetrics.stressLevel}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Overall health
              </div>
            </div>
          </div>

          {/* Cash Injection Strategy */}
          {injectionStrategy && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-4">💡 Recommended Action Plan</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="font-medium">Strategy:</span>
                  <span>{injectionStrategy.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Required Amount:</span>
                  <span className="font-bold text-yellow-800 dark:text-yellow-200">
                    {formatCurrency(injectionStrategy.injection_amount)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Timing:</span>
                  <span>Before Period {injectionStrategy.injection_period}</span>
                </div>
                <div className="text-sm text-yellow-700 dark:text-yellow-300 mt-3">
                  {injectionStrategy.description}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {activeTab === 'projections' && (
        <div className="space-y-6">
          {/* Configuration Panel */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">⚙️ Projection Settings</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Starting Balance
                </label>
                <input
                  type="number"
                  value={startingBalance}
                  onChange={(e) => setStartingBalance(Number(e.target.value))}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Months to Project (Auto: {defaultMonthsToProject} months)
                </label>
                <select
                  value={monthsToProject}
                  onChange={(e) => setMonthsToProject(Number(e.target.value))}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value={12}>12 months (1 year)</option>
                  <option value={24}>24 months (2 years)</option>
                  <option value={36}>36 months (3 years)</option>
                  <option value={60}>60 months (5 years)</option>
                  <option value={120}>120 months (10 years)</option>
                  <option value={defaultMonthsToProject}>
                    {defaultMonthsToProject} months (Until last loan + buffer)
                  </option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Monthly Living Expenses
                </label>
                <input
                  type="number"
                  value={customLivingExpenses}
                  onChange={(e) => setCustomLivingExpenses(Number(e.target.value))}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600"
                />
              </div>
            </div>
          </div>

          {/* Payment Management Panel */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">💳 Payment Management</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Mark months as paid to exclude them from projections. Months before the next payment date are automatically marked as paid.
            </p>
            
            {loans && loans.length > 0 && (
              <div className="space-y-4">
                {loans.map(loan => {
                  // Use original loan dates from data (not recalculated)
                  const nextPaymentDate = parseISO(loan.next_payment_date);
                  const actualEndDate = parseISO(loan.end_date);
                  
                  // Generate months to show - show current and future months around the payment schedule
                  const today = new Date();
                  const startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1); // Start 1 month before current
                  
                  const monthsToShow = [];
                  for (let i = 0; i < 8; i++) { // Show 8 months
                    const monthDate = addMonths(startDate, i);
                    if (monthDate <= actualEndDate) {
                      monthsToShow.push(monthDate);
                    }
                  }
                  
                  return (
                    <div key={loan.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-3">
                        <h4 className="font-semibold text-gray-900 dark:text-white">{loan.name}</h4>
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {formatCurrency(loan.monthly_payment)}/month • Next: {format(nextPaymentDate, 'MMM yyyy')} • End: {format(actualEndDate, 'MMM yyyy')}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
                        {monthsToShow.map(monthDate => {
                          const monthKey = format(monthDate, 'yyyy-MM');
                          const isPaid = isMonthPaid(loan.id, monthKey);
                          const isPast = monthDate < today;
                          
                          return (
                            <button
                              key={monthKey}
                              onClick={() => isPaid ? markMonthAsUnpaid(loan.id, monthKey) : markMonthAsPaid(loan.id, monthKey)}
                              className={`p-2 rounded text-xs font-medium transition-colors ${
                                isPaid 
                                  ? 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-200' 
                                  : isPast
                                  ? 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'
                                  : 'bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900 dark:text-red-200'
                              }`}
                            >
                              <div>{format(monthDate, 'MMM yyyy')}</div>
                              <div className="text-xs opacity-75">
                                {isPaid ? '✓ Paid' : 'Due'}
                              </div>
                            </button>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Projection Table */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow overflow-x-auto">
            <h3 className="text-lg font-semibold mb-4">📊 Detailed Cash Flow Projection</h3>
            <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
              Showing {Math.min(cashFlowProjection.length, 60)} of {cashFlowProjection.length} projected months
              {cashFlowProjection.length > 60 && ' (limited for performance)'}
            </div>
            <div className="max-h-96 overflow-y-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700 sticky top-0">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Starting Balance</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loan Payments</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shortfall</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ending Balance</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {cashFlowProjection.slice(0, 60).map((projection) => (
                    <tr 
                      key={projection.period}
                      className={projection.status === 'Negative' ? 'bg-red-50 dark:bg-red-900/20' : ''}
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {projection.period}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {format(parseISO(projection.date), 'MMM yyyy')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {formatCurrency(projection.starting_balance)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {formatCurrency(projection.loan_payments)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {formatCurrency(projection.loan_shortfall)}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                        projection.ending_balance < 0 ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {formatCurrency(projection.ending_balance)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          projection.status === 'Positive' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {projection.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'scenarios' && (
        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">🎯 Expense Reduction Scenarios</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Compare different expense reduction strategies to improve your cash flow situation.
            </p>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {scenarios.map((scenario, index) => (
                <div 
                  key={index}
                  className={`p-4 rounded-lg border-2 ${
                    scenario.achieves_positive_flow 
                      ? 'border-green-200 bg-green-50 dark:bg-green-900/20' 
                      : 'border-gray-200 bg-gray-50 dark:bg-gray-700'
                  }`}
                >
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Monthly Savings:</span>
                      <span className="font-medium text-green-600">
                        {formatCurrency(scenario.monthly_savings)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Annual Savings:</span>
                      <span className="font-medium text-green-600">
                        {formatCurrency(scenario.annual_savings)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>New Monthly Expense:</span>
                      <span className="font-medium">
                        {formatCurrency(scenario.new_monthly_expense)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Achieves Positive Flow:</span>
                      <span className={`font-medium ${
                        scenario.achieves_positive_flow ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {scenario.achieves_positive_flow ? '✅ Yes' : '❌ No'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'ai-recommendations' && (
        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <Brain className="h-6 w-6 mr-2 text-indigo-600" />
              🤖 AI-Powered Financial Recommendations
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Intelligent recommendations based on your financial situation analysis.
            </p>
            
            <div className="space-y-4">
              {aiRecommendations.map((rec, index) => (
                <div 
                  key={index}
                  className={`p-4 rounded-lg border-l-4 ${
                    rec.priority.includes('URGENT') 
                      ? 'border-red-500 bg-red-50 dark:bg-red-900/20' 
                      : rec.priority.includes('HIGH')
                      ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20'
                      : rec.priority.includes('STRATEGIC')
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-green-500 bg-green-50 dark:bg-green-900/20'
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold text-gray-900 dark:text-white">
                      {rec.priority} - {rec.action}
                    </h4>
                    <span className={`text-xs px-2 py-1 rounded ${
                      rec.category === 'liquidity' ? 'bg-red-100 text-red-800' :
                      rec.category === 'debt' ? 'bg-orange-100 text-orange-800' :
                      rec.category === 'optimization' ? 'bg-blue-100 text-blue-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {rec.category}
                    </span>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300">
                    {rec.details}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}