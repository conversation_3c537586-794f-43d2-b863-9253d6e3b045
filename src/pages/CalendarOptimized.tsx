/**
 * Optimized Calendar Component
 * Refactored to use sub-components, reducer for state management, and memoization
 */

import { useReducer, useMemo, useCallback, useState } from 'react';
import { format, parseISO } from 'date-fns';
import { 
  Calendar as CalendarIcon, 
  Plus, 
  Settings, 
  Filter,
  Search,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

// Types and state management
import { CalendarEvent, EventTimeHorizon, RecurrencePattern, ReminderUnit, ToastMessage } from '../types/calendar';
import { calendarReducer, initialCalendarState } from '../hooks/useCalendarReducer';
import { useCalendarEvents } from '../hooks/useCalendarEvents';

// Components
import { CalendarView } from '../components/CalendarView';
import { EventModal } from '../components/EventModal';
import { ToastContainer } from '../components/Toast';
import { formatCurrency } from '../utils/currency';
import { paginateEvents } from '../utils/calendarUtils';

// Constants
const EVENTS_PER_PAGE = 5;

export function Calendar() {
  const [state, dispatch] = useReducer(calendarReducer, initialCalendarState);
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  // Custom hook for data management
  const {
    calendarEvents,
    loading,
    error: dataError,
    addCustomEvent,
    updateCustomEvent,
    deleteCustomEvent,
    refreshData
  } = useCalendarEvents(
    state.eventTimeHorizon,
    state.eventTypeFilter,
    state.eventCategoryFilter,
    state.searchQuery
  );

  // Memoized filtered and paginated events for the event list
  const paginatedEvents = useMemo(() => {
    return paginateEvents(calendarEvents, state.currentPage, EVENTS_PER_PAGE);
  }, [calendarEvents, state.currentPage]);

  const totalPages = useMemo(() => {
    return Math.ceil(calendarEvents.length / EVENTS_PER_PAGE);
  }, [calendarEvents.length]);

  // Toast management
  const addToast = useCallback((message: Omit<ToastMessage, 'id'>) => {
    const newToast: ToastMessage = {
      ...message,
      id: Date.now().toString()
    };
    setToasts(prev => [...prev, newToast]);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  // Calendar event handlers
  const handleDateClick = useCallback((info: any) => {
    const clickedDate = parseISO(info.dateStr);
    dispatch({ type: 'SET_SELECTED_DATE', payload: clickedDate });
    dispatch({ type: 'SET_SELECTED_EVENT', payload: null });
    dispatch({ type: 'RESET_FORM' });
    dispatch({ type: 'SET_MODAL_OPEN', payload: true });
  }, []);

  const handleEventClick = useCallback((info: any) => {
    const clickedEvent = info.event;
    const calendarEvent: CalendarEvent = {
      id: clickedEvent.id,
      title: clickedEvent.title,
      start: clickedEvent.startStr,
      end: clickedEvent.endStr,
      backgroundColor: clickedEvent.backgroundColor,
      borderColor: clickedEvent.borderColor,
      textColor: clickedEvent.textColor,
      allDay: clickedEvent.allDay,
      className: clickedEvent.classNames.join(' '),
      extendedProps: clickedEvent.extendedProps
    };

    dispatch({ type: 'SET_SELECTED_EVENT', payload: calendarEvent });
    dispatch({ type: 'SET_SELECTED_DATE', payload: parseISO(clickedEvent.startStr) });
    
    // Pre-fill form if it's a custom event
    if (calendarEvent.extendedProps.type === 'custom') {
      dispatch({ type: 'SET_EVENT_TITLE', payload: calendarEvent.title });
      dispatch({ type: 'SET_EVENT_DESCRIPTION', payload: calendarEvent.extendedProps.description || '' });
      dispatch({ type: 'SET_EVENT_AMOUNT', payload: calendarEvent.extendedProps.amount?.toString() || '' });
      dispatch({ type: 'SET_EVENT_COLOR', payload: calendarEvent.backgroundColor });
    }
    
    dispatch({ type: 'SET_MODAL_OPEN', payload: true });
  }, []);

  // Form handlers using useCallback for performance
  const handleEventTitleChange = useCallback((value: string) => {
    dispatch({ type: 'SET_EVENT_TITLE', payload: value });
  }, []);

  const handleEventDescriptionChange = useCallback((value: string) => {
    dispatch({ type: 'SET_EVENT_DESCRIPTION', payload: value });
  }, []);

  const handleEventAmountChange = useCallback((value: string) => {
    dispatch({ type: 'SET_EVENT_AMOUNT', payload: value });
  }, []);

  const handleEventColorChange = useCallback((value: string) => {
    dispatch({ type: 'SET_EVENT_COLOR', payload: value });
  }, []);

  const handleIsRecurringChange = useCallback((value: boolean) => {
    dispatch({ type: 'SET_IS_RECURRING', payload: value });
  }, []);

  const handleRecurrencePatternChange = useCallback((value: RecurrencePattern) => {
    dispatch({ type: 'SET_RECURRENCE_PATTERN', payload: value });
  }, []);

  const handleRecurrenceEndDateChange = useCallback((value: string) => {
    dispatch({ type: 'SET_RECURRENCE_END_DATE', payload: value });
  }, []);

  const handleRecurrenceCountChange = useCallback((value: string) => {
    dispatch({ type: 'SET_RECURRENCE_COUNT', payload: value });
  }, []);

  const handleReminderChange = useCallback((value: boolean) => {
    dispatch({ type: 'SET_REMINDER', payload: value });
  }, []);

  const handleReminderTimeChange = useCallback((value: number) => {
    dispatch({ type: 'SET_REMINDER_TIME', payload: value });
  }, []);

  const handleReminderUnitChange = useCallback((value: ReminderUnit) => {
    dispatch({ type: 'SET_REMINDER_UNIT', payload: value });
  }, []);

  const handleEventCategoryChange = useCallback((value: string) => {
    dispatch({ type: 'SET_EVENT_CATEGORY', payload: value });
  }, []);

  // Modal close handler
  const handleCloseModal = useCallback(() => {
    dispatch({ type: 'SET_MODAL_OPEN', payload: false });
    dispatch({ type: 'RESET_FORM' });
  }, []);

  // Save event handler
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  const handleSaveEvent = useCallback(async () => {
    if (!state.eventTitle.trim()) {
      setSaveError('Event title is required');
      return;
    }

    setIsSaving(true);
    setSaveError(null);

    try {
      const eventData = {
        title: state.eventTitle,
        start_date: state.selectedDate ? format(state.selectedDate, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd'),
        type: 'custom',
        description: state.eventDescription,
        amount: state.eventAmount ? parseFloat(state.eventAmount) : undefined,
        backgroundColor: state.eventColor || '#3b82f6',
        borderColor: state.eventColor || '#3b82f6',
        textColor: '#ffffff',
        isRecurring: state.isRecurring,
        recurrencePattern: state.isRecurring ? state.recurrencePattern : undefined,
        recurrenceEndDate: state.isRecurring && state.recurrenceEndDate ? state.recurrenceEndDate : undefined,
        recurrenceCount: state.isRecurring && state.recurrenceCount ? parseInt(state.recurrenceCount) : undefined,
        reminder: state.reminder,
        reminderTime: state.reminder ? state.reminderTime : undefined,
        reminderUnit: state.reminder ? state.reminderUnit : undefined,
        category: state.eventCategory,
        user_id: '' // Will be set by the hook
      };

      if (state.selectedEvent && state.selectedEvent.extendedProps.type === 'custom') {
        // Update existing event
        await updateCustomEvent(state.selectedEvent.id, eventData);
        addToast({ type: 'success', message: 'Event updated successfully!' });
      } else {
        // Create new event
        await addCustomEvent(eventData);
        addToast({ type: 'success', message: 'Event created successfully!' });
      }

      handleCloseModal();
      refreshData();
    } catch (err) {
      console.error('Error saving event:', err);
      setSaveError('Failed to save event. Please try again.');
      addToast({ type: 'error', message: 'Failed to save event' });
    } finally {
      setIsSaving(false);
    }
  }, [
    state.eventTitle,
    state.selectedDate,
    state.eventDescription,
    state.eventAmount,
    state.eventColor,
    state.isRecurring,
    state.recurrencePattern,
    state.recurrenceEndDate,
    state.recurrenceCount,
    state.reminder,
    state.reminderTime,
    state.reminderUnit,
    state.eventCategory,
    state.selectedEvent,
    addCustomEvent,
    updateCustomEvent,
    addToast,
    handleCloseModal,
    refreshData
  ]);

  // Delete event handler
  const handleDeleteEvent = useCallback(async () => {
    if (!state.selectedEvent || state.selectedEvent.extendedProps.type !== 'custom') return;

    setIsSaving(true);
    setSaveError(null);

    try {
      await deleteCustomEvent(state.selectedEvent.id);
      addToast({ type: 'success', message: 'Event deleted successfully!' });
      handleCloseModal();
      refreshData();
    } catch (err) {
      console.error('Error deleting event:', err);
      setSaveError('Failed to delete event. Please try again.');
      addToast({ type: 'error', message: 'Failed to delete event' });
    } finally {
      setIsSaving(false);
    }
  }, [state.selectedEvent, deleteCustomEvent, addToast, handleCloseModal, refreshData]);

  // Filter handlers
  const handleTimeHorizonChange = useCallback((value: EventTimeHorizon) => {
    dispatch({ type: 'SET_EVENT_TIME_HORIZON', payload: value });
  }, []);

  const handleTypeFilterChange = useCallback((value: string) => {
    dispatch({ type: 'SET_EVENT_TYPE_FILTER', payload: value });
  }, []);

  const handleSearchChange = useCallback((value: string) => {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: value });
  }, []);

  const handlePageChange = useCallback((page: number) => {
    dispatch({ type: 'SET_CURRENT_PAGE', payload: page });
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading calendar...</span>
      </div>
    );
  }

  if (dataError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-800">Error loading calendar: {dataError}</p>
        <button 
          onClick={refreshData}
          className="mt-2 text-red-600 hover:text-red-700 underline"
        >
          Try again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <CalendarIcon className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Financial Calendar</h1>
            <p className="text-gray-600">Track your financial events and milestones</p>
          </div>
        </div>
        
        <button
          onClick={() => {
            dispatch({ type: 'SET_SELECTED_DATE', payload: new Date() });
            dispatch({ type: 'SET_SELECTED_EVENT', payload: null });
            dispatch({ type: 'RESET_FORM' });
            dispatch({ type: 'SET_MODAL_OPEN', payload: true });
          }}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus size={20} />
          <span>Add Event</span>
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex items-center space-x-4 flex-wrap gap-4">
          <div className="flex items-center space-x-2">
            <Filter size={16} className="text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Filters:</span>
          </div>
          
          <select
            value={state.eventTimeHorizon}
            onChange={(e) => handleTimeHorizonChange(e.target.value as EventTimeHorizon)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
            <option value="all">All Events</option>
          </select>

          <select
            value={state.eventTypeFilter}
            onChange={(e) => handleTypeFilterChange(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Types</option>
            <option value="loan">Loan Payments</option>
            <option value="goal">Financial Goals</option>
            <option value="expense">Expenses</option>
            <option value="cd-maturity">CD Maturities</option>
            <option value="custom">Custom Events</option>
          </select>

          <div className="flex items-center space-x-2">
            <Search size={16} className="text-gray-500" />
            <input
              type="text"
              placeholder="Search events..."
              value={state.searchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Calendar */}
        <div className="lg:col-span-3">
          <CalendarView
            events={calendarEvents}
            onDateClick={handleDateClick}
            onEventClick={handleEventClick}
            height="600px"
          />
        </div>

        {/* Event List Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold text-gray-900">Upcoming Events</h3>
              <Settings size={16} className="text-gray-500" />
            </div>

            <div className="space-y-3">
              {paginatedEvents.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">
                  No events found
                </p>
              ) : (
                paginatedEvents.map(event => (
                  <div
                    key={event.id}
                    className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => handleEventClick({ event: { ...event, startStr: event.start, endStr: event.end, extendedProps: event.extendedProps } })}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {event.title}
                        </h4>
                        <p className="text-xs text-gray-500 mt-1">
                          {format(parseISO(event.start), 'MMM dd, yyyy')}
                        </p>
                        {event.extendedProps.amount && (
                          <p className="text-xs text-gray-600 mt-1">
                            {formatCurrency(event.extendedProps.amount)}
                          </p>
                        )}
                      </div>
                      <div
                        className="w-3 h-3 rounded-full flex-shrink-0"
                        style={{ backgroundColor: event.backgroundColor }}
                      />
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                <button
                  onClick={() => handlePageChange(Math.max(1, state.currentPage - 1))}
                  disabled={state.currentPage === 1}
                  className="flex items-center text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft size={16} />
                  <span>Previous</span>
                </button>
                
                <span className="text-sm text-gray-600">
                  Page {state.currentPage} of {totalPages}
                </span>
                
                <button
                  onClick={() => handlePageChange(Math.min(totalPages, state.currentPage + 1))}
                  disabled={state.currentPage === totalPages}
                  className="flex items-center text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span>Next</span>
                  <ChevronRight size={16} />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Event Modal */}
      <EventModal
        isOpen={state.isModalOpen}
        onClose={handleCloseModal}
        selectedDate={state.selectedDate}
        selectedEvent={state.selectedEvent}
        eventTitle={state.eventTitle}
        eventDescription={state.eventDescription}
        eventAmount={state.eventAmount}
        eventColor={state.eventColor}
        isRecurring={state.isRecurring}
        recurrencePattern={state.recurrencePattern}
        recurrenceEndDate={state.recurrenceEndDate}
        recurrenceCount={state.recurrenceCount}
        reminder={state.reminder}
        reminderTime={state.reminderTime}
        reminderUnit={state.reminderUnit}
        eventCategory={state.eventCategory}
        onEventTitleChange={handleEventTitleChange}
        onEventDescriptionChange={handleEventDescriptionChange}
        onEventAmountChange={handleEventAmountChange}
        onEventColorChange={handleEventColorChange}
        onIsRecurringChange={handleIsRecurringChange}
        onRecurrencePatternChange={handleRecurrencePatternChange}
        onRecurrenceEndDateChange={handleRecurrenceEndDateChange}
        onRecurrenceCountChange={handleRecurrenceCountChange}
        onReminderChange={handleReminderChange}
        onReminderTimeChange={handleReminderTimeChange}
        onReminderUnitChange={handleReminderUnitChange}
        onEventCategoryChange={handleEventCategoryChange}
        onSave={handleSaveEvent}
        onDelete={state.selectedEvent?.extendedProps.type === 'custom' ? handleDeleteEvent : undefined}
        isSaving={isSaving}
        error={saveError}
      />

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
    </div>
  );
}
