import { useState } from 'react';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { HelpTooltip, PageHelp } from '../components/HelpTooltip';
import { PageLoading, ErrorState, EmptyState } from '../components/LoadingStates';
import { useCashFlowConfig } from '../hooks/useCashFlowConfig';
import { usePaidMonths } from '../hooks/usePaidMonths';
import { useCashFlowCalculations } from '../hooks/useCashFlowCalculations';
import { OverviewSection } from '../components/cashflow/OverviewSection';
import { ProjectionSection } from '../components/cashflow/ProjectionSection';
import { RiskAnalysisSection } from '../components/cashflow/RiskAnalysisSection';
import { ScenarioAnalysisSection } from '../components/cashflow/ScenarioAnalysisSection';
import { AIRecommendationsSection } from '../components/cashflow/AIRecommendationsSection';
import { LoanPaymentSection } from '../components/cashflow/LoanPaymentSection';
import type { Loan, CD } from '../types/cashflow';

export function CashFlowAnalysis() {
  const { data: loans, loading: loansLoading, error: loansError } = useSupabaseQuery<Loan>('loans');
  const { data: cds, loading: cdsLoading, error: cdsError } = useSupabaseQuery<CD>('cds');
  
  const [activeTab, setActiveTab] = useState('overview');
  
  // Use custom hooks for configuration and paid months management
  const { config, setStartingBalance, setCustomLivingExpenses, setMonthsToProject } = useCashFlowConfig(loans || undefined);
  const { isMonthPaid, markMonthAsPaid, markMonthAsUnpaid } = usePaidMonths(loans || undefined);
  
  // Use custom hook for all calculations
  const {
    currentSituation,
    cashFlowProjection,
    criticalAnalysis,
    riskMetrics,
    injectionStrategy,
    scenarios,
    aiRecommendations
  } = useCashFlowCalculations({
    loans: loans || undefined,
    cds: cds || undefined,
    startingBalance: config.starting_balance,
    customLivingExpenses: config.custom_living_expenses,
    monthsToProject: config.months_to_project,
    isMonthPaid
  });

  // Loading state
  if (loansLoading || cdsLoading) {
    return <PageLoading message="Loading financial data..." icon="financial" />;
  }

  // Error state
  if (loansError || cdsError) {
    return (
      <ErrorState 
        title="Failed to load financial data"
        message="Please try refreshing the page or check your connection."
        onRetry={() => window.location.reload()} 
      />
    );
  }

  // Empty state
  if (!loans || loans.length === 0) {
    return (
      <EmptyState 
        title="No Loans Found"
        description="Add some loans to start analyzing your cash flow."
        action={{
          label: "Add Loan",
          onClick: () => {/* Navigate to loans page */}
        }}
      />
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'projection', label: 'Projection', icon: '📈' },
    { id: 'risk-analysis', label: 'Risk Analysis', icon: '⚠️' },
    { id: 'scenarios', label: 'Scenarios', icon: '🔄' },
    { id: 'loan-management', label: 'Loan Management', icon: '📅' },
    { id: 'ai-recommendations', label: 'AI Recommendations', icon: '🤖' }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header with Help */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            💹 Advanced Cash Flow Analysis
          </h1>
          <HelpTooltip content="Comprehensive analysis of your cash flow including projections, risk assessment, and AI-powered recommendations." />
        </div>
        <div className={`px-4 py-2 rounded-lg ${riskMetrics.bgColor}`}>
          <span className={`font-semibold ${riskMetrics.color}`}>
            {riskMetrics.level} Risk
          </span>
        </div>
      </div>

      {/* Page Help */}
      <PageHelp
        title="Cash Flow Analysis"
        description="Analyze your financial situation with advanced projections, risk assessment, and AI recommendations."
        tips={[
          "Real-time cash flow projections",
          "Risk assessment and early warning system",
          "Scenario analysis for different expense levels",
          "AI-powered financial recommendations",
          "Loan payment tracking and management"
        ]}
      />

      {/* Configuration Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Starting Balance
            <HelpTooltip content="Your current account balance to start projections from" />
          </label>
          <input
            type="number"
            value={config.starting_balance}
            onChange={(e) => setStartingBalance(Number(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Monthly Living Expenses
            <HelpTooltip content="Your estimated monthly living expenses not covered by CD income" />
          </label>
          <input
            type="number"
            value={config.custom_living_expenses}
            onChange={(e) => setCustomLivingExpenses(Number(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Projection Period (Months)
            <HelpTooltip content="How many months ahead to project your cash flow" />
          </label>
          <input
            type="number"
            min="12"
            max="240"
            value={config.months_to_project}
            onChange={(e) => setMonthsToProject(Number(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          />
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <OverviewSection 
          currentSituation={currentSituation}
          riskMetrics={riskMetrics}
        />
      )}

      {activeTab === 'projection' && (
        <ProjectionSection 
          cashFlowProjection={cashFlowProjection}
        />
      )}

      {activeTab === 'risk-analysis' && (
        <RiskAnalysisSection 
          riskMetrics={riskMetrics}
          injectionStrategy={injectionStrategy}
          criticalAnalysis={criticalAnalysis}
        />
      )}

      {activeTab === 'scenarios' && (
        <ScenarioAnalysisSection 
          scenarios={scenarios}
        />
      )}

      {activeTab === 'loan-management' && (
        <LoanPaymentSection 
          loans={loans}
          isMonthPaid={isMonthPaid}
          markMonthAsPaid={markMonthAsPaid}
          markMonthAsUnpaid={markMonthAsUnpaid}
        />
      )}

      {activeTab === 'ai-recommendations' && (
        <AIRecommendationsSection 
          aiRecommendations={aiRecommendations}
        />
      )}
    </div>
  );
}
