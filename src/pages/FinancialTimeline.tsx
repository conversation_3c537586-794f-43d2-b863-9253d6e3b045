import { useState, useEffect } from 'react';
import { Loan, CD } from '../types/index';
import type { Expense } from '../types/expenses';
import { Transaction } from '../types/index'; // Import Transaction
import { formatCurrency } from '../utils/currency';
import { addMonths, format, compareAsc, isBefore } from 'date-fns';
import { calculateAmortizationSchedule } from '../utils/loanCalculator';
import { localDataStore } from '../lib/local-data-store'; // Import localDataStore

// Define the structure for timeline events
interface TimelineEvent {
  id: string;
  title: string;
  description: string;
  date: Date;
  amount: number;
  type: 'loan-payment' | 'loan-payoff' | 'cd-maturity' | 'milestone' | 'expense' | 'transaction';
  entityId: string;
  color: string;
}

export function FinancialTimeline() {
  const [events, setEvents] = useState<TimelineEvent[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<TimelineEvent[]>([]);
  const [filterTypes, setFilterTypes] = useState<string[]>([
    'loan-payoff',
    'cd-maturity',
    'milestone',
    'expense', // Added expense to default filters
    'transaction' // Added transaction to default filters
  ]);
  const [timeRange, setTimeRange] = useState<number>(24); // months

  // State and loading for local data
  const [loans, setLoans] = useState<Loan[]>([]);
  const [loansLoading, setLoansLoading] = useState(true);

  const [cds, setCds] = useState<CD[]>([]);
  const [cdsLoading, setCdsLoading] = useState(true);

  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [expensesLoading, setExpensesLoading] = useState(true);

  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [transactionsLoading, setTransactionsLoading] = useState(true);

  // Fetch data from localDataStore on component mount
  useEffect(() => {
    const fetchData = async () => {
      setLoansLoading(true);
      setCdsLoading(true);
      setExpensesLoading(true);
      setTransactionsLoading(true); // Set loading for transactions
      try {
        // Use localDataStore to load collections
        const [loansData, cdsData, expensesData, transactionsData] = await Promise.all([ // Include transactionsData
          localDataStore.data.loadCollection<Loan>('loans'),
          localDataStore.data.loadCollection<CD>('cds'),
          localDataStore.data.loadCollection<Expense>('expenses'),
          localDataStore.data.loadCollection<Transaction>('transactions'), // Load transactions
        ]);
        setLoans(loansData);
        setCds(cdsData);
        setExpenses(expensesData);
        setTransactions(transactionsData); // Set transactions state
      } catch (error) {
        console.error('Error loading financial data:', error);
        // Handle error appropriately, e.g., display an error message
      } finally {
        setLoansLoading(false);
        setCdsLoading(false);
        setExpensesLoading(false);
        setTransactionsLoading(false); // Set loading to false for transactions
      }
    };

    fetchData();
  }, []); // Empty dependency array means this effect runs once on mount

  // Generate all timeline events when loans, CDs, expenses, or transactions change
  useEffect(() => {
    if ((loans && loans.length > 0) || (cds && cds.length > 0) || (expenses && expenses.length > 0) || (transactions && transactions.length > 0)) { // Include transactions in condition
      const allEvents: TimelineEvent[] = [];

      // Process loan payoff events
      if (loans) {
        loans.forEach((loan: Loan) => {
          const payoffDate = calculateLoanPayoffDate(loan);

          allEvents.push({
            id: `loan-payoff-${loan.id}`,
            title: `${loan.name} Paid Off`,
            description: `Loan "${loan.name}" will be fully paid off`,
            date: payoffDate,
            amount: loan.remaining_balance,
            type: 'loan-payoff',
            entityId: loan.id,
            color: '#10B981' // Emerald/green for positive payoff events
          });

          const milestones = calculateLoanMilestones(loan);
          milestones.forEach(milestone => {
            allEvents.push({
              id: `loan-milestone-${loan.id}-${milestone.percentage}`,
              title: `${loan.name} ${milestone.percentage}% Paid`,
              description: `Loan "${loan.name}" will be ${milestone.percentage}% paid off`,
              date: milestone.date,
              amount: milestone.amount,
              type: 'milestone',
              entityId: loan.id,
              color: '#8B5CF6' // Purple for milestones
            });
          });
        });
      }

      // Process CD maturity events
      if (cds) {
        cds.forEach((cd: CD) => {
          const maturityDate = new Date(cd.maturity_date);

          allEvents.push({
            id: `cd-maturity-${cd.id}`,
            title: `${cd.institution} CD Matures`,
            description: `Certificate of Deposit at ${cd.institution} will mature`,
            date: maturityDate,
            amount: cd.principal,
            type: 'cd-maturity',
            entityId: cd.id,
            color: '#3B82F6' // Blue for CD events
          });
        });
      }

      // Process expense events
      if (expenses) {
        expenses.forEach((expense: Expense) => {
          allEvents.push({
            id: `expense-${expense.id}`,
            title: `Expense: ${expense.category}`,
            description: expense.description || 'No description',
            date: new Date(expense.date),
            amount: expense.amount,
            type: 'expense',
            entityId: expense.id,
            color: '#EF4444' // Red for expenses
          });
        });
      }

      // Process transaction events (includes income and other transactions)
      if (transactions) {
        transactions.forEach((transaction: Transaction) => {
          allEvents.push({
            id: `transaction-${transaction.id}`, // Assuming transactions have an id
            title: transaction.amount > 0 ? 'Income' : 'Transaction', // Differentiate income
            description: transaction.description || 'No description',
            date: new Date(transaction.date),
            amount: transaction.amount,
            type: 'transaction',
            entityId: transaction.id,
            color: transaction.amount > 0 ? '#059669' : '#F59E0B' // Green for income, amber for other transactions
          });
        });
      }


      // Sort all events by date
      allEvents.sort((a, b) => compareAsc(a.date, b.date));

      setEvents(allEvents);
    }
  }, [loans, cds, expenses, transactions]); // Ensure all data dependencies are here

  // Filter events based on type and time range
  useEffect(() => {
    if (events.length > 0) {
      const now = new Date();
      const endDate = addMonths(now, timeRange);

      const filtered = events.filter(event =>
        filterTypes.includes(event.type) &&
        isBefore(event.date, endDate)
      );

      setFilteredEvents(filtered);
    }
  }, [events, filterTypes, timeRange]);

  // Calculate when a loan will be fully paid off
  const calculateLoanPayoffDate = (loan: Loan): Date => {
    const startDate = new Date(loan.next_payment_date);
    const schedule = calculateAmortizationSchedule(
      loan.principal,
      loan.interest_rate,
      loan.term_months,
      startDate,
      loan.name,
      true, // Use cache
      loan.end_date ? new Date(loan.end_date) : undefined // Pass end_date if available
    );

    // The payoff date is the due date of the last payment in the schedule
    if (schedule.length > 0) {
      return schedule[schedule.length - 1].dueDate;
    }

    // Fallback to simplified calculation if schedule is empty
    return addMonths(startDate, loan.term_months);
  };

  // Calculate significant loan payment milestones
  const calculateLoanMilestones = (loan: Loan): { percentage: number; date: Date; amount: number }[] => {
    const milestones: { percentage: number; date: Date; amount: number }[] = [];
    const startDate = new Date(loan.next_payment_date);
    const schedule = calculateAmortizationSchedule(
      loan.principal,
      loan.interest_rate,
      loan.term_months,
      startDate,
      loan.name,
      true, // Use cache
      loan.end_date ? new Date(loan.end_date) : undefined // Pass end_date if available
    );

    if (schedule.length === 0) {
      return []; // Cannot calculate milestones without a schedule
    }

    const totalPrincipalPaid = loan.principal; // Assuming principal is the original principal

    [25, 50, 75].forEach(percentage => {
      const targetPrincipalPaid = totalPrincipalPaid * (percentage / 100);
      let accumulatedPrincipal = 0;

      for (const payment of schedule) {
        accumulatedPrincipal += payment.principalComponent;
        if (accumulatedPrincipal >= targetPrincipalPaid) {
          milestones.push({
            percentage,
            date: payment.dueDate,
            amount: accumulatedPrincipal // Amount paid up to this milestone
          });
          return; // Move to the next percentage
        }
      }
    });

    return milestones;
  };

  // Toggle event type filtering
  const toggleFilterType = (type: string) => {
    if (filterTypes.includes(type)) {
      setFilterTypes(filterTypes.filter(t => t !== type));
    } else {
      setFilterTypes([...filterTypes, type]);
    }
  };

  // Loading state
  if (loansLoading || cdsLoading || expensesLoading || transactionsLoading) { // Include transactionsLoading
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  // Group events by month-year for display
  const groupedEvents: Record<string, TimelineEvent[]> = {};
  filteredEvents.forEach(event => {
    const monthYear = format(event.date, 'MMM yyyy');
    if (!groupedEvents[monthYear]) {
      groupedEvents[monthYear] = [];
    }
    groupedEvents[monthYear].push(event);
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Financial Timeline</h1>
      </div>

      <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow">
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Timeline Settings</h2>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Time Range
            </label>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(Number(e.target.value))}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-500 dark:text-white"
            >
              <option value={6}>6 months</option>
              <option value={12}>1 year</option>
              <option value={24}>2 years</option>
              <option value={36}>3 years</option>
              <option value={60}>5 years</option>
              <option value={120}>10 years</option>
            </select>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Event Types
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => toggleFilterType('loan-payoff')}
                className={`px-3 py-1 rounded-full text-sm ${
                  filterTypes.includes('loan-payoff')
                    ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-800 dark:text-emerald-100'
                    : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                }`}
              >
                Loan Payoffs
              </button>
              <button
                onClick={() => toggleFilterType('milestone')}
                className={`px-3 py-1 rounded-full text-sm ${
                  filterTypes.includes('milestone')
                    ? 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100'
                    : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                }`}
              >
                Loan Milestones
              </button>
              <button
                onClick={() => toggleFilterType('cd-maturity')}
                className={`px-3 py-1 rounded-full text-sm ${
                  filterTypes.includes('cd-maturity')
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                    : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                }`}
              >
                CD Maturities
              </button>
              <button
                onClick={() => toggleFilterType('expense')}
                className={`px-3 py-1 rounded-full text-sm ${
                  filterTypes.includes('expense')
                    ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                    : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                }`}
              >
                Expenses
              </button>
              <button
                onClick={() => toggleFilterType('transaction')}
                className={`px-3 py-1 rounded-full text-sm ${
                  filterTypes.includes('transaction')
                    ? 'bg-yellow-500 text-yellow-900 dark:bg-yellow-700 dark:text-yellow-100' // Using yellow for transactions
                    : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                }`}
              >
                Transactions
              </button>
            </div>
          </div>
        </div>

        {filteredEvents.length === 0 ? (
          <div className="bg-gray-50 dark:bg-dark-700 p-4 rounded-lg text-center">
            <p className="text-gray-700 dark:text-gray-300">
              No financial events found in the selected time range. Try extending the timeline or adding more loans and CDs.
            </p>
          </div>
        ) : (
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-5 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"></div>

            {/* Timeline events */}
            <div className="space-y-8 relative pl-14">
              {Object.entries(groupedEvents).map(([monthYear, monthEvents]) => (
                <div key={monthYear} className="relative">
                  {/* Month label */}
                  <div className="absolute -left-14 top-0 flex items-center justify-center w-10 h-10 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-200 font-medium text-sm">
                    <div className="text-center">
                      {monthYear.split(' ')[0]}
                    </div>
                  </div>

                  {/* Month header */}
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    {monthYear}
                  </h3>

                  {/* Event cards for this month */}
                  <div className="space-y-4">
                    {monthEvents.map(event => (
                      <div
                        key={event.id}
                        className="bg-white dark:bg-dark-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4 shadow-sm relative"
                        style={{ borderLeftWidth: '4px', borderLeftColor: event.color }}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="text-md font-semibold text-gray-900 dark:text-white">{event.title}</h4>
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{event.description}</p>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                              {format(event.date, 'EEEE, MMMM d, yyyy')}
                            </p>
                          </div>
                          <div className="text-right">
                            <span className="text-lg font-medium" style={{ color: event.color }}>
                              {formatCurrency(event.amount)}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
