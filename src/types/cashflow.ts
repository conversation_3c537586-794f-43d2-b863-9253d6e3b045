// Cash Flow Analysis Types
export interface Loan {
  id: string;
  name: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  remaining_balance: number;
  monthly_payment: number;
  start_date: string;
  next_payment_date: string;
  end_date: string;
}

export interface CD {
  id: string;
  institution: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  maturity_date: string;
}

export interface CashFlowProjection {
  period: number;
  date: string;
  starting_balance: number;
  monthly_income: number;
  living_expenses: number;
  loan_payments: number;
  cd_income_for_loans: number;
  loan_shortfall: number;
  total_outflow: number;
  net_change: number;
  ending_balance: number;
  active_loans: number;
  status: 'Positive' | 'Negative';
}

export interface FinancialSituation {
  total_cd_income: number;
  cib_available: number;
  cd_for_loans: number;
  total_loan_payments: number;
  funding_gap: number;
  estimated_living_expenses: number;
  monthly_shortfall: number;
  cd_coverage_percentage: number;
}

export interface RiskMetrics {
  level: string;
  color: string;
  bgColor: string;
  monthsToNegative: number;
  maxDeficit: number;
  cashRunway: number;
  stressLevel: string;
}

export interface InjectionStrategy {
  name: string;
  description: string;
  injection_amount: number;
  injection_period: number;
  injection_date: string;
  type: 'emergency' | 'strategic' | 'staged' | 'restructuring';
}

export interface ScenarioResult {
  name: string;
  monthly_savings: number;
  annual_savings: number;
  new_monthly_expense: number;
  months_to_breakeven: number;
  achieves_positive_flow: boolean;
  projection: CashFlowProjection[];
}

export interface PaidMonth {
  id?: string;
  loan_id: string;
  month: string; // Format: 'YYYY-MM'
}

export interface CashFlowConfig {
  id?: string;
  starting_balance: number;
  custom_living_expenses: number;
  months_to_project: number;
}

export interface AIRecommendation {
  priority: string;
  action: string;
  details: string;
  category: 'liquidity' | 'debt' | 'optimization' | 'investment';
}
