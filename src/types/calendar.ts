/**
 * Calendar Component Types
 * Extracted from Calendar.tsx for better organization and reusability
 */

export interface Loan {
  id: string;
  user_id: string;
  name: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  remaining_balance: number;
  monthly_payment: number;
  next_payment_date: string;
  end_date?: string;
  created_at?: string;
}

export interface CD {
  id: string;
  user_id: string;
  institution: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  maturity_date: string;
}

export interface FinancialGoal {
  id: string;
  user_id: string;
  name: string;
  target_amount: number;
  target_date: string;
  notes: string;
}

export interface Expense {
  id: string;
  user_id: string;
  amount: number;
  category: string;
  description: string;
  date: string;
}

export interface CalendarEvent {
  id: string;
  title: string;
  start: string;
  end?: string;
  backgroundColor: string;
  borderColor: string;
  textColor: string;
  allDay?: boolean;
  className?: string;
  extendedProps: {
    type: 'loan' | 'goal' | 'expense' | 'custom' | 'cd-maturity';
    amount?: number;
    description?: string;
    icon?: string;
    recurrence?: boolean;
    // Additional properties for loan payments
    principalPayment?: number;
    interestPayment?: number;
    remainingBalance?: number;
    percentPaid?: number;
  };
}

export interface CustomEvent {
  id: string;
  user_id: string;
  title: string;
  start_date: string;
  end_date?: string;
  type: string;
  amount?: number;
  description?: string;
  created_at: string;
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  isRecurring?: boolean;
  recurrencePattern?: RecurrencePattern;
  recurrenceEndDate?: string;
  recurrenceCount?: number;
  reminder?: boolean;
  reminderTime?: number;
  reminderUnit?: ReminderUnit;
  reminderSent?: boolean;
  category?: string;
  templateId?: string;
}

export type RecurrencePattern = 'daily' | 'weekly' | 'monthly' | 'yearly';
export type ReminderUnit = 'minutes' | 'hours' | 'days';
export type EventTimeHorizon = 'week' | 'month' | 'quarter' | 'year' | 'all';

export interface EventCategory {
  id: string;
  label: string;
  icon: string;
}

export interface CalendarState {
  // Modal state
  isModalOpen: boolean;
  selectedDate: Date | null;
  selectedEvent: CalendarEvent | null;
  
  // Data state
  loans: Loan[];
  cds: CD[];
  goals: FinancialGoal[];
  expenses: Expense[];
  customEvents: CustomEvent[];
  
  // Form state
  eventTitle: string;
  eventDescription: string;
  eventAmount: string;
  eventColor: string;
  isRecurring: boolean;
  recurrencePattern: RecurrencePattern;
  recurrenceEndDate: string;
  recurrenceCount: string;
  reminder: boolean;
  reminderTime: number;
  reminderUnit: ReminderUnit;
  eventCategory: string;
  
  // Filter and pagination state
  eventTimeHorizon: EventTimeHorizon;
  eventTypeFilter: string;
  eventCategoryFilter: string;
  searchQuery: string;
  currentPage: number;
}

export type CalendarAction =
  | { type: 'SET_MODAL_OPEN'; payload: boolean }
  | { type: 'SET_SELECTED_DATE'; payload: Date | null }
  | { type: 'SET_SELECTED_EVENT'; payload: CalendarEvent | null }
  | { type: 'SET_LOANS'; payload: Loan[] }
  | { type: 'SET_CDS'; payload: CD[] }
  | { type: 'SET_GOALS'; payload: FinancialGoal[] }
  | { type: 'SET_EXPENSES'; payload: Expense[] }
  | { type: 'SET_CUSTOM_EVENTS'; payload: CustomEvent[] }
  | { type: 'SET_EVENT_TITLE'; payload: string }
  | { type: 'SET_EVENT_DESCRIPTION'; payload: string }
  | { type: 'SET_EVENT_AMOUNT'; payload: string }
  | { type: 'SET_EVENT_COLOR'; payload: string }
  | { type: 'SET_IS_RECURRING'; payload: boolean }
  | { type: 'SET_RECURRENCE_PATTERN'; payload: RecurrencePattern }
  | { type: 'SET_RECURRENCE_END_DATE'; payload: string }
  | { type: 'SET_RECURRENCE_COUNT'; payload: string }
  | { type: 'SET_REMINDER'; payload: boolean }
  | { type: 'SET_REMINDER_TIME'; payload: number }
  | { type: 'SET_REMINDER_UNIT'; payload: ReminderUnit }
  | { type: 'SET_EVENT_CATEGORY'; payload: string }
  | { type: 'SET_EVENT_TIME_HORIZON'; payload: EventTimeHorizon }
  | { type: 'SET_EVENT_TYPE_FILTER'; payload: string }
  | { type: 'SET_EVENT_CATEGORY_FILTER'; payload: string }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_CURRENT_PAGE'; payload: number }
  | { type: 'RESET_FORM' };

export interface CalendarEventGenerationConfig {
  generateLoanPayments: boolean;
  generateGoalMilestones: boolean;
  generateExpenseReminders: boolean;
  generateCDMaturity: boolean;
  timeHorizon: EventTimeHorizon;
}

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
}
