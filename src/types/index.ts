export interface User {
  id: string;
  email: string;
  created_at: string;
}

export interface Budget {
  id: string;
  user_id: string;
  category: string;
  amount: number;
  spent: number;
  period: 'monthly' | 'yearly';
  created_at: string;
}

export interface Expense {
  id: string;
  user_id: string;
  amount: number;
  category: string;
  description: string;
  date: string;
  frequency: 'monthly' | 'quarterly' | 'annual' | 'one-time';
  dueDate?: string;
  created_at: string;
}

export interface Loan {
  id: string;
  user_id: string;
  name: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  remaining_balance: number;
  monthly_payment: number;
  start_date: string;
  next_payment_date: string;
  end_date: string;
  created_at: string;
}

export interface LoanPayment {
  id: string;
  user_id: string;
  loan_id: string;
  amount: number;
  payment_date: string;
  created_at: string;
}

export interface CD {
  id: string;
  user_id: string;
  name: string;
  institution: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  maturity_date: string;
  created_at: string;
}

export interface BankAccount {
  id: string;
  user_id: string;
  bank_name: string;
  account_name: string;
  account_number: string;
  balance: number;
  account_type: 'checking' | 'savings' | 'investment';
  created_at: string;
}

export interface FinancialGoal {
  id: string;
  user_id: string;
  name: string;
  target_amount: number;
  current_amount: number;
  category: 'savings' | 'debt' | 'investment' | 'emergency' | 'custom';
  target_date: string;
  priority: 'low' | 'medium' | 'high';
  notes: string;
  created_at: string;
}

// Define the interface for the PyWebView API
export interface PyWebViewApi {
  get_item(key: string): Promise<string | null>;
  set_item(key: string, value: string): Promise<boolean>;
  remove_item(key: string): Promise<boolean>;
  clean_indexes(): Promise<string>; // Assuming it returns a string as per DataStorage
  repair_storage(): Promise<string>; // Assuming it returns a string as per DataStorage
  get_storage_stats(): Promise<string>; // Assuming it returns a string as per DataStorage
  get_collection(collection: string): Promise<string>; // Assuming it returns a string as per DataStorage
  get_cash_flow_forecast(start_date: string, initial_balance: number, income_sources: any[], expenses: any[], loan_payments: any[], duration_months: number): Promise<any[] | { error: string }>;
  run_financial_plan_projection(planner_settings_json_string?: string): Promise<string>; // Advanced projection engine
  
  // Investment-related methods
  save_investment_portfolio(portfolio_data: any): Promise<string>;
  get_investment_portfolios(): Promise<string>;
  save_stock_holding(holding_data: any): Promise<string>;
  get_portfolio_holdings(portfolio_id: string): Promise<string>;
  update_stock_holding(holding_id: string, updates: any): Promise<string>;
  delete_stock_holding(holding_id: string): Promise<string>;
  save_trading_transaction(transaction_data: any): Promise<string>;
  get_trading_transactions(portfolio_id?: string): Promise<string>;
  
  // Add other methods from DataStorage if they are exposed
}

// Declare the pywebview object on the Window interface
declare global {
  interface Window {
    pywebview: {
      api: PyWebViewApi;
      // pywebview_channel might also exist depending on version, but let's focus on api for now
      pywebview_channel?: { // Make optional
          call: (method: string, ...args: any[]) => Promise<any>;
      };
    };
  }
}


export interface NotificationPreferences {
  id: string;
  user_id: string;
  payment_reminders: boolean;
  goal_updates: boolean;
  monthly_reports: boolean;
  created_at: string;
}

export interface PlaidAccount {
  id: string;
  name: string;
  mask: string;
  type: string;
  subtype: string;
  balance: number;
}

export interface Asset {
  id: string;
  user_id: string;
  name: string;
  category: string;
  value: number;
  acquisition_date: string;
  notes?: string;
  created_at: string;
}

export interface Liability {
  id: string;
  user_id: string;
  name: string;
  category: string;
  amount: number;
  interest_rate: number;
  notes?: string;
  created_at: string;
}

export interface Transaction {
  id: string;
  description: string;
  date: string;
  amount: number;
}
