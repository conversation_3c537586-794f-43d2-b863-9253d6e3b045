import { z } from 'zod';

// Base schemas
export const DateStringSchema = z.string().refine(
  (date) => !isNaN(Date.parse(date)),
  { message: "Invalid date string" }
);

export const CurrencySchema = z.number().min(0, "Amount must be positive");

// Loan schema
export const LoanSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, "Loan name is required"),
  principal: CurrencySchema,
  interest_rate: z.number().min(0).max(100, "Interest rate must be between 0-100%"),
  term_months: z.number().int().min(1, "Term must be at least 1 month"),
  remaining_balance: CurrencySchema,
  monthly_payment: CurrencySchema,
  next_payment_date: DateStringSchema,
  created_at: DateStringSchema.optional(),
});

export type Loan = z.infer<typeof LoanSchema>;

// CD schema
export const CDSchema = z.object({
  id: z.string().uuid(),
  institution: z.string().min(1, "Institution name is required"),
  principal: CurrencySchema,
  interest_rate: z.number().min(0).max(100, "Interest rate must be between 0-100%"),
  term_months: z.number().int().min(1, "Term must be at least 1 month"),
  maturity_date: DateStringSchema,
  created_at: DateStringSchema.optional(),
});

export type CD = z.infer<typeof CDSchema>;

// Bank Account schema
export const BankAccountSchema = z.object({
  id: z.string().uuid(),
  bank_name: z.string().min(1, "Bank name is required"),
  account_type: z.enum(['checking', 'savings', 'money_market', 'other']),
  balance: z.number(), // Can be negative for overdrafts
  created_at: DateStringSchema.optional(),
});

export type BankAccount = z.infer<typeof BankAccountSchema>;

// Financial Goal schema
export const FinancialGoalSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, "Goal name is required"),
  target_amount: CurrencySchema,
  current_amount: CurrencySchema.default(0),
  target_date: DateStringSchema,
  category: z.enum(['emergency_fund', 'retirement', 'house', 'vacation', 'education', 'other']).default('other'),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
  created_at: DateStringSchema.optional(),
});

export type FinancialGoal = z.infer<typeof FinancialGoalSchema>;

// Budget schema
export const BudgetSchema = z.object({
  id: z.string().uuid(),
  category: z.string().min(1, "Category is required"),
  budgeted_amount: CurrencySchema,
  spent_amount: CurrencySchema.default(0),
  month: z.string().regex(/^\d{4}-\d{2}$/, "Month must be in YYYY-MM format"),
  created_at: DateStringSchema.optional(),
});

export type Budget = z.infer<typeof BudgetSchema>;

// Expense schema
export const ExpenseSchema = z.object({
  id: z.string().uuid(),
  description: z.string().min(1, "Description is required"),
  amount: CurrencySchema,
  category: z.string().min(1, "Category is required"),
  date: DateStringSchema,
  payment_method: z.enum(['cash', 'credit_card', 'debit_card', 'bank_transfer', 'other']).default('other'),
  tags: z.array(z.string()).default([]),
  created_at: DateStringSchema.optional(),
});

export type Expense = z.infer<typeof ExpenseSchema>;

// Financial Event schema (for calendar)
export const FinancialEventSchema = z.object({
  id: z.string().uuid(),
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  date: DateStringSchema,
  type: z.enum(['payment', 'income', 'maturity', 'goal_deadline', 'reminder', 'other']),
  amount: z.number().optional(),
  recurring: z.boolean().default(false),
  recurring_pattern: z.enum(['daily', 'weekly', 'monthly', 'yearly']).optional(),
  created_at: DateStringSchema.optional(),
});

export type FinancialEvent = z.infer<typeof FinancialEventSchema>;

// Collection schemas for bulk operations
export const LoanCollectionSchema = z.array(LoanSchema);
export const CDCollectionSchema = z.array(CDSchema);
export const BankAccountCollectionSchema = z.array(BankAccountSchema);
export const FinancialGoalCollectionSchema = z.array(FinancialGoalSchema);
export const BudgetCollectionSchema = z.array(BudgetSchema);
export const ExpenseCollectionSchema = z.array(ExpenseSchema);
export const FinancialEventCollectionSchema = z.array(FinancialEventSchema);

// Validation helper functions
export function validateLoan(data: unknown): Loan {
  return LoanSchema.parse(data);
}

export function validateCD(data: unknown): CD {
  return CDSchema.parse(data);
}

export function validateBankAccount(data: unknown): BankAccount {
  return BankAccountSchema.parse(data);
}

export function validateFinancialGoal(data: unknown): FinancialGoal {
  return FinancialGoalSchema.parse(data);
}

export function validateBudget(data: unknown): Budget {
  return BudgetSchema.parse(data);
}

export function validateExpense(data: unknown): Expense {
  return ExpenseSchema.parse(data);
}

export function validateFinancialEvent(data: unknown): FinancialEvent {
  return FinancialEventSchema.parse(data);
}

// Partial schemas for updates
export const LoanUpdateSchema = LoanSchema.partial().omit({ id: true });
export const CDUpdateSchema = CDSchema.partial().omit({ id: true });
export const BankAccountUpdateSchema = BankAccountSchema.partial().omit({ id: true });
export const FinancialGoalUpdateSchema = FinancialGoalSchema.partial().omit({ id: true });
export const BudgetUpdateSchema = BudgetSchema.partial().omit({ id: true });
export const ExpenseUpdateSchema = ExpenseSchema.partial().omit({ id: true });
export const FinancialEventUpdateSchema = FinancialEventSchema.partial().omit({ id: true });

export type LoanUpdate = z.infer<typeof LoanUpdateSchema>;
export type CDUpdate = z.infer<typeof CDUpdateSchema>;
export type BankAccountUpdate = z.infer<typeof BankAccountUpdateSchema>;
export type FinancialGoalUpdate = z.infer<typeof FinancialGoalUpdateSchema>;
export type BudgetUpdate = z.infer<typeof BudgetUpdateSchema>;
export type ExpenseUpdate = z.infer<typeof ExpenseUpdateSchema>;
export type FinancialEventUpdate = z.infer<typeof FinancialEventUpdateSchema>;
