/**
 * Investment and Portfolio Management Types
 */

// Core Investment Types
export interface Stock {
  id: string;
  user_id?: string;
  symbol: string;
  name: string;
  price: number;
  change: number;
  volume: number;
  market_cap: number;
  liquidity_zone: {
    support: number;
    resistance: number;
  };
  order_blocks: {
    bullish: number[];
    bearish: number[];
  };
  ai_score: number;
  recommendation: 'Buy' | 'Sell' | 'Hold';
  analysis: {
    smcSignals: {
      choch: boolean;
      mss: boolean;
      fvg: Array<{
        price: number;
        direction: 'up' | 'down';
      }>;
    };
    predictions: {
      short_term: string;
      medium_term: string;
      long_term: string;
    };
    risk_analysis: {
      volatility: number;
      beta: number;
      sharpe_ratio: number;
    };
  };
  created_at: string;
  updated_at: string;
}

// Basic stock price data (used for quick quotes)
export interface StockQuote {
  symbol: string;
  price: number;
  change: number;
  change_percentage: number;
  volume: number;
  last_updated: string;
}

// Stock holding in a portfolio
export interface StockHolding {
  id: string;
  user_id?: string;
  portfolio_id: string;
  symbol: string;
  company_name: string;
  shares: number;
  purchase_price: number;
  purchase_date: string;
  average_cost: number;
  total_cost: number;
  current_price?: number;
  current_value?: number;
  unrealized_gain_loss?: number;
  unrealized_gain_loss_percentage?: number;
  fees: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}


export interface StockData {
  id: string;
  stock_id: string;
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface Bond {
  id: string;
  user_id: string;
  name: string;
  issuer: string;
  face_value: number;
  coupon_rate: number;
  maturity_date: string;
  current_price: number;
  yield_to_maturity: number;
  rating: string;
  bond_type: 'government' | 'corporate' | 'municipal';
  price_change?: number; // Daily price change in currency units
  price_change_percent?: number; // Daily price change percentage
  created_at: string;
  updated_at: string;
}

// Portfolio Management
export interface Portfolio {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  initial_balance: number;
  current_balance: number;
  currency: string;
  created_at: string;
  updated_at: string;
}

export interface PortfolioHolding {
  id: string;
  portfolio_id: string;
  asset_type: 'stock' | 'bond';
  asset_id: string;
  quantity: number;
  average_cost: number;
  current_value: number;
  gain_loss: number;
  gain_loss_percentage: number;
  asset_details?: Stock | Bond;
}

export interface Trade {
  id: string;
  portfolio_id: string;
  stock_id?: string;
  bond_id?: string;
  type: 'buy' | 'sell';
  quantity: number;
  price: number;
  fees: number;
  notes?: string;
  executed_at: string;
  created_at: string;
}

// Trading transaction record
export interface TradingTransaction {
  id: string;
  user_id: string;
  portfolio_id: string;
  symbol: string;
  transaction_type: 'buy' | 'sell';
  shares: number;
  price: number;
  total_amount: number;
  fees: number;
  notes?: string;
  transaction_date: string;
  created_at: string;
}

// Watchlist Management
export interface Watchlist {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface WatchlistItem {
  id: string;
  watchlist_id: string;
  stock_id: string;
  notes?: string;
  added_at: string;
  stock_details?: Stock;
}

// Alerts and Notifications
export interface Alert {
  id: string;
  user_id: string;
  stock_id: string;
  type: 'price' | 'technical' | 'news' | 'volume';
  condition: {
    operator: 'greater_than' | 'less_than' | 'equal_to';
    value: number;
    field: string;
  };
  active: boolean;
  triggered_at?: string;
  created_at: string;
  updated_at: string;
}

// Analysis and Insights
export interface TechnicalPattern {
  id: string;
  stock_id: string;
  pattern_type: string;
  confidence: number;
  price_level: number;
  detected_at: string;
  validated: boolean;
  validated_at?: string;
  created_at: string;
}

export interface Correlation {
  id: string;
  stock_id_1: string;
  stock_id_2: string;
  correlation: number;
  period: string;
  calculated_at: string;
}

// Investment Performance
export interface PerformanceMetrics {
  total_return: number;
  total_return_percentage: number;
  daily_return: number;
  daily_return_percentage: number;
  volatility: number;
  sharpe_ratio: number;
  max_drawdown: number;
  win_rate: number;
  profit_factor: number;
}

export interface PortfolioPerformance {
  portfolio_id: string;
  metrics: PerformanceMetrics;
  historical_values: Array<{
    date: string;
    value: number;
    daily_return: number;
  }>;
  asset_allocation: Array<{
    asset_type: 'stock' | 'bond';
    percentage: number;
    value: number;
  }>;
  top_performers: PortfolioHolding[];
  worst_performers: PortfolioHolding[];
}

// Search and Filtering
export interface InvestmentSearchFilters {
  query?: string;
  asset_type?: 'stock' | 'bond' | 'all';
  sector?: string;
  market_cap_min?: number;
  market_cap_max?: number;
  price_min?: number;
  price_max?: number;
  volume_min?: number;
  rating?: string;
  recommendation?: 'Buy' | 'Sell' | 'Hold';
}

export interface InvestmentSearchResult {
  stocks: Stock[];
  bonds: Bond[];
  total_count: number;
  page: number;
  per_page: number;
}

// Market Data
export interface MarketOverview {
  market_status: 'open' | 'closed' | 'pre_market' | 'after_hours';
  major_indices: Array<{
    name: string;
    value: number;
    change: number;
    change_percentage: number;
  }>;
  market_sentiment: 'bullish' | 'bearish' | 'neutral';
  top_gainers: Stock[];
  top_losers: Stock[];
  most_active: Stock[];
}

// News and Events
export interface News {
  id: string;
  title: string;
  content: string;
  source: string;
  url?: string;
  published_at: string;
  sentiment?: number;
  created_at: string;
}

export interface StockNews {
  stock_id: string;
  news_id: string;
  relevance: number;
  news_details?: News;
}

// UI State Types
export interface InvestmentUIState {
  selectedPortfolio?: Portfolio;
  selectedStock?: Stock;
  selectedBond?: Bond;
  viewMode: 'grid' | 'list' | 'chart';
  timeframe: '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | '5Y';
  chartType: 'line' | 'candlestick' | 'area';
  isLoading: boolean;
  error?: string;
}

// Form Types
export interface CreatePortfolioForm {
  name: string;
  description?: string;
  initial_balance: number;
  currency: string;
}

export interface CreateTradeForm {
  portfolio_id: string;
  asset_type: 'stock' | 'bond';
  asset_id: string;
  type: 'buy' | 'sell';
  quantity: number;
  price: number;
  fees: number;
  notes?: string;
}

export interface CreateWatchlistForm {
  name: string;
  description?: string;
  stock_ids: string[];
}

export interface CreateAlertForm {
  stock_id: string;
  type: 'price' | 'technical' | 'news' | 'volume';
  condition: {
    operator: 'greater_than' | 'less_than' | 'equal_to';
    value: number;
    field: string;
  };
}

export interface PortfolioSummary {
  portfolio: Portfolio;
  holdings: StockHolding[];
  total_invested: number;       // Sum of all total_cost
  current_value: number;        // Sum of all current_value
  total_gain_loss: number;      // current_value - total_invested
  total_gain_loss_percentage: number;
  best_performer?: StockHolding;
  worst_performer?: StockHolding;
  sector_allocation: { sector: string; value: number; percentage: number }[];
}
