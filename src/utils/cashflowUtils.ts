import { format, addMonths, parseISO, differenceInMonths } from 'date-fns';
import type { Loan, CD, CashFlowProjection, FinancialSituation, RiskMetrics, InjectionStrategy, ScenarioResult, AIRecommendation } from '../types/cashflow';
import { calculateAmortizationSchedule, parseDate } from './loanCalculator';

// Helper function to fix loan dates
export const getCorrectLoanDates = (loan: Loan) => {
  try {
    const currentDate = new Date();
    const startDate = parseISO(loan.start_date);
    
    // Calculate how many payments have been made
    const monthsSinceStart = differenceInMonths(currentDate, startDate);
    
    // Calculate next payment date (first unpaid month)
    const nextPaymentDate = addMonths(startDate, Math.max(0, monthsSinceStart));
    
    // Calculate actual end date based on start date + term
    const actualEndDate = addMonths(startDate, loan.term_months);
    
    return {
      nextPaymentDate,
      actualEndDate,
      monthsRemaining: Math.max(0, differenceInMonths(actualEndDate, currentDate))
    };
  } catch (error) {
    console.error('Error calculating loan dates for', loan.name, error);
    return {
      nextPaymentDate: new Date(),
      actualEndDate: new Date(),
      monthsRemaining: 0
    };
  }
};

// Calculate current financial situation
export const calculateFinancialSituation = (
  loans: Loan[] | undefined, 
  cds: CD[] | undefined, 
  customLivingExpenses: number
): FinancialSituation => {
  const totalLoanPayments = loans?.reduce((sum, loan) => sum + loan.monthly_payment, 0) || 0;
  
  // Separate CIB income (available for living) from other CD income (for loans)
  const cibIncome = cds?.filter(cd => cd.institution.toLowerCase().includes('cib'))
    .reduce((sum, cd) => sum + (cd.principal * (cd.interest_rate / 100) / 12), 0) || 0;
  
  const otherCdIncome = cds?.filter(cd => !cd.institution.toLowerCase().includes('cib'))
    .reduce((sum, cd) => sum + (cd.principal * (cd.interest_rate / 100) / 12), 0) || 0;
  
  const totalCdIncome = cibIncome + otherCdIncome;
  const fundingGap = Math.max(0, totalLoanPayments - otherCdIncome);
  const monthlyShortfall = fundingGap; // Only loan shortfall since CIB covers living expenses

  return {
    total_cd_income: totalCdIncome,
    cib_available: cibIncome,
    cd_for_loans: otherCdIncome,
    total_loan_payments: totalLoanPayments,
    funding_gap: fundingGap,
    estimated_living_expenses: customLivingExpenses,
    monthly_shortfall: monthlyShortfall,
    cd_coverage_percentage: (otherCdIncome / totalLoanPayments * 100) || 0
  };
};

// Generate cash flow projection
export const generateCashFlowProjection = (
  loans: Loan[] | undefined,
  cds: CD[] | undefined,
  startingBalance: number,
  customLivingExpenses: number,
  monthsToProject: number,
  isMonthPaid: (loanId: string, month: string) => boolean
): CashFlowProjection[] => {
  if (!loans || !cds) return [];
  
  try {
    const projections: CashFlowProjection[] = [];
    let currentBalance = startingBalance;
    const startDate = new Date();

    // Create loan payment schedule using proper amortization calculation
    const loanSchedule = loans.map(loan => {
      try {
        const startDate = parseDate(loan.next_payment_date);
        const endDate = loan.end_date ? parseISO(loan.end_date) : null;

        if (!startDate) {
          throw new Error('Invalid start date');
        }

        // Calculate the actual amortization schedule
        console.log(`Calculating amortization for loan: ${loan.name}`, {
          principal: loan.principal,
          interest_rate: loan.interest_rate,
          term_months: loan.term_months,
          startDate: startDate,
          endDate: endDate
        });

        const amortizationSchedule = calculateAmortizationSchedule(
          loan.principal,
          loan.interest_rate,
          loan.term_months,
          startDate,
          loan.name,
          false, // Don't use cache to ensure fresh calculation
          endDate || undefined
        );

        console.log(`Amortization schedule for ${loan.name}:`, {
          scheduleLength: amortizationSchedule.length,
          firstPayment: amortizationSchedule[0],
          lastPayment: amortizationSchedule[amortizationSchedule.length - 1]
        });

        // Get the actual end date from the last payment in the schedule
        const actualEndDate = amortizationSchedule.length > 0
          ? amortizationSchedule[amortizationSchedule.length - 1].dueDate
          : (endDate || addMonths(startDate, loan.term_months));

        const remainingMonths = Math.max(0, differenceInMonths(actualEndDate, new Date()));

        return {
          id: loan.id,
          monthly_payment: loan.monthly_payment || 0,
          end_date: actualEndDate,
          next_payment_date: startDate,
          remaining_months: remainingMonths,
          name: loan.name,
          amortization_schedule: amortizationSchedule
        };
      } catch (error) {
        console.error('Error processing loan:', loan.name, error);
        return {
          id: loan.id,
          monthly_payment: 0,
          end_date: new Date(),
          next_payment_date: new Date(),
          remaining_months: 0,
          name: loan.name,
          amortization_schedule: []
        };
      }
    });

    for (let month = 0; month < monthsToProject; month++) {
      try {
        const projectionDate = addMonths(startDate, month);
        
        // Calculate active loans for this month using proper amortization schedule
        const activeLoans = loanSchedule.filter(loan => {
          // Check if loan is still active based on amortization schedule
          if (loan.amortization_schedule && loan.amortization_schedule.length > 0) {
            // Find if there are any payments due on or after the projection date
            const hasRemainingPayments = loan.amortization_schedule.some(payment =>
              payment.dueDate >= projectionDate && payment.closingPrincipal > 0
            );

            // Debug logging for February 2028
            if (format(projectionDate, 'MMM yyyy') === 'Feb 2028') {
              console.log(`Feb 2028 - Loan ${loan.name}:`, {
                hasRemainingPayments,
                scheduleLength: loan.amortization_schedule.length,
                paymentsAfterDate: loan.amortization_schedule.filter(p => p.dueDate >= projectionDate).length,
                paymentsWithBalance: loan.amortization_schedule.filter(p => p.closingPrincipal > 0).length
              });
            }

            return hasRemainingPayments;
          }
          // Fallback to date comparison if no amortization schedule
          const isActive = projectionDate < loan.end_date;

          // Debug logging for February 2028
          if (format(projectionDate, 'MMM yyyy') === 'Feb 2028') {
            console.log(`Feb 2028 - Loan ${loan.name} (fallback):`, {
              isActive,
              projectionDate: format(projectionDate, 'yyyy-MM-dd'),
              endDate: format(loan.end_date, 'yyyy-MM-dd')
            });
          }

          return isActive;
        });

        // Calculate loan payments excluding paid months
        const monthKey = format(projectionDate, 'yyyy-MM');
        const monthlyLoanPayments = activeLoans.reduce((sum, loan) => {
          try {
            const isPaid = isMonthPaid(loan.id, monthKey);
            if (isPaid) {
              return sum;
            }

            // Use amortization schedule to get the exact payment for this month
            if (loan.amortization_schedule && loan.amortization_schedule.length > 0) {
              const paymentForMonth = loan.amortization_schedule.find(payment => {
                const paymentMonth = format(payment.dueDate, 'yyyy-MM');
                return paymentMonth === monthKey;
              });

              if (paymentForMonth && paymentForMonth.closingPrincipal >= 0) {
                return sum + paymentForMonth.installment;
              }
            }

            // Fallback to stored monthly payment
            return sum + (loan.monthly_payment || 0);
          } catch (error) {
            console.error('Error calculating payment for loan:', loan.name, error);
            return sum;
          }
        }, 0);
        
        // Calculate CD income
        const monthlyIncome = cds.reduce((sum, cd) => {
          try {
            const maturityDate = parseISO(cd.maturity_date);
            return projectionDate <= maturityDate 
              ? sum + (cd.principal * (cd.interest_rate / 100) / 12)
              : sum;
          } catch (error) {
            console.error('Error calculating CD income:', cd.institution, error);
            return sum;
          }
        }, 0);

        // Separate CIB and other CD income
        const cibIncome = cds.filter(cd => cd.institution.toLowerCase().includes('cib'))
          .reduce((sum, cd) => {
            try {
              const maturityDate = parseISO(cd.maturity_date);
              return projectionDate <= maturityDate 
                ? sum + (cd.principal * (cd.interest_rate / 100) / 12)
                : sum;
            } catch (error) {
              return sum;
            }
          }, 0);

        const otherCdIncome = monthlyIncome - cibIncome;
        const cdIncomeForLoans = Math.min(otherCdIncome, monthlyLoanPayments);
        const loanShortfall = Math.max(0, monthlyLoanPayments - otherCdIncome);

        // Debug logging for February 2028
        if (format(projectionDate, 'MMM yyyy') === 'Feb 2028') {
          console.log(`Feb 2028 - Cash Flow Calculation:`, {
            activeLoansCount: activeLoans.length,
            monthlyLoanPayments,
            monthlyIncome,
            cibIncome,
            otherCdIncome,
            cdIncomeForLoans,
            loanShortfall,
            activeLoansNames: activeLoans.map(l => l.name)
          });
        }

        // Fixed logic: CIB income covers living expenses, only loan shortfall is additional outflow
        // Living expenses are covered by CIB income, so they shouldn't be added to outflow
        const livingExpensesCovered = Math.min(cibIncome, customLivingExpenses);
        const remainingCibIncome = Math.max(0, cibIncome - livingExpensesCovered);

        // Total outflow is only the loan shortfall (not covered by other CD income)
        const totalOutflow = loanShortfall;

        // Net change: remaining CIB income (after covering living expenses) minus loan shortfall
        const netChange = remainingCibIncome - loanShortfall;
        
        currentBalance += netChange;
        
        projections.push({
          period: month + 1,
          date: format(projectionDate, 'MMM yyyy'),
          starting_balance: currentBalance - netChange,
          monthly_income: monthlyIncome,
          living_expenses: livingExpensesCovered, // Only the amount actually covered by CIB
          loan_payments: monthlyLoanPayments,
          cd_income_for_loans: cdIncomeForLoans,
          loan_shortfall: loanShortfall,
          total_outflow: totalOutflow, // Only loan shortfall, not living expenses
          net_change: netChange,
          ending_balance: currentBalance,
          active_loans: activeLoans.length,
          status: netChange >= 0 ? 'Positive' : 'Negative'
        });
      } catch (error) {
        console.error('Error generating projection for month:', month, error);
      }
    }

    return projections;
  } catch (error) {
    console.error('Error generating cash flow projection:', error);
    return [];
  }
};

// Calculate risk metrics
export const calculateRiskMetrics = (projection: CashFlowProjection[]): RiskMetrics => {
  const monthsToNegative = projection.findIndex(p => p.ending_balance < 0);
  const maxDeficit = Math.min(...projection.map(p => p.ending_balance), 0);
  
  // Calculate cash runway (months until balance goes negative)
  const cashRunway = monthsToNegative === -1 ? projection.length : monthsToNegative;
  
  let level = 'Low';
  let color = 'text-green-600';
  let bgColor = 'bg-green-100';
  let stressLevel = 'Stable';
  
  if (cashRunway <= 6) {
    level = 'Critical';
    color = 'text-red-600';
    bgColor = 'bg-red-100';
    stressLevel = 'High Stress';
  } else if (cashRunway <= 12) {
    level = 'High';
    color = 'text-orange-600';
    bgColor = 'bg-orange-100';
    stressLevel = 'Moderate Stress';
  } else if (cashRunway <= 24) {
    level = 'Medium';
    color = 'text-yellow-600';
    bgColor = 'bg-yellow-100';
    stressLevel = 'Low Stress';
  }
  
  return {
    level,
    color,
    bgColor,
    monthsToNegative: monthsToNegative === -1 ? 0 : monthsToNegative,
    maxDeficit: Math.abs(maxDeficit),
    cashRunway,
    stressLevel
  };
};

// Generate injection strategy
export const generateInjectionStrategy = (
  situation: FinancialSituation,
  riskMetrics: RiskMetrics
): InjectionStrategy | null => {
  if (riskMetrics.cashRunway > 24) {
    return null; // No injection needed
  }
  
  const injectionAmount = Math.max(
    situation.monthly_shortfall * 12, // One year of shortfall
    riskMetrics.maxDeficit * 1.5 // 1.5x max deficit for buffer
  );
  
  let type: InjectionStrategy['type'] = 'strategic';
  let name = 'Strategic Cash Injection';
  let description = 'Recommended cash injection to maintain positive cash flow';
  
  if (riskMetrics.cashRunway <= 6) {
    type = 'emergency';
    name = 'Emergency Cash Injection';
    description = 'Urgent cash injection required to avoid negative balance';
  } else if (riskMetrics.cashRunway <= 12) {
    type = 'staged';
    name = 'Staged Cash Injection';
    description = 'Phased cash injection to improve cash flow stability';
  }
  
  return {
    name,
    description,
    injection_amount: injectionAmount,
    injection_period: 1,
    injection_date: format(new Date(), 'yyyy-MM-dd'),
    type
  };
};

// Generate scenario analysis
export const generateScenarios = (
  situation: FinancialSituation,
  generateProjection: (expenses: number) => CashFlowProjection[]
): ScenarioResult[] => {
  const scenarios: ScenarioResult[] = [];
  
  // Scenario 1: 10% expense reduction
  const reduction10 = situation.estimated_living_expenses * 0.1;
  const newExpenses10 = situation.estimated_living_expenses - reduction10;
  const projection10 = generateProjection(newExpenses10);
  
  scenarios.push({
    name: '10% Expense Reduction',
    monthly_savings: reduction10,
    annual_savings: reduction10 * 12,
    new_monthly_expense: newExpenses10,
    months_to_breakeven: 0,
    achieves_positive_flow: projection10.some(p => p.net_change > 0),
    projection: projection10.slice(0, 24) // First 24 months
  });
  
  // Scenario 2: 20% expense reduction
  const reduction20 = situation.estimated_living_expenses * 0.2;
  const newExpenses20 = situation.estimated_living_expenses - reduction20;
  const projection20 = generateProjection(newExpenses20);
  
  scenarios.push({
    name: '20% Expense Reduction',
    monthly_savings: reduction20,
    annual_savings: reduction20 * 12,
    new_monthly_expense: newExpenses20,
    months_to_breakeven: 0,
    achieves_positive_flow: projection20.some(p => p.net_change > 0),
    projection: projection20.slice(0, 24)
  });
  
  return scenarios;
};

// Generate AI recommendations
export const generateAIRecommendations = (
  situation: FinancialSituation,
  riskMetrics: RiskMetrics
): AIRecommendation[] => {
  const recommendations: AIRecommendation[] = [];
  
  if (riskMetrics.cashRunway <= 6) {
    recommendations.push({
      priority: 'URGENT',
      action: 'Immediate Cash Injection Required',
      details: `Your cash will run out in ${riskMetrics.cashRunway} months. Consider immediate liquidation of investments or emergency funding.`,
      category: 'liquidity'
    });
  }
  
  if (situation.cd_coverage_percentage < 80) {
    recommendations.push({
      priority: 'HIGH',
      action: 'Optimize Debt Structure',
      details: `Your CDs only cover ${situation.cd_coverage_percentage.toFixed(1)}% of loan payments. Consider debt consolidation or refinancing.`,
      category: 'debt'
    });
  }
  
  if (situation.monthly_shortfall > 0) {
    recommendations.push({
      priority: 'STRATEGIC',
      action: 'Reduce Monthly Expenses',
      details: `You have a monthly shortfall of ${situation.monthly_shortfall.toFixed(0)}. Focus on reducing living expenses by 10-20%.`,
      category: 'optimization'
    });
  }
  
  if (riskMetrics.cashRunway > 24) {
    recommendations.push({
      priority: 'OPPORTUNITY',
      action: 'Consider Investment Opportunities',
      details: 'Your cash flow is stable. Consider diversifying into higher-yield investments or real estate.',
      category: 'investment'
    });
  }
  
  return recommendations;
};
