/**
 * Demo Portfolio Setup - Creates sample portfolios with real EGX stocks
 */

import { realPortfolioService } from '../services/realPortfolioService';

export async function createDemoPortfolios() {
  console.log('🎯 Creating demo portfolios with real EGX stocks...');

  try {
    // Create Growth Portfolio
    const growthPortfolio = await realPortfolioService.createPortfolio({
      name: 'EGX Growth Portfolio',
      description: 'Focused on high-growth Egyptian stocks with strong fundamentals',
      initial_balance: 100000,
      currency: 'EGP'
    });

    // Add stocks to Growth Portfolio
    await realPortfolioService.buyStock({
      portfolio_id: growthPortfolio.id,
      symbol: 'COMI',
      shares: 500,
      price: 80.00,
      transaction_type: 'buy',
      fees: 150,
      notes: 'Leading commercial bank in Egypt'
    });

    await realPortfolioService.buyStock({
      portfolio_id: growthPortfolio.id,
      symbol: 'FWRY',
      shares: 2000,
      price: 12.00,
      transaction_type: 'buy',
      fees: 100,
      notes: 'Fintech leader with strong growth potential'
    });

    await realPortfolioService.buyStock({
      portfolio_id: growthPortfolio.id,
      symbol: 'SWDY',
      shares: 300,
      price: 75.00,
      transaction_type: 'buy',
      fees: 120,
      notes: 'Industrial leader with regional expansion'
    });

    // Create Income Portfolio
    const incomePortfolio = await realPortfolioService.createPortfolio({
      name: 'EGX Income Portfolio',
      description: 'Dividend-focused strategy with stable Egyptian companies',
      initial_balance: 75000,
      currency: 'EGP'
    });

    // Add stocks to Income Portfolio
    await realPortfolioService.buyStock({
      portfolio_id: incomePortfolio.id,
      symbol: 'ETEL',
      shares: 1000,
      price: 35.00,
      transaction_type: 'buy',
      fees: 80,
      notes: 'Telecom Egypt - stable dividend payer'
    });

    await realPortfolioService.buyStock({
      portfolio_id: incomePortfolio.id,
      symbol: 'HRHO',
      shares: 800,
      price: 26.00,
      transaction_type: 'buy',
      fees: 70,
      notes: 'EFG Holding - diversified financial services'
    });

    // Create Speculative Portfolio
    const speculativePortfolio = await realPortfolioService.createPortfolio({
      name: 'EGX Speculative Portfolio',
      description: 'Higher risk positions with potential for outsized returns',
      initial_balance: 50000,
      currency: 'EGP'
    });

    // Add stocks to Speculative Portfolio
    await realPortfolioService.buyStock({
      portfolio_id: speculativePortfolio.id,
      symbol: 'VALU',
      shares: 5000,
      price: 9.20,
      transaction_type: 'buy',
      fees: 60,
      notes: 'U Consumer Finance - emerging fintech'
    });

    await realPortfolioService.buyStock({
      portfolio_id: speculativePortfolio.id,
      symbol: 'PHDC',
      shares: 1200,
      price: 8.50,
      transaction_type: 'buy',
      fees: 50,
      notes: 'Palm Hills Development - real estate growth'
    });

    console.log('✅ Demo portfolios created successfully!');
    console.log('📊 Growth Portfolio:', growthPortfolio.name);
    console.log('💰 Income Portfolio:', incomePortfolio.name);
    console.log('🎲 Speculative Portfolio:', speculativePortfolio.name);

    return {
      growth: growthPortfolio,
      income: incomePortfolio,
      speculative: speculativePortfolio
    };

  } catch (error) {
    console.error('❌ Error creating demo portfolios:', error);
    throw error;
  }
}

export async function clearAllPortfolios() {
  console.log('🗑️ Clearing all portfolios...');
  
  try {
    // Clear localStorage
    localStorage.removeItem('real_portfolios');
    localStorage.removeItem('real_portfolio_holdings');
    
    console.log('✅ All portfolios cleared');
  } catch (error) {
    console.error('❌ Error clearing portfolios:', error);
  }
}
