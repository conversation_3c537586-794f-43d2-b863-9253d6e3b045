import { format, addMonths, parseISO, differenceInMonths } from 'date-fns';
import type { Loan, CD, CashFlowProjection } from '../types/cashflow';

export interface FinancialGap {
  startDate: string;
  endDate: string;
  durationMonths: number;
  monthlyShortfall: number;
  totalFundingNeeded: number;
  activeLoan: string;
  expiredCDs: string[];
  activeCDs: string[];
}

export interface StrategicOption {
  id: string;
  title: string;
  description: string;
  type: 'cd_renewal' | 'early_payoff' | 'cash_reserve' | 'income_diversification';
  priority: 'high' | 'medium' | 'low';
  estimatedCost: number;
  estimatedSavings: number;
  implementation: string[];
  pros: string[];
  cons: string[];
  timeframe: string;
}

export interface StrategicAnalysis {
  gaps: FinancialGap[];
  totalRisk: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  strategicOptions: StrategicOption[];
  recommendedAction: string;
}

export function analyzeFinancialGaps(
  loans: Loan[],
  cds: CD[],
  cashFlowProjection: CashFlowProjection[]
): StrategicAnalysis {
  const gaps: FinancialGap[] = [];
  const strategicOptions: StrategicOption[] = [];
  
  // Find periods with loan shortfall
  const shortfallPeriods = cashFlowProjection.filter(p => p.loan_shortfall > 0);
  
  if (shortfallPeriods.length > 0) {
    // Group consecutive shortfall periods
    let currentGap: FinancialGap | null = null;
    
    shortfallPeriods.forEach((period, index) => {
      if (!currentGap) {
        // Start new gap
        currentGap = {
          startDate: period.date,
          endDate: period.date,
          durationMonths: 1,
          monthlyShortfall: period.loan_shortfall,
          totalFundingNeeded: period.loan_shortfall,
          activeLoan: '',
          expiredCDs: [],
          activeCDs: []
        };
      } else if (index === 0 || 
                 Math.abs(period.loan_shortfall - currentGap.monthlyShortfall) < 1000) {
        // Continue current gap
        currentGap.endDate = period.date;
        currentGap.durationMonths++;
        currentGap.totalFundingNeeded += period.loan_shortfall;
      } else {
        // End current gap and start new one
        gaps.push(currentGap);
        currentGap = {
          startDate: period.date,
          endDate: period.date,
          durationMonths: 1,
          monthlyShortfall: period.loan_shortfall,
          totalFundingNeeded: period.loan_shortfall,
          activeLoan: '',
          expiredCDs: [],
          activeCDs: []
        };
      }
    });
    
    if (currentGap) {
      gaps.push(currentGap);
    }
  }

  // Analyze each gap and generate strategic options
  gaps.forEach(gap => {
    const gapStartDate = parseISO(gap.startDate + '-01');
    
    // Find active loans during gap
    const activeLoans = loans.filter(loan => {
      const endDate = parseISO(loan.end_date);
      return endDate > gapStartDate;
    });
    
    if (activeLoans.length > 0) {
      gap.activeLoan = activeLoans[0].name;
    }
    
    // Find expired CDs before gap
    gap.expiredCDs = cds.filter(cd => {
      const maturityDate = parseISO(cd.maturity_date);
      return maturityDate < gapStartDate;
    }).map(cd => cd.institution);
    
    // Find active CDs during gap
    gap.activeCDs = cds.filter(cd => {
      const maturityDate = parseISO(cd.maturity_date);
      return maturityDate >= gapStartDate;
    }).map(cd => cd.institution);
    
    // Generate strategic options for this gap
    generateStrategicOptions(gap, loans, cds, strategicOptions);
  });

  // Calculate overall risk
  const totalRisk = gaps.reduce((sum, gap) => sum + gap.totalFundingNeeded, 0);
  let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
  
  if (totalRisk > 1000000) riskLevel = 'critical';
  else if (totalRisk > 500000) riskLevel = 'high';
  else if (totalRisk > 100000) riskLevel = 'medium';

  // Generate recommended action
  const recommendedAction = generateRecommendedAction(gaps, strategicOptions);

  return {
    gaps,
    totalRisk,
    riskLevel,
    strategicOptions,
    recommendedAction
  };
}

function generateStrategicOptions(
  gap: FinancialGap,
  loans: Loan[],
  cds: CD[],
  options: StrategicOption[]
): void {
  // Option 1: CD Renewal Strategy
  const expiredCDValue = cds
    .filter(cd => gap.expiredCDs.includes(cd.institution))
    .reduce((sum, cd) => sum + cd.principal, 0);
    
  if (expiredCDValue > 0) {
    options.push({
      id: `cd_renewal_${gap.startDate}`,
      title: 'CD Renewal Strategy',
      description: `Renew or replace expiring CDs to maintain income stream through ${gap.endDate}`,
      type: 'cd_renewal',
      priority: 'high',
      estimatedCost: 0,
      estimatedSavings: gap.totalFundingNeeded,
      implementation: [
        'Review CD renewal options 6 months before maturity',
        'Compare interest rates across institutions',
        'Consider staggered maturity dates for flexibility',
        `Target renewal amount: ${(expiredCDValue / 1000000).toFixed(1)}M EGP`
      ],
      pros: [
        'Maintains steady income stream',
        'No additional capital required',
        'Preserves principal amount'
      ],
      cons: [
        'Subject to interest rate changes',
        'Funds locked for CD term',
        'May require shopping for better rates'
      ],
      timeframe: '6 months before CD maturity'
    });
  }

  // Option 2: Early Loan Payoff
  const activeLoan = loans.find(loan => loan.name === gap.activeLoan);
  if (activeLoan) {
    const remainingBalance = activeLoan.remaining_balance || activeLoan.principal;
    
    options.push({
      id: `early_payoff_${gap.startDate}`,
      title: 'Early Loan Payoff',
      description: `Use CD principal to pay off ${activeLoan.name} early and eliminate monthly payments`,
      type: 'early_payoff',
      priority: 'medium',
      estimatedCost: remainingBalance,
      estimatedSavings: gap.totalFundingNeeded,
      implementation: [
        'Calculate exact remaining loan balance',
        'Review prepayment penalties',
        'Use maturing CD principal for payoff',
        'Redirect monthly payment amount to savings'
      ],
      pros: [
        'Eliminates monthly loan payment',
        'Reduces total interest paid',
        'Improves cash flow permanently'
      ],
      cons: [
        'Requires large upfront payment',
        'Reduces liquid CD principal',
        'May have prepayment penalties'
      ],
      timeframe: 'When CDs mature'
    });
  }

  // Option 3: Cash Reserve Strategy
  options.push({
    id: `cash_reserve_${gap.startDate}`,
    title: 'Strategic Cash Reserve',
    description: `Build cash reserve to cover ${gap.durationMonths}-month funding gap`,
    type: 'cash_reserve',
    priority: 'medium',
    estimatedCost: gap.totalFundingNeeded,
    estimatedSavings: 0,
    implementation: [
      `Set aside ${(gap.totalFundingNeeded / 1000).toFixed(0)}K EGP from CD maturities`,
      'Place in high-yield savings account',
      'Create automatic monthly transfers',
      'Monitor and adjust as needed'
    ],
    pros: [
      'Provides guaranteed funding',
      'Maintains flexibility',
      'No debt restructuring needed'
    ],
    cons: [
      'Opportunity cost of lower returns',
      'Requires discipline to maintain',
      'Inflation risk over time'
    ],
    timeframe: 'Before gap period begins'
  });
}

function generateRecommendedAction(gaps: FinancialGap[], options: StrategicOption[]): string {
  if (gaps.length === 0) {
    return 'No significant funding gaps identified. Continue monitoring CD maturity dates and loan schedules.';
  }

  const primaryGap = gaps[0];
  const highPriorityOptions = options.filter(opt => opt.priority === 'high');
  
  if (highPriorityOptions.length > 0) {
    return `Primary recommendation: ${highPriorityOptions[0].title}. ${primaryGap.durationMonths}-month gap starting ${primaryGap.startDate} requires ${(primaryGap.totalFundingNeeded / 1000).toFixed(0)}K EGP funding. Focus on CD renewal strategy to maintain income continuity.`;
  }

  return `Address ${primaryGap.durationMonths}-month funding gap starting ${primaryGap.startDate}. Consider multiple strategies including CD renewal and cash reserves.`;
}
