/**
 * Loan calculation utility functions
 * Contains pure functions for loan calculations to improve maintainability and testing
 */


/**
 * Get the proper date string to use for a loan
 * This will return the original date as provided
 * @param loanName Loan name
 * @param originalDate Original date to use
 * @returns The date string to use
 */
export function getLoanDate(loanName: string, originalDate: string): string {
  // Always use the original date provided by the user
  console.log(`Using original date for ${loanName}: ${originalDate}`);
  return originalDate;
}

/**
 * Calculate the monthly payment for a loan
 */
export function calculateMonthlyPayment(principal: number, annualRate: number, termMonths: number): number {
  if (!principal || !annualRate || !termMonths || termMonths <= 0) {
    return 0;
  }

  try {
    const monthlyRate = (annualRate / 100) / 12;

    // Calculate monthly payment using the standard amortization formula
    const payment = (principal * monthlyRate * Math.pow(1 + monthlyRate, termMonths)) /
                   (Math.pow(1 + monthlyRate, termMonths) - 1);

    // Round to 2 decimal places
    return Number(payment.toFixed(2));
  } catch (error) {
    console.error('Error calculating monthly payment:', error);
    return 0;
  }
}

/**
 * Interface for amortization table row
 */
export interface AmortizationRow {
  dueDate: Date;
  installment: number;
  interestComponent: number;
  principalComponent: number;
  closingPrincipal: number;
}

// Cache for amortization schedules to avoid recalculation
const scheduleCache: Record<string, AmortizationRow[]> = {};

/**
 * Calculate amortization schedule for a loan
 */
export function calculateAmortizationSchedule(
  principal: number,
  annualRate: number,
  termMonths: number,
  startDate: Date,
  loanName?: string,
  useCache: boolean = true,
  endDate?: Date
): AmortizationRow[] {
  // Validate inputs
  if (!principal || !annualRate || !termMonths || !startDate || isNaN(startDate.getTime())) {
    return [];
  }

  // Create cache key for memoization
  const endDateStr = endDate ? endDate.getTime().toString() : '';
  const cacheKey = `${principal}-${annualRate}-${termMonths}-${startDate.getTime()}-${loanName}-${endDateStr}`;

  // Return cached result if available and caching is enabled
  if (useCache && scheduleCache[cacheKey]) {
    return [...scheduleCache[cacheKey]];
  }

  // If endDate is provided, recalculate termMonths
  if (endDate && !isNaN(endDate.getTime())) {
    // Calculate months between startDate and endDate
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth();
    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth();

    // Calculate the difference in months
    const calculatedTermMonths = (endYear - startYear) * 12 + (endMonth - startMonth);

    // Use calculated term if it's valid
    if (calculatedTermMonths > 0) {
      termMonths = calculatedTermMonths;
    }
  }

  try {
    const monthlyRate = (annualRate / 100) / 12;
    const monthlyPayment = calculateMonthlyPayment(principal, annualRate, termMonths);

    if (monthlyPayment === 0) {
      return [];
    }

    const schedule: AmortizationRow[] = [];
    let remainingPrincipal = principal;

    // Calculate each payment row
    for (let month = 0; month < termMonths; month++) {
      const interestPayment = remainingPrincipal * monthlyRate;
      const principalPayment = monthlyPayment - interestPayment;

      // Ensure we don't go negative on the last payment due to rounding
      const newRemainingPrincipal = month === termMonths - 1 ?
        0 : Math.max(0, remainingPrincipal - principalPayment);

      const dueDate = new Date(new Date(startDate).setMonth(startDate.getMonth() + month));
      const overrideDate = getLoanDate(loanName || "", dueDate.toISOString().split('T')[0]);
      const overrideDueDate = parseDate(overrideDate);

      // Ensure we have a valid date object - use original dueDate as fallback
      const finalDueDate = overrideDueDate || dueDate;

      schedule.push({
        dueDate: finalDueDate,
        installment: Number(monthlyPayment.toFixed(2)),
        interestComponent: Number(interestPayment.toFixed(2)),
        principalComponent: Number(principalPayment.toFixed(2)),
        closingPrincipal: Number(newRemainingPrincipal.toFixed(2))
      });

      remainingPrincipal = newRemainingPrincipal;
    }

    // Cache the result if caching is enabled
    if (useCache) {
      scheduleCache[cacheKey] = [...schedule];
    }

    return schedule;
  } catch (error) {
    console.error('Error calculating amortization schedule:', error);
    return [];
  }
}

/**
 * Clear a specific schedule from cache based on loan parameters, or all schedules if no parameters are provided
 */
export function clearScheduleCache(
  principal?: number,
  annualRate?: number,
  termMonths?: number,
  startDate?: Date,
  loanName?: string,
  endDate?: Date
): void {
  if (principal !== undefined && annualRate !== undefined && termMonths !== undefined && startDate && loanName !== undefined) {
    const endDateStr = endDate ? endDate.getTime().toString() : '';
    const cacheKey = `${principal}-${annualRate}-${termMonths}-${startDate.getTime()}-${loanName}-${endDateStr}`;
    if (scheduleCache[cacheKey]) {
      delete scheduleCache[cacheKey];
    }
  } else {
    // Clear all schedules if no specific parameters are provided
    Object.keys(scheduleCache).forEach(key => delete scheduleCache[key]);
  }
}

/**
 * Calculate the remaining balance for a loan
 */
export function calculateRemainingBalance(
  principal: number,
  annualRate: number,
  termMonths: number,
  startDate: Date,
  loanName: string = ''
): number {
  try {
    const schedule = calculateAmortizationSchedule(principal, annualRate, termMonths, startDate, loanName, false);
    if (!schedule.length) return principal;

    const today = new Date();

    // Find the most recent payment that's due before today
    for (let i = schedule.length - 1; i >= 0; i--) {
      const payment = schedule[i];
      if (payment.dueDate && payment.dueDate <= today) {
        return payment.closingPrincipal;
      }
    }

    // If no payments are due yet, return the full principal
    return principal;
  } catch (error) {
    console.error('Error calculating remaining balance:', error);
    return principal;
  }
}

/**
 * Format a date string in yyyy-MM-dd format
 */
export function formatDateString(date: Date): string {
  try {
    return date.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error formatting date:', error);
    return new Date().toISOString().split('T')[0];
  }
}

/**
 * Parse a date string into a Date object
 */
export function parseDate(dateString: string): Date | null {
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    console.error('Error parsing date:', error);
    return null;
  }
}

/**
 * Add months to a date and return a new date
 */
export function addMonthsToDate(date: Date, months: number): Date {
  try {
    const result = new Date(date);
    result.setMonth(result.getMonth() + months);
    return result;
  } catch (error) {
    console.error('Error adding months to date:', error);
    return new Date();
  }
}

/**
 * Calculate number of months between two dates
 */
export function calculateMonthsBetween(startDate: Date, endDate: Date): number {
  try {
    return (
      (endDate.getFullYear() - startDate.getFullYear()) * 12 +
      endDate.getMonth() - startDate.getMonth()
    );
  } catch (error) {
    console.error('Error calculating months between dates:', error);
    return 0;
  }
}
