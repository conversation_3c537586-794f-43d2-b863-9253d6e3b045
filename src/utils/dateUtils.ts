/**
 * Date utility functions for consistent date handling
 */

/**
 * Parse a date string into a Date object
 * @param dateString - String date to parse
 * @returns Date object or null if invalid
 */
export function parseDate(dateString: string): Date | null {
  try {
    if (!dateString) return null;
    
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    console.error('Error parsing date:', error);
    return null;
  }
}

/**
 * Format a date to YYYY-MM-DD
 * @param date - Date to format
 * @returns Formatted date string
 */
export function formatDateString(date: Date): string {
  try {
    return date.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}

/**
 * Add months to a date
 * @param date - Base date
 * @param months - Number of months to add
 * @returns New date with added months
 */
export function addMonthsToDate(date: Date, months: number): Date {
  try {
    const result = new Date(date);
    result.setMonth(result.getMonth() + months);
    return result;
  } catch (error) {
    console.error('Error adding months to date:', error);
    return new Date();
  }
}

/**
 * Calculate months between two dates
 * @param startDate - Start date
 * @param endDate - End date
 * @returns Number of months between dates
 */
export function calculateMonthsBetween(startDate: Date, endDate: Date): number {
  try {
    return (
      (endDate.getFullYear() - startDate.getFullYear()) * 12 +
      endDate.getMonth() - startDate.getMonth()
    );
  } catch (error) {
    console.error('Error calculating months between dates:', error);
    return 0;
  }
}

/**
 * Check if a date is valid
 * @param dateString - Date string to check
 * @returns True if valid date
 */
export function isValidDateString(dateString: string): boolean {
  try {
    if (!dateString) return false;
    
    // For YYYY-MM-DD format validation
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      const date = new Date(dateString);
      return !isNaN(date.getTime());
    }
    
    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Convert any valid date input to YYYY-MM-DD format
 * @param input - Date input (string or Date)
 * @returns Formatted date string or empty string if invalid
 */
export function normalizeDate(input: string | Date | null | undefined): string {
  if (!input) return '';
  
  try {
    if (typeof input === 'string') {
      // If already in YYYY-MM-DD format, return as is
      if (/^\d{4}-\d{2}-\d{2}$/.test(input)) {
        return input;
      }
      
      const date = new Date(input);
      if (isNaN(date.getTime())) return '';
      return formatDateString(date);
    }
    
    if (input instanceof Date) {
      return formatDateString(input);
    }
    
    return '';
  } catch (error) {
    console.error('Error normalizing date:', error);
    return '';
  }
}
