/**
 * Calendar Utilities
 * Helper functions for calendar operations and date handling
 */

import { format, parseISO, addMonths, addDays, addWeeks, addYears, isAfter } from 'date-fns';
import { CalendarEvent, Loan, CD, FinancialGoal, Expense, CustomEvent, RecurrencePattern, EventTimeHorizon } from '../types/calendar';

/**
 * Calculate loan payment breakdown
 */
export function calculateLoanPayment(loan: Loan) {
  const remainingBalance = loan.remaining_balance;
  const monthlyRate = loan.interest_rate / 100 / 12;
  const interestPayment = remainingBalance * monthlyRate;
  const principalPayment = loan.monthly_payment - interestPayment;
  const newBalance = Math.max(0, remainingBalance - principalPayment);
  const percentPaid = ((loan.principal - newBalance) / loan.principal) * 100;

  return {
    principalPayment,
    interestPayment,
    remainingBalance: newBalance,
    percentPaid
  };
}

/**
 * Generate recurring event dates
 */
export function generateRecurringDates(
  startDate: string,
  pattern: RecurrencePattern,
  endDate?: string,
  count?: number
): string[] {
  const dates: string[] = [];
  let currentDate = parseISO(startDate);
  const endDateTime = endDate ? parseISO(endDate) : null;
  let iteration = 0;

  while (true) {
    // Check if we've reached the count limit
    if (count && iteration >= count) break;
    
    // Check if we've passed the end date
    if (endDateTime && isAfter(currentDate, endDateTime)) break;
    
    // Add current date to the list
    dates.push(format(currentDate, 'yyyy-MM-dd'));
    
    // Move to next occurrence
    switch (pattern) {
      case 'daily':
        currentDate = addDays(currentDate, 1);
        break;
      case 'weekly':
        currentDate = addWeeks(currentDate, 1);
        break;
      case 'monthly':
        currentDate = addMonths(currentDate, 1);
        break;
      case 'yearly':
        currentDate = addYears(currentDate, 1);
        break;
    }
    
    iteration++;
    
    // Safety check to prevent infinite loops
    if (iteration > 1000) break;
  }

  return dates;
}

/**
 * Generate loan payment events
 */
export function generateLoanEvents(loans: Loan[], timeHorizon: EventTimeHorizon): CalendarEvent[] {
  const events: CalendarEvent[] = [];
  const now = new Date();
  let endDate: Date;

  // Determine end date based on time horizon
  switch (timeHorizon) {
    case 'week':
      endDate = addDays(now, 7);
      break;
    case 'month':
      endDate = addMonths(now, 1);
      break;
    case 'quarter':
      endDate = addMonths(now, 3);
      break;
    case 'year':
      endDate = addYears(now, 1);
      break;
    case 'all':
      endDate = addYears(now, 10); // 10 years should cover all loans
      break;
  }

  loans.forEach(loan => {
    if (!loan.next_payment_date) return;

    let currentDate = parseISO(loan.next_payment_date);
    let remainingBalance = loan.remaining_balance;
    let paymentNumber = 1;

    while (currentDate <= endDate && remainingBalance > 0.01) {
      const payment = calculateLoanPayment({
        ...loan,
        remaining_balance: remainingBalance
      });

      events.push({
        id: `loan-${loan.id}-${paymentNumber}`,
        title: `${loan.name} Payment`,
        start: format(currentDate, 'yyyy-MM-dd'),
        backgroundColor: '#dc2626',
        borderColor: '#dc2626',
        textColor: '#ffffff',
        allDay: true,
        className: 'loan-payment',
        extendedProps: {
          type: 'loan',
          amount: loan.monthly_payment,
          description: `Payment #${paymentNumber} for ${loan.name}`,
          icon: 'credit-card',
          principalPayment: payment.principalPayment,
          interestPayment: payment.interestPayment,
          remainingBalance: payment.remainingBalance,
          percentPaid: payment.percentPaid
        }
      });

      remainingBalance = payment.remainingBalance;
      currentDate = addMonths(currentDate, 1);
      paymentNumber++;
    }
  });

  return events;
}

/**
 * Generate CD maturity events
 */
export function generateCDEvents(cds: CD[]): CalendarEvent[] {
  return cds.map(cd => ({
    id: `cd-${cd.id}`,
    title: `${cd.institution} CD Matures`,
    start: cd.maturity_date,
    backgroundColor: '#059669',
    borderColor: '#059669',
    textColor: '#ffffff',
    allDay: true,
    className: 'cd-maturity',
    extendedProps: {
      type: 'cd-maturity',
      amount: cd.principal,
      description: `CD at ${cd.institution} matures`,
      icon: 'piggy-bank'
    }
  }));
}

/**
 * Generate goal milestone events
 */
export function generateGoalEvents(goals: FinancialGoal[]): CalendarEvent[] {
  return goals.map(goal => ({
    id: `goal-${goal.id}`,
    title: `${goal.name} Target`,
    start: goal.target_date,
    backgroundColor: '#7c3aed',
    borderColor: '#7c3aed',
    textColor: '#ffffff',
    allDay: true,
    className: 'goal-target',
    extendedProps: {
      type: 'goal',
      amount: goal.target_amount,
      description: goal.notes || `Target date for ${goal.name}`,
      icon: 'target'
    }
  }));
}

/**
 * Generate expense events
 */
export function generateExpenseEvents(expenses: Expense[]): CalendarEvent[] {
  return expenses.map(expense => ({
    id: `expense-${expense.id}`,
    title: `${expense.category}: ${expense.description}`,
    start: expense.date,
    backgroundColor: '#ea580c',
    borderColor: '#ea580c',
    textColor: '#ffffff',
    allDay: true,
    className: 'expense',
    extendedProps: {
      type: 'expense',
      amount: expense.amount,
      description: expense.description,
      icon: 'banknote'
    }
  }));
}

/**
 * Generate custom events with recurrence
 */
export function generateCustomEvents(customEvents: CustomEvent[]): CalendarEvent[] {
  const events: CalendarEvent[] = [];

  customEvents.forEach(customEvent => {
    if (customEvent.isRecurring && customEvent.recurrencePattern) {
      // Generate recurring events
      const dates = generateRecurringDates(
        customEvent.start_date,
        customEvent.recurrencePattern,
        customEvent.recurrenceEndDate,
        customEvent.recurrenceCount
      );

      dates.forEach((date, index) => {
        events.push({
          id: `${customEvent.id}-${index}`,
          title: customEvent.title,
          start: date,
          end: customEvent.end_date ? date : undefined,
          backgroundColor: customEvent.backgroundColor || '#3b82f6',
          borderColor: customEvent.borderColor || '#3b82f6',
          textColor: customEvent.textColor || '#ffffff',
          allDay: true,
          className: 'custom-event',
          extendedProps: {
            type: 'custom',
            amount: customEvent.amount,
            description: customEvent.description || '',
            icon: 'calendar',
            recurrence: true
          }
        });
      });
    } else {
      // Single event
      events.push({
        id: customEvent.id,
        title: customEvent.title,
        start: customEvent.start_date,
        end: customEvent.end_date,
        backgroundColor: customEvent.backgroundColor || '#3b82f6',
        borderColor: customEvent.borderColor || '#3b82f6',
        textColor: customEvent.textColor || '#ffffff',
        allDay: true,
        className: 'custom-event',
        extendedProps: {
          type: 'custom',
          amount: customEvent.amount,
          description: customEvent.description || '',
          icon: 'calendar'
        }
      });
    }
  });

  return events;
}

/**
 * Filter events based on search query and filters
 */
export function filterEvents(
  events: CalendarEvent[],
  searchQuery: string,
  typeFilter: string,
  categoryFilter: string
): CalendarEvent[] {
  return events.filter(event => {
    // Search query filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesTitle = event.title.toLowerCase().includes(query);
      const matchesDescription = event.extendedProps.description?.toLowerCase().includes(query);
      if (!matchesTitle && !matchesDescription) return false;
    }

    // Type filter
    if (typeFilter !== 'all' && event.extendedProps.type !== typeFilter) {
      return false;
    }

    // Category filter (for custom events)
    if (categoryFilter !== 'all') {
      // This would need to be extended based on how categories are stored
      // For now, we'll skip this filter for non-custom events
      if (event.extendedProps.type === 'custom') {
        // Would need to check event category here
        // return event.category === categoryFilter;
      }
    }

    return true;
  });
}

/**
 * Sort events by date
 */
export function sortEventsByDate(events: CalendarEvent[]): CalendarEvent[] {
  return events.sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime());
}

/**
 * Paginate events
 */
export function paginateEvents(events: CalendarEvent[], page: number, perPage: number): CalendarEvent[] {
  const startIndex = (page - 1) * perPage;
  const endIndex = startIndex + perPage;
  return events.slice(startIndex, endIndex);
}

/**
 * Get event color based on type
 */
export function getEventColor(type: string): { backgroundColor: string; borderColor: string; textColor: string } {
  const colors = {
    loan: { backgroundColor: '#dc2626', borderColor: '#dc2626', textColor: '#ffffff' },
    goal: { backgroundColor: '#7c3aed', borderColor: '#7c3aed', textColor: '#ffffff' },
    expense: { backgroundColor: '#ea580c', borderColor: '#ea580c', textColor: '#ffffff' },
    'cd-maturity': { backgroundColor: '#059669', borderColor: '#059669', textColor: '#ffffff' },
    custom: { backgroundColor: '#3b82f6', borderColor: '#3b82f6', textColor: '#ffffff' }
  };

  return colors[type as keyof typeof colors] || colors.custom;
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
}

/**
 * Format date for display
 */
export function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return format(dateObj, 'MMM dd, yyyy');
}

/**
 * Check if date is in the past
 */
export function isPastDate(date: string): boolean {
  return parseISO(date) < new Date();
}

/**
 * Get days until date
 */
export function getDaysUntil(date: string): number {
  const targetDate = parseISO(date);
  const now = new Date();
  return Math.ceil((targetDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
}
