/**
 * Storage Bridge - Provides a bridge between the browser's localStorage API and PyWebView's API
 * This allows the application to work with both local storage in development and PyWebView in production
 */

// Check if we're running in PyWebView environment
const isRunningInPyWebView = typeof window !== 'undefined' && window.pywebview;
const useLocalStore = typeof window !== 'undefined' && (window.USE_LOCAL_STORE === true || process.env.VITE_USE_LOCAL_STORE === 'true');

// Debug logging
console.log('Storage Bridge initialized');
console.log('isRunningInPyWebView:', isRunningInPyWebView);
console.log('useLocalStore:', useLocalStore);
console.log('window.pywebview:', typeof window !== 'undefined' ? window.pywebview : 'undefined');
console.log('window.pywebview_channel:', typeof window !== 'undefined' ? window.pywebview_channel : 'undefined');

// Helper function to safely parse JSON
function safeParseJSON(jsonString) {
    if (!jsonString) return null;
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        console.error('Error parsing JSON:', error);
        console.error('JSON string:', jsonString);
        return null;
    }
}

// Helper function to safely call PyWebView API methods
async function safeApiCall(methodName, ...args) {
    return new Promise((resolve, reject) => {
        try {
            if (window.pywebview && window.pywebview.api && typeof window.pywebview.api[methodName] === 'function') {
                const result = window.pywebview.api[methodName](...args);
                resolve(result);
            } else {
                console.error(`API method ${methodName} not available`);
                reject(new Error(`API method ${methodName} not available`));
            }
        } catch (error) {
            console.error(`Error calling ${methodName}:`, error);
            reject(error);
        }
    });
}

// Create a storage bridge that mimics localStorage API
const storageBridge = {
    // Get an item from storage
    async getItem(key) {
        console.log(`[StorageBridge] getItem: ${key}`);

        try {
            if (isRunningInPyWebView && useLocalStore) {
                // Use PyWebView API
                const result = await safeApiCall('get_item', key);
                console.log(`[StorageBridge] getItem result for ${key}:`, result ? result.substring(0, 50) + '...' : 'null');
                return result;
            } else {
                // Use browser localStorage
                return localStorage.getItem(key);
            }
        } catch (error) {
            console.error(`[StorageBridge] Error in getItem(${key}):`, error);
            return null;
        }
    },

    // Set an item in storage
    async setItem(key, value) {
        console.log(`[StorageBridge] setItem: ${key}`);

        try {
            if (isRunningInPyWebView && useLocalStore) {
                // Use PyWebView API
                const result = await safeApiCall('set_item', key, value);
                console.log(`[StorageBridge] setItem result for ${key}:`, result);
                return result;
            } else {
                // Use browser localStorage
                localStorage.setItem(key, value);
                return true;
            }
        } catch (error) {
            console.error(`[StorageBridge] Error in setItem(${key}):`, error);
            return false;
        }
    },

    // Remove an item from storage
    async removeItem(key) {
        console.log(`[StorageBridge] removeItem: ${key}`);

        try {
            if (isRunningInPyWebView && useLocalStore) {
                // Use PyWebView API
                const result = await safeApiCall('remove_item', key);
                console.log(`[StorageBridge] removeItem result for ${key}:`, result);
                return result;
            } else {
                // Use browser localStorage
                localStorage.removeItem(key);
                return true;
            }
        } catch (error) {
            console.error(`[StorageBridge] Error in removeItem(${key}):`, error);
            return false;
        }
    },

    // Get all keys in storage
    async getAllKeys() {
        console.log('[StorageBridge] getAllKeys');

        try {
            if (isRunningInPyWebView && useLocalStore) {
                // Use PyWebView API
                const result = await safeApiCall('get_all_keys');
                const keys = safeParseJSON(result) || [];
                console.log('[StorageBridge] getAllKeys result:', keys);
                return keys;
            } else {
                // Use browser localStorage
                return Object.keys(localStorage);
            }
        } catch (error) {
            console.error('[StorageBridge] Error in getAllKeys:', error);
            return [];
        }
    },

    // Get a collection of items
    async getCollection(collection) {
        console.log(`[StorageBridge] getCollection: ${collection}`);

        try {
            if (isRunningInPyWebView && useLocalStore) {
                // Use PyWebView API
                const result = await safeApiCall('get_collection', collection);
                const items = safeParseJSON(result) || [];
                console.log(`[StorageBridge] getCollection result for ${collection}:`, items.length);
                return items;
            } else {
                // Use browser localStorage
                // This is a simplified implementation for browser localStorage
                const keys = Object.keys(localStorage);
                const collectionKeys = keys.filter(key => key.startsWith(`${collection}/`));

                return collectionKeys.map(key => {
                    const value = localStorage.getItem(key);
                    return safeParseJSON(value);
                }).filter(Boolean);
            }
        } catch (error) {
            console.error(`[StorageBridge] Error in getCollection(${collection}):`, error);
            return [];
        }
    },

    // Get storage statistics
    async getStorageStats() {
        console.log('[StorageBridge] getStorageStats');

        try {
            if (isRunningInPyWebView && useLocalStore) {
                // Use PyWebView API
                const result = await safeApiCall('get_storage_stats');
                const stats = safeParseJSON(result) || { total_items: 0, collections: {} };
                console.log('[StorageBridge] getStorageStats result:', stats);
                return stats;
            } else {
                // Use browser localStorage
                return {
                    total_items: Object.keys(localStorage).length,
                    collections: {}
                };
            }
        } catch (error) {
            console.error('[StorageBridge] Error in getStorageStats:', error);
            return { total_items: 0, collections: {} };
        }
    }
};

// Export the storage bridge
export default storageBridge;
