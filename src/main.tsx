import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';
import './utils/disable-date-normalization.js';
import './utils/disableDateNormalization.js'; // Import our comprehensive fix

// Set a global flag for using local data store
// This will be set to true in our desktop version
declare global {
  interface Window {
    USE_LOCAL_STORE: boolean;
  }
}

// Check if we should use the local data store
// This will be set by the environment variable in our Python launcher
window.USE_LOCAL_STORE = import.meta.env.VITE_USE_LOCAL_STORE === 'true';

console.log('App starting...');
console.log('Using local data store:', window.USE_LOCAL_STORE ? 'Yes' : 'No');

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>
);
