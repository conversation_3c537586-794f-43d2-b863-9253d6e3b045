/**
 * Local Data Store
 *
 * A simple, direct data storage system for the Financial Advisor desktop app.
 * This replaces the more complex Supabase mock implementation with a streamlined
 * approach that directly saves data to local storage files.
 */

// Define basic user interface
export interface User {
  id: string;
  email: string;
  created_at: string;
}

// Define auth state change event type
export type AuthChangeEvent = 'SIGNED_IN' | 'SIGNED_OUT' | 'SIGNED_UP';

// Define listener type
export type AuthChangeListener = (event: AuthChangeEvent, user: User | null) => void;

// Extend the Window interface to include our global flags and debug function
declare global {
  interface Window {
    // Debug function for storage diagnostics
    dumpStorage?: () => Promise<string | undefined>;
    debugStorage?: () => Promise<any>;
  }
}

/**
 * Check if we're running in the desktop app with PyWebView
 */
function isPyWebViewAvailable(): boolean {
  try {
    return typeof window !== 'undefined' &&
      typeof (window as any).pywebview !== 'undefined' &&
      typeof (window as any).pywebview.api !== 'undefined';
  } catch (error) {
    console.error('Error checking PyWebView availability:', error);
    return false;
  }
}

/**
 * Wait for PyWebView to be ready
 */
function waitForPyWebView(): Promise<boolean> {
  return new Promise<boolean>((resolve) => {
    if (isPyWebViewAvailable()) {
      console.log('PyWebView is already available');
      resolve(true);
      return;
    }

    console.log('Waiting for PyWebView API to become available...');

    // Setup event listener for when PyWebView API is ready
    window.addEventListener('pywebview-api-ready', () => {
      console.log('PyWebView API ready event fired');
      resolve(true);
    });

    // Set a timeout in case the event never fires
    setTimeout(() => {
      if (!isPyWebViewAvailable()) {
        console.log('PyWebView timed out, falling back to localStorage');
        resolve(false);
      } else {
        console.log('PyWebView is available after timeout check');
        resolve(true);
      }
    }, 2000);
  });
}

/**
 * Storage System
 * Handles saving and loading data in both browser and desktop environments
 */
class StorageSystem {
  private initialized: boolean = false;
  private initPromise: Promise<boolean> | null = null;

  /**
   * Initialize the storage system
   */
  async initialize(): Promise<boolean> {
    if (this.initialized) return true;
    if (this.initPromise) return this.initPromise;

    console.log('Initializing storage system...');

    this.initPromise = new Promise<boolean>(async (resolve) => {
      try {
        // Wait for PyWebView to be ready if in desktop mode
        await waitForPyWebView();

        // Log whether we're using PyWebView or localStorage
        if (isPyWebViewAvailable()) {
          console.log('Storage system initialized with PyWebView');
        } else {
          console.log('Storage system initialized with localStorage');
        }

        this.initialized = true;
        resolve(true);
      } catch (error) {
        console.error('Error initializing storage system:', error);
        // Fall back to localStorage if initialization fails
        this.initialized = true;
        resolve(false);
      }
    });

    return this.initPromise;
  }

  /**
   * Save data to storage
   */
  async saveData(key: string, data: any): Promise<boolean> {
    await this.initialize();

    try {
      const jsonData = JSON.stringify(data);
      console.log(`Saving data: ${key} (${jsonData.length} bytes)`, data);

      if (isPyWebViewAvailable()) {
        // Use PyWebView API in desktop mode
        console.log('Using PyWebView for storage');
        try {
          // Access the PyWebView API safely
          const api = (window as any).pywebview.api;
          if (typeof api.set_item !== 'function') {
            throw new Error('PyWebView API set_item method not found');
          }

          const result = await api.set_item(key, jsonData);
          console.log(`Save result for ${key}:`, result);
          return result;
        } catch (pywebviewError) {
          console.error('PyWebView API error when saving:', pywebviewError);
          // Fallback to localStorage if PyWebView fails
          console.log('Falling back to localStorage');
          localStorage.setItem(key, jsonData);
          return true;
        }
      } else {
        // Use localStorage in browser mode
        console.log('Using localStorage for storage');
        localStorage.setItem(key, jsonData);
        return true;
      }
    } catch (error) {
      console.error('Error saving data:', error);

      // More detailed error logging
      if (error instanceof Error) {
        console.error('Error name:', error.name);
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }

      return false;
    }
  }

  /**
   * Load data from storage
   */
  async loadData<T>(key: string): Promise<T | null> {
    await this.initialize();

    try {
      console.log(`Loading data: ${key}`);

      let jsonData: string | null = null;

      if (isPyWebViewAvailable()) {
        // Use PyWebView API in desktop mode
        const api = (window as any).pywebview.api;
        if (typeof api.get_item !== 'function') {
          throw new Error('PyWebView API get_item method not found');
        }

        jsonData = await api.get_item(key);
      } else {
        // Use localStorage in browser mode
        jsonData = localStorage.getItem(key);
      }

      if (!jsonData) {
        console.log(`No data found for key: ${key}`);
        return null;
      }

      console.log(`Data loaded: ${key} (${jsonData.length} bytes)`);
      return JSON.parse(jsonData) as T;
    } catch (error) {
      console.error('Error loading data:', error);
      return null;
    }
  }

  /**
   * Remove data from storage
   */
  async removeData(key: string): Promise<boolean> {
    await this.initialize();

    try {
      console.log(`Removing data: ${key}`);

      if (isPyWebViewAvailable()) {
        // Use PyWebView API in desktop mode
        const api = (window as any).pywebview.api;
        if (typeof api.remove_item !== 'function') {
          throw new Error('PyWebView API remove_item method not found');
        }

        return await api.remove_item(key);
      } else {
        // Use localStorage in browser mode
        localStorage.removeItem(key);
        return true;
      }
    } catch (error) {
      console.error('Error removing data:', error);
      return false;
    }
  }

  /**
   * Get all keys in storage
   */
  async getAllKeys(): Promise<string[]> {
    await this.initialize();

    try {
      console.log('Getting all keys');

      if (isPyWebViewAvailable()) {
        // Use PyWebView API in desktop mode
        const api = (window as any).pywebview.api;
        if (typeof api.get_all_keys !== 'function') {
          throw new Error('PyWebView API get_all_keys method not found');
        }

        const keysJson = await api.get_all_keys();
        return JSON.parse(keysJson);
      } else {
        // Use localStorage in browser mode
        return Object.keys(localStorage);
      }
    } catch (error) {
      console.error('Error getting keys:', error);
      return [];
    }
  }
}

/**
 * Authentication Service
 * Handles user authentication and session management
 */
class AuthService {
  private currentUser: User | null = null;
  private listeners: AuthChangeListener[] = [];
  private storage: StorageSystem;

  // Constants
  private readonly USER_KEY = 'financial_advisor_user';
  private readonly USERS_KEY = 'financial_advisor_users';

  constructor(storage: StorageSystem) {
    this.storage = storage;
  }

  /**
   * Initialize the auth service and load the current user
   */
  async initialize(): Promise<void> {
    console.log('Initializing auth service...');

    // Load the current user
    this.currentUser = await this.storage.loadData<User>(this.USER_KEY);
    console.log('Current user:', this.currentUser?.email || 'None');

    // If no user found, create and use a default user
    if (!this.currentUser) {
      await this.autoLogin();
    }
  }

  /**
   * Auto login with a default user (no authentication)
   */
  async autoLogin(): Promise<User> {
    console.log('Auto-logging in with default user');

    // Create a default user
    const defaultUser: User = {
      id: 'default-user',
      email: '<EMAIL>',
      created_at: new Date().toISOString()
    };

    // Save the user
    await this.storage.saveData(this.USER_KEY, defaultUser);

    // Set as current user
    this.currentUser = defaultUser;

    // Notify listeners
    this.notifyListeners('SIGNED_IN', defaultUser);

    return defaultUser;
  }

  /**
   * Get the current user
   */
  async getCurrentUser(): Promise<User | null> {
    if (!this.currentUser) {
      // Try to load from storage
      this.currentUser = await this.storage.loadData<User>(this.USER_KEY);
    }
    return this.currentUser;
  }

  /**
   * Register a new user
   */
  async signUp(email: string, password: string): Promise<User> {
    if (!email || !email.includes('@')) {
      throw new Error('Invalid email format');
    }

    if (!password || password.length < 6) {
      throw new Error('Password must be at least 6 characters');
    }

    // Load existing users
    const users = await this.storage.loadData<Record<string, { email: string, password: string }>>(this.USERS_KEY) || {};

    // Generate a unique ID from email (using browser-compatible method)
    const userId = `user-${btoa(email)}`;

    // Check if user already exists
    if (users[userId]) {
      throw new Error('User already exists');
    }

    // Create the new user
    users[userId] = { email, password };

    // Save the updated users list
    await this.storage.saveData(this.USERS_KEY, users);

    // Create user object
    const user: User = {
      id: userId,
      email,
      created_at: new Date().toISOString()
    };

    // Save current user
    await this.storage.saveData(this.USER_KEY, user);

    // Update current user
    this.currentUser = user;

    // Notify listeners
    this.notifyListeners('SIGNED_UP', user);

    return user;
  }

  /**
   * Sign in an existing user
   */
  async signIn(email: string, password: string): Promise<User> {
    if (!email || !email.includes('@')) {
      throw new Error('Invalid email format');
    }

    if (!password || password.length < 6) {
      throw new Error('Password must be at least 6 characters');
    }

    // Load existing users
    const users = await this.storage.loadData<Record<string, { email: string, password: string }>>(this.USERS_KEY) || {};

    // Generate ID from email (using browser-compatible method)
    const userId = `user-${btoa(email)}`;

    // Check if user exists and password matches
    if (!users[userId] || users[userId].password !== password) {
      throw new Error('Invalid email or password');
    }

    // Create user object
    const user: User = {
      id: userId,
      email,
      created_at: new Date().toISOString()
    };

    // Save current user
    await this.storage.saveData(this.USER_KEY, user);

    // Update current user
    this.currentUser = user;

    // Notify listeners
    this.notifyListeners('SIGNED_IN', user);

    return user;
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<void> {
    // Remove current user from storage
    await this.storage.removeData(this.USER_KEY);

    // Update current user
    const prevUser = this.currentUser;
    this.currentUser = null;

    // Notify listeners if there was a user
    if (prevUser) {
      this.notifyListeners('SIGNED_OUT', null);
    }
  }

  /**
   * Listen for authentication state changes
   */
  onAuthStateChange(listener: AuthChangeListener): { unsubscribe: () => void } {
    // Add the listener
    this.listeners.push(listener);

    // Return an unsubscribe function
    return {
      unsubscribe: () => {
        this.listeners = this.listeners.filter(l => l !== listener);
      }
    };
  }

  /**
   * Notify all listeners of an auth state change
   */
  private notifyListeners(event: AuthChangeEvent, user: User | null): void {
    this.listeners.forEach(listener => {
      try {
        listener(event, user);
      } catch (error) {
        console.error('Error in auth state change listener:', error);
      }
    });
  }
}

/**
 * Data Service
 * Handles storage and retrieval of application data
 */
class DataService {
  private storage: StorageSystem;

  constructor(storage: StorageSystem) {
    this.storage = storage;
  }

  /**
   * Save data with the given key
   */
  async saveData<T>(collection: string, data: T): Promise<T> {
    // Add id if not present
    if (typeof data === 'object' && data !== null && !('id' in data)) {
      (data as any).id = this.generateId();
    }

    // Add timestamps
    if (typeof data === 'object' && data !== null) {
      const now = new Date().toISOString();
      if (!('created_at' in data)) {
        (data as any).created_at = now;
      }
      (data as any).updated_at = now;
    }

    // If data has an ID, save it with the ID in the key
    const id = typeof data === 'object' && data !== null && 'id' in data
      ? (data as any).id
      : this.generateId();

    // Save the data
    const key = `${collection}/${id}`;
    await this.storage.saveData(key, data);

    // Add to collection index
    await this.addToCollectionIndex(collection, id);

    return data;
  }

  /**
   * Save multiple items to a collection
   */
  async saveCollection<T>(collection: string, items: T[]): Promise<T[]> {
    // Save each item
    const savedItems = await Promise.all(items.map(item => this.saveData(collection, item)));

    return savedItems;
  }

  /**
   * Load data with the given key
   */
  async loadData<T>(collection: string, id: string): Promise<T | null> {
    const key = `${collection}/${id}`;
    return await this.storage.loadData<T>(key);
  }

  /**
   * Load all data from a collection
   */
  async loadCollection<T>(collection: string): Promise<T[]> {
    // Get the collection index
    const index = await this.getCollectionIndex(collection);

    // Load each item
    const items = await Promise.all(
      index.map(id => this.loadData<T>(collection, id))
    );

    // Filter out nulls
    return items.filter(item => item !== null) as T[];
  }

  /**
   * Delete data with the given key
   */
  async deleteData(collection: string, id: string): Promise<boolean> {
    const key = `${collection}/${id}`;

    // Remove from storage
    const success = await this.storage.removeData(key);

    // Remove from collection index
    if (success) {
      await this.removeFromCollectionIndex(collection, id);
    }

    return success;
  }

  /**
   * Remove an item from the collection index
   */
  private async removeFromCollectionIndex(collection: string, id: string): Promise<void> {
    const key = `${collection}_index`;
    const index = await this.storage.loadData<string[]>(key) || [];

    // Remove from index
    const newIndex = index.filter(itemId => itemId !== id);
    await this.storage.saveData(key, newIndex);
  }

  /**
   * Add a new item to a collection
   */
  async addItem<T extends { id: string }>(collection: string, item: T): Promise<T> {
    console.log(`Adding item to ${collection}:`, item);
    return this.saveData(collection, item);
  }

  /**
   * Update an existing item in a collection
   */
  async updateItem<T extends { id: string }>(collection: string, id: string, item: T): Promise<T> {
    console.log(`Updating item in ${collection} with id ${id}:`, item);
    // Make sure the item has the correct ID
    item.id = id;
    return this.saveData(collection, item);
  }

  /**
   * Remove an item from a collection
   */
  async removeItem(collection: string, id: string): Promise<boolean> {
    console.log(`Removing item from ${collection} with id ${id}`);
    return this.deleteData(collection, id);
  }

  /**
   * Generate a unique ID
   */
  private generateId(): string {
    return `id-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Get the index of items in a collection
   */
  private async getCollectionIndex(collection: string): Promise<string[]> {
    const key = `${collection}_index`;
    return await this.storage.loadData<string[]>(key) || [];
  }

  /**
   * Add an item to the collection index
   */
  private async addToCollectionIndex(collection: string, id: string): Promise<void> {
    const key = `${collection}_index`;
    const index = await this.storage.loadData<string[]>(key) || [];

    // Add to index if not already there
    if (!index.includes(id)) {
      index.push(id);
      await this.storage.saveData(key, index);
    }
  }
}

/**
 * Utils for local data store
 */
const utils = {
  /**
   * Generate a unique ID
   */
  generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }
};

/**
 * Create and export the singleton local data store
 */
export const localDataStore = {
  storage: new StorageSystem(),
  auth: new AuthService(new StorageSystem()),
  data: new DataService(new StorageSystem()),
  utils
};

// Expose utility functions for debugging
if (typeof window !== 'undefined') {
  // Expose the dumpStorage function
  window.dumpStorage = async () => {
    const storage = new StorageSystem();
    await storage.initialize();
    const keys = await storage.getAllKeys();
    const data = await Promise.all(keys.map(key => storage.loadData(key)));
    return JSON.stringify(data, null, 2);
  };

  // Expose the debugStorage function
  window.debugStorage = async () => {
    const storage = new StorageSystem();
    await storage.initialize();
    const keys = await storage.getAllKeys();
    const data = await Promise.all(keys.map(key => storage.loadData(key)));
    console.log('Storage Data:');
    console.log(JSON.stringify(data, null, 2));
  };
}
