/**
 * PyWebView Bridge
 * 
 * This file provides a reliable bridge between the React app and PyWebView's Python backend.
 * It handles the initialization of the PyWebView API and provides fallbacks for development.
 */

// Create a placeholder API for development mode
const devModeApi = {
  get_item: async (key) => {
    console.log('[DEV] get_item:', key);
    const stored = localStorage.getItem(key);
    return stored;
  },
  set_item: async (key, value) => {
    console.log('[DEV] set_item:', key, value);
    localStorage.setItem(key, value);
    return true;
  },
  remove_item: async (key) => {
    console.log('[DEV] remove_item:', key);
    localStorage.removeItem(key);
    return true;
  },
  get_all_keys: async () => {
    console.log('[DEV] get_all_keys');
    const keys = [];
    for (let i = 0; i < localStorage.length; i++) {
      keys.push(localStorage.key(i));
    }
    return JSON.stringify(keys);
  },
  dump_storage: async () => {
    console.log('[DEV] dump_storage');
    const dump = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      dump[key] = localStorage.getItem(key);
    }
    return JSON.stringify(dump);
  },
  clean_indexes: async () => {
    console.log('[DEV] clean_indexes');
    return true;
  },
  repair_storage: async () => {
    console.log('[DEV] repair_storage');
    return true;
  },
  get_storage_stats: async () => {
    console.log('[DEV] get_storage_stats');
    return JSON.stringify({
      items: localStorage.length,
      size: JSON.stringify(localStorage).length
    });
  },
  get_collection: async (collection) => {
    console.log('[DEV] get_collection:', collection);
    const collectionData = localStorage.getItem(`${collection}_data`);
    return collectionData || '[]';
  }
};

// Initialize the PyWebView API
let pywebviewApi = null;

// Function to initialize the PyWebView API
const initPyWebViewApi = () => {
  // Check if we're running in PyWebView
  if (window.pywebview) {
    console.log('PyWebView detected, using native API');
    return window.pywebview.api;
  }

  // Check if we have the pywebview_channel for newer versions
  if (window.pywebview_channel) {
    console.log('PyWebView channel detected, creating API wrapper');
    
    // Create an API wrapper that uses the channel
    return {
      get_item: (key) => window.pywebview_channel.call('get_item', key),
      set_item: (key, value) => window.pywebview_channel.call('set_item', key, value),
      remove_item: (key) => window.pywebview_channel.call('remove_item', key),
      get_all_keys: () => window.pywebview_channel.call('get_all_keys'),
      dump_storage: () => window.pywebview_channel.call('dump_storage'),
      clean_indexes: () => window.pywebview_channel.call('clean_indexes'),
      repair_storage: () => window.pywebview_channel.call('repair_storage'),
      get_storage_stats: () => window.pywebview_channel.call('get_storage_stats'),
      get_collection: (collection) => window.pywebview_channel.call('get_collection', collection)
    };
  }

  // If we're not in PyWebView, use the development mode API
  console.log('PyWebView not detected, using development mode API');
  return devModeApi;
};

// Function to ensure the API is initialized
const ensureApiInitialized = () => {
  if (!pywebviewApi) {
    pywebviewApi = initPyWebViewApi();
  }
  return pywebviewApi;
};

// Create a wrapper for the PyWebView API
const pywebviewBridge = {
  // Storage methods
  getItem: async (key) => {
    const api = ensureApiInitialized();
    try {
      return await api.get_item(key);
    } catch (error) {
      console.error('Error in getItem:', error);
      return null;
    }
  },
  
  setItem: async (key, value) => {
    const api = ensureApiInitialized();
    try {
      return await api.set_item(key, value);
    } catch (error) {
      console.error('Error in setItem:', error);
      return false;
    }
  },
  
  removeItem: async (key) => {
    const api = ensureApiInitialized();
    try {
      return await api.remove_item(key);
    } catch (error) {
      console.error('Error in removeItem:', error);
      return false;
    }
  },
  
  getAllKeys: async () => {
    const api = ensureApiInitialized();
    try {
      const keysJson = await api.get_all_keys();
      return JSON.parse(keysJson || '[]');
    } catch (error) {
      console.error('Error in getAllKeys:', error);
      return [];
    }
  },
  
  dumpStorage: async () => {
    const api = ensureApiInitialized();
    try {
      const dumpJson = await api.dump_storage();
      return JSON.parse(dumpJson || '{}');
    } catch (error) {
      console.error('Error in dumpStorage:', error);
      return {};
    }
  },
  
  cleanIndexes: async () => {
    const api = ensureApiInitialized();
    try {
      return await api.clean_indexes();
    } catch (error) {
      console.error('Error in cleanIndexes:', error);
      return false;
    }
  },
  
  repairStorage: async () => {
    const api = ensureApiInitialized();
    try {
      return await api.repair_storage();
    } catch (error) {
      console.error('Error in repairStorage:', error);
      return false;
    }
  },
  
  getStorageStats: async () => {
    const api = ensureApiInitialized();
    try {
      const statsJson = await api.get_storage_stats();
      return JSON.parse(statsJson || '{"items":0,"size":0}');
    } catch (error) {
      console.error('Error in getStorageStats:', error);
      return { items: 0, size: 0 };
    }
  },
  
  getCollection: async (collection) => {
    const api = ensureApiInitialized();
    try {
      const collectionJson = await api.get_collection(collection);
      return JSON.parse(collectionJson || '[]');
    } catch (error) {
      console.error('Error in getCollection:', error);
      return [];
    }
  },
  
  // Utility methods
  isRunningInPyWebView: () => {
    return !!(window.pywebview && (window.pywebview.api || window.pywebview.pywebview_channel)); // Update check
  },
  
  testConnection: async () => {
    const api = ensureApiInitialized();
    try {
      const testKey = `test-connection-${Date.now()}`;
      const testValue = JSON.stringify({ timestamp: Date.now() });
      
      // Use the typed api
      await api.set_item(testKey, testValue);
      const retrieved = await api.get_item(testKey);
      await api.remove_item(testKey);
      
      return retrieved === testValue;
    } catch (error) {
      console.error('Error in testConnection:', error);
      return false;
    }
  }
};

// Initialize the API when the module is loaded
ensureApiInitialized();

// Listen for PyWebView ready event
window.addEventListener('pywebviewready', () => {
  console.log('PyWebView ready event received');
  // Re-initialize the API to use the PyWebView API
  pywebviewApi = initPyWebViewApi();
});

// Export the bridge
export default pywebviewBridge;
