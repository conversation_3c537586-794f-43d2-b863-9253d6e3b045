/**
 * Storage Bridge
 *
 * This module bridges localStorage operations to the Python backend
 * for persistent data storage in the desktop application.
 *
 * Updated to use the more reliable pywebview-bridge.js
 */

import pywebviewBridge from './pywebview-bridge';

// Check if we're running in a PyWebView environment
const isPyWebView = window.USE_LOCAL_STORE === true ||
                    process.env.VITE_USE_LOCAL_STORE === 'true' ||
                    process.env.REACT_APP_USE_LOCAL_STORE === 'true' ||
                    pywebviewBridge.isRunningInPyWebView();

// Create a storage bridge that mimics the localStorage API
class StorageBridge {
  constructor() {
    this.isReady = false;
    this.pendingOperations = [];

    // Set a flag to indicate we're using local storage
    window.USE_LOCAL_STORE = isPyWebView;

    // Initialize
    this.initialize();
  }

  async initialize() {
    console.log('Initializing StorageBridge...');
    console.log('isPyWebView:', isPyWebView);

    if (isPyWebView) {
      try {
        // Test the connection to make sure it's working
        const connectionTest = await pywebviewBridge.testConnection();
        console.log('PyWebView connection test:', connectionTest ? 'SUCCESS' : 'FAILED');

        // Set ready state based on connection test
        this.isReady = connectionTest;

        // If connection failed, fall back to localStorage
        if (!connectionTest) {
          console.warn('PyWebView connection test failed, falling back to localStorage');
        } else {
          console.log('PyWebView connection established, using Python backend storage');
        }
      } catch (error) {
        console.error('Error testing PyWebView connection:', error);
        this.isReady = false;
      }
    } else {
      // When running in a regular browser, just use localStorage
      console.log('Running in browser mode, using regular localStorage');
      this.isReady = true;
    }

    // Process any pending operations
    if (this.isReady) {
      this.pendingOperations.forEach(op => op());
      this.pendingOperations = [];
    }

    // Listen for pywebview ready event
    window.addEventListener('pywebviewready', () => {
      console.log('PyWebView is ready event received, reinitializing storage bridge');
      this.isReady = true;

      // Process any pending operations
      this.pendingOperations.forEach(op => op());
      this.pendingOperations = [];
    });
  }

  /**
   * Get an item from storage
   */
  async getItem(key) {
    if (!isPyWebView) {
      // In browser mode, use regular localStorage
      return localStorage.getItem(key);
    }

    if (!this.isReady) {
      console.warn('StorageBridge not ready yet for getItem, returning null');
      return null;
    }

    // Use the PyWebView bridge to get the item
    try {
      return await pywebviewBridge.getItem(key);
    } catch (error) {
      console.error('Error in getItem:', error);
      return null;
    }
  }

  /**
   * Set an item in storage
   */
  async setItem(key, value) {
    if (!isPyWebView) {
      // In browser mode, use regular localStorage
      return localStorage.setItem(key, value);
    }

    if (!this.isReady) {
      // Queue the operation for when we're ready
      this.pendingOperations.push(() => this.setItem(key, value));
      console.warn('StorageBridge not ready yet, setItem operation queued');
      return;
    }

    // Use the PyWebView bridge to set the item
    try {
      return await pywebviewBridge.setItem(key, value);
    } catch (error) {
      console.error('Error in setItem:', error);
    }
  }

  /**
   * Remove an item from storage
   */
  async removeItem(key) {
    if (!isPyWebView) {
      // In browser mode, use regular localStorage
      return localStorage.removeItem(key);
    }

    if (!this.isReady) {
      // Queue the operation for when we're ready
      this.pendingOperations.push(() => this.removeItem(key));
      console.warn('StorageBridge not ready yet, removeItem operation queued');
      return;
    }

    // Use the PyWebView bridge to remove the item
    try {
      return await pywebviewBridge.removeItem(key);
    } catch (error) {
      console.error('Error in removeItem:', error);
    }
  }

  /**
   * Get all keys in storage
   */
  async getAllKeys() {
    if (!isPyWebView) {
      // In browser mode, use regular localStorage
      return Object.keys(localStorage);
    }

    if (!this.isReady) {
      console.warn('StorageBridge not ready yet, getAllKeys will return empty array');
      return [];
    }

    // Use the PyWebView bridge to get all keys
    try {
      return await pywebviewBridge.getAllKeys();
    } catch (error) {
      console.error('Error in getAllKeys:', error);
      return [];
    }
  }

  /**
   * Get a collection of items
   */
  async getCollection(collection) {
    if (!isPyWebView) {
      // In browser mode, use regular localStorage
      const collectionData = localStorage.getItem(`${collection}_data`);
      return JSON.parse(collectionData || '[]');
    }

    if (!this.isReady) {
      console.warn('StorageBridge not ready yet, getCollection will return empty array');
      return [];
    }

    // Use the PyWebView bridge to get the collection
    try {
      return await pywebviewBridge.getCollection(collection);
    } catch (error) {
      console.error('Error in getCollection:', error);
      return [];
    }
  }
}

// Create a singleton instance
const storageBridge = new StorageBridge();

// Export the bridge
export default storageBridge;
