// This is a mock implementation of Supabase client that uses storageBridge
// It provides the same API shape but stores data locally
import storageBridge from './storage-bridge';

// Define SupabaseUser to match the User interface in types/index.ts
interface SupabaseUser {
  id: string;
  email: string;  // Not optional to match User interface
  created_at: string;
}

interface AuthResponse {
  data: {
    user: SupabaseUser | null;
    session: any | null;
  };
  error: Error | null;
}

interface SupabaseQueryResponse<T> {
  data: T[] | null;
  error: Error | null;
}

interface SupabaseSingleResponse<T> {
  data: T | null;
  error: Error | null;
}

// Define an interface for generic database items
interface DbItem {
  id?: string;
  [key: string]: any;
}

// Define AuthStateChangeCallback
type AuthStateChangeCallback = (event: string, session: { user: SupabaseUser | null } | null) => void;

class LocalSupabaseClient {
  private authStateChangeCallbacks: AuthStateChangeCallback[] = [];
  private storageInitialized: boolean = false;
  
  // Storage interface
  storage = {
    initialize: async (): Promise<boolean> => {
      if (this.storageInitialized) {
        return true;
      }
      
      console.log('Initializing storage in LocalSupabaseClient');
      try {
        // Wait for the storage bridge to initialize
        await new Promise(resolve => setTimeout(resolve, 500)); // Small delay for stability
        this.storageInitialized = true;
        console.log('Storage initialized in LocalSupabaseClient');
        return true;
      } catch (error) {
        console.error('Error initializing storage:', error);
        return false;
      }
    }
  };

  // Auth methods
  auth = {
    getSession: async (): Promise<{ data: { session: { user: SupabaseUser } | null } }> => {
      try {
        // Ensure storage is initialized
        await this.storage.initialize();
        
        console.log('Getting session from storage');
        const userJson = await storageBridge.getItem('local_auth_user');
        console.log('Got session data:', userJson ? 'User found' : 'No user found');
        
        const user = userJson ? JSON.parse(userJson) : null;
        return {
          data: {
            session: user ? { user } : null
          }
        };
      } catch (error) {
        console.error('Error getting session:', error);
        return {
          data: { session: null }
        };
      }
    },
    
    onAuthStateChange: (callback: AuthStateChangeCallback) => {
      console.log('Registering auth state change callback');
      this.authStateChangeCallbacks.push(callback);
      return {
        data: { subscription: { unsubscribe: () => {
          this.authStateChangeCallbacks = this.authStateChangeCallbacks.filter(cb => cb !== callback);
        }}},
        error: null
      };
    },
    
    signInWithPassword: async ({ email, password }: { email: string, password: string }): Promise<AuthResponse> => {
      try {
        // Ensure storage is initialized
        await this.storage.initialize();
        
        console.log('Signing in with password:', email);
        
        // In a real app, you'd validate credentials
        // For demo, create a mock user if valid format
        if (!email || !email.includes('@')) {
          throw new Error('Invalid email format');
        }
        
        if (!password || password.length < 6) {
          throw new Error('Password must be at least 6 characters');
        }
        
        // Check if this user exists in our mock DB
        const usersJson = await storageBridge.getItem('local_users');
        const users: Record<string, { email: string, password: string }> = usersJson ? JSON.parse(usersJson) : {};
        
        // Generate user ID based on email for consistency
        const userId = `user-${btoa(email)}`;
        
        // Check if user exists and password matches
        if (!users[userId] || users[userId].password !== password) {
          console.error('Invalid login credentials');
          throw new Error('Invalid login credentials');
        }
        
        // Create session user
        const user: SupabaseUser = {
          id: userId,
          email,
          created_at: new Date().toISOString()
        };
        
        // Save user to session
        await storageBridge.setItem('local_auth_user', JSON.stringify(user));
        console.log('User signed in successfully:', email);
        
        // Trigger auth state change
        this.triggerAuthStateChange('SIGNED_IN', { user });
        
        return {
          data: {
            user,
            session: { user }
          },
          error: null
        };
      } catch (error) {
        console.error('Error signing in:', error);
        return {
          data: { user: null, session: null },
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    },
    
    signUp: async ({ email, password }: { email: string, password: string }): Promise<AuthResponse> => {
      try {
        // Ensure storage is initialized
        await this.storage.initialize();
        
        console.log('Signing up new user:', email);
        
        // Validate inputs
        if (!email || !email.includes('@')) {
          throw new Error('Invalid email format');
        }
        
        if (!password || password.length < 6) {
          throw new Error('Password must be at least 6 characters');
        }
        
        // Create a unique ID based on email (for consistency)
        const userId = `user-${btoa(email)}`;
        
        // Store the user credentials
        const usersJson = await storageBridge.getItem('local_users');
        const users: Record<string, { email: string, password: string }> = usersJson ? JSON.parse(usersJson) : {};
        
        // Check if user already exists
        if (users[userId]) {
          throw new Error('User already exists');
        }
        
        // Add new user
        users[userId] = { email, password };
        await storageBridge.setItem('local_users', JSON.stringify(users));
        
        // Create session user
        const user: SupabaseUser = {
          id: userId,
          email,
          created_at: new Date().toISOString()
        };
        
        // Save user to session
        await storageBridge.setItem('local_auth_user', JSON.stringify(user));
        console.log('User signed up successfully:', email);
        
        // Trigger auth state change
        this.triggerAuthStateChange('SIGNED_UP', { user });
        
        return {
          data: {
            user,
            session: { user }
          },
          error: null
        };
      } catch (error) {
        console.error('Error signing up:', error);
        return {
          data: { user: null, session: null },
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    },
    
    signOut: async (): Promise<{ error: Error | null }> => {
      try {
        // Ensure storage is initialized
        await this.storage.initialize();
        
        console.log('Signing out user');
        
        // Get current user before removing
        const userJson = await storageBridge.getItem('local_auth_user');
        const user = userJson ? JSON.parse(userJson) : null;
        
        // Remove user from session
        await storageBridge.removeItem('local_auth_user');
        console.log('User signed out successfully');
        
        // Trigger auth state change
        if (user) {
          this.triggerAuthStateChange('SIGNED_OUT', null);
        }
        
        return { error: null };
      } catch (error) {
        console.error('Error signing out:', error);
        return { error: error instanceof Error ? error : new Error(String(error)) };
      }
    },
    
    getUser: async (): Promise<AuthResponse> => {
      try {
        // Ensure storage is initialized
        await this.storage.initialize();
        
        console.log('Getting current user');
        const userJson = await storageBridge.getItem('local_auth_user');
        const user = userJson ? JSON.parse(userJson) : null;
        console.log('Current user:', user?.email || 'None');
        
        return {
          data: {
            user,
            session: user ? { user } : null
          },
          error: null
        };
      } catch (error) {
        console.error('Error getting user:', error);
        return {
          data: { user: null, session: null },
          error: error instanceof Error ? error : new Error(String(error))
        };
      }
    }
  };
  
  // Helper method to trigger auth state change
  private triggerAuthStateChange(event: string, session: { user: SupabaseUser } | null) {
    console.log('Triggering auth state change:', event, session?.user?.email || 'null');
    this.authStateChangeCallbacks.forEach(callback => {
      try {
        callback(event, session);
      } catch (error) {
        console.error('Error in auth state change callback:', error);
      }
    });
  }
  
  // Database methods
  from = (tableName: string) => {
    const storageKey = `local_${tableName}`;
    
    return {
      select: (_columns = '*') => {
        return {
          eq: (column: string, value: any) => {
            return {
              order: (column: string, { ascending = true } = {}) => {
                return {
                  limit: (limit: number) => {
                    return {
                      async execute(): Promise<SupabaseQueryResponse<DbItem>> {
                        try {
                          // Ensure storage is initialized
                          await storageBridge.initialize();
                          
                          const storedData = await storageBridge.getItem(storageKey);
                          let data: DbItem[] = storedData ? JSON.parse(storedData) : [];
                          
                          // Filter by equality
                          data = data.filter((item: DbItem) => item[column] === value);
                          
                          // Sort data
                          data.sort((a: DbItem, b: DbItem) => {
                            if (a[column] < b[column]) return ascending ? -1 : 1;
                            if (a[column] > b[column]) return ascending ? 1 : -1;
                            return 0;
                          });
                          
                          // Apply limit
                          if (limit > 0) {
                            data = data.slice(0, limit);
                          }
                          
                          return { data, error: null };
                        } catch (error) {
                          console.error('Error in query:', error);
                          return { 
                            data: null, 
                            error: error instanceof Error ? error : new Error(String(error))
                          };
                        }
                      }
                    };
                  },
                  async execute(): Promise<SupabaseQueryResponse<DbItem>> {
                    try {
                      // Ensure storage is initialized
                      await storageBridge.initialize();
                      
                      const storedData = await storageBridge.getItem(storageKey);
                      let data: DbItem[] = storedData ? JSON.parse(storedData) : [];
                      
                      // Filter by equality
                      data = data.filter((item: DbItem) => item[column] === value);
                      
                      // Sort data
                      data.sort((a: DbItem, b: DbItem) => {
                        if (a[column] < b[column]) return ascending ? -1 : 1;
                        if (a[column] > b[column]) return ascending ? 1 : -1;
                        return 0;
                      });
                      
                      return { data, error: null };
                    } catch (error) {
                      console.error('Error in query:', error);
                      return { 
                        data: null, 
                        error: error instanceof Error ? error : new Error(String(error))
                      };
                    }
                  }
                };
              },
              async execute(): Promise<SupabaseQueryResponse<DbItem>> {
                try {
                  // Ensure storage is initialized
                  await storageBridge.initialize();
                  
                  const storedData = await storageBridge.getItem(storageKey);
                  const data: DbItem[] = storedData 
                    ? JSON.parse(storedData).filter((item: DbItem) => item[column] === value)
                    : [];
                  
                  return { data, error: null };
                } catch (error) {
                  console.error('Error in query:', error);
                  return { 
                    data: null, 
                    error: error instanceof Error ? error : new Error(String(error))
                  };
                }
              }
            };
          },
          async execute(): Promise<SupabaseQueryResponse<DbItem>> {
            try {
              // Ensure storage is initialized
              await storageBridge.initialize();
              
              const storedData = await storageBridge.getItem(storageKey);
              const data: DbItem[] = storedData ? JSON.parse(storedData) : [];
              
              return { data, error: null };
            } catch (error) {
              console.error('Error in query:', error);
              return { 
                data: null, 
                error: error instanceof Error ? error : new Error(String(error))
              };
            }
          }
        };
      },
      
      insert: (item: DbItem) => {
        return {
          select: () => {
            return {
              single: async (): Promise<SupabaseSingleResponse<DbItem>> => {
                try {
                  // Ensure storage is initialized
                  await storageBridge.initialize();
                  
                  const storedData = await storageBridge.getItem(storageKey);
                  const items: DbItem[] = storedData ? JSON.parse(storedData) : [];
                  
                  // Generate an ID if not provided
                  if (!item.id) {
                    item.id = `local-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
                  }
                  
                  // Add timestamps
                  item.created_at = new Date().toISOString();
                  item.updated_at = new Date().toISOString();
                  
                  // Add the item
                  items.push(item);
                  
                  // Save back to storage
                  await storageBridge.setItem(storageKey, JSON.stringify(items));
                  
                  return { data: item, error: null };
                } catch (error) {
                  console.error('Error in insert:', error);
                  return { 
                    data: null, 
                    error: error instanceof Error ? error : new Error(String(error))
                  };
                }
              }
            };
          },
          async execute(): Promise<SupabaseQueryResponse<DbItem>> {
            try {
              // Ensure storage is initialized
              await storageBridge.initialize();
              
              const storedData = await storageBridge.getItem(storageKey);
              const items: DbItem[] = storedData ? JSON.parse(storedData) : [];
              
              // Generate an ID if not provided
              if (!item.id) {
                item.id = `local-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
              }
              
              // Add timestamps
              item.created_at = new Date().toISOString();
              item.updated_at = new Date().toISOString();
              
              // Add the item
              items.push(item);
              
              // Save back to storage
              await storageBridge.setItem(storageKey, JSON.stringify(items));
              
              return { data: [item], error: null };
            } catch (error) {
              console.error('Error in insert:', error);
              return { 
                data: null, 
                error: error instanceof Error ? error : new Error(String(error))
              };
            }
          }
        };
      },
      
      update: (updates: DbItem) => {
        return {
          eq: (column: string, value: any) => {
            return {
              single: () => {
                return {
                  async execute(): Promise<SupabaseSingleResponse<DbItem>> {
                    try {
                      // Ensure storage is initialized
                      await storageBridge.initialize();
                      
                      const storedData = await storageBridge.getItem(storageKey);
                      const items: DbItem[] = storedData ? JSON.parse(storedData) : [];
                      
                      const index = items.findIndex((item: DbItem) => item[column] === value);
                      if (index === -1) {
                        throw new Error(`No item found with ${column} = ${value}`);
                      }
                      
                      // Update the item
                      const updatedItem: DbItem = { ...items[index], ...updates };
                      items[index] = updatedItem;
                      
                      // Save back to storage
                      await storageBridge.setItem(storageKey, JSON.stringify(items));
                      
                      return { data: updatedItem, error: null };
                    } catch (error) {
                      console.error('Error in update:', error);
                      return { 
                        data: null, 
                        error: error instanceof Error ? error : new Error(String(error))
                      };
                    }
                  }
                };
              },
              async execute(): Promise<SupabaseQueryResponse<DbItem>> {
                try {
                  // Ensure storage is initialized
                  await storageBridge.initialize();
                  
                  const storedData = await storageBridge.getItem(storageKey);
                  const items: DbItem[] = storedData ? JSON.parse(storedData) : [];
                  
                  const updatedItems: DbItem[] = [];
                  
                  // Create a new array with updated items
                  const newItems = items.map((item: DbItem) => {
                    if (item[column] === value) {
                      const updatedItem = { ...item, ...updates };
                      updatedItems.push(updatedItem);
                      return updatedItem;
                    }
                    return item;
                  });
                  
                  // Save back to storage
                  await storageBridge.setItem(storageKey, JSON.stringify(newItems));
                  
                  return { data: updatedItems, error: null };
                } catch (error) {
                  console.error('Error in update:', error);
                  return { 
                    data: null, 
                    error: error instanceof Error ? error : new Error(String(error))
                  };
                }
              }
            };
          }
        };
      },
      
      delete: () => {
        return {
          eq: (column: string, value: any) => {
            return {
              async execute(): Promise<SupabaseQueryResponse<DbItem>> {
                try {
                  // Ensure storage is initialized
                  await storageBridge.initialize();
                  
                  const storedData = await storageBridge.getItem(storageKey);
                  const items: DbItem[] = storedData ? JSON.parse(storedData) : [];
                  
                  // Filter out items to delete
                  const deletedItems: DbItem[] = items.filter((item: DbItem) => item[column] === value);
                  const filteredItems = items.filter((item: DbItem) => item[column] !== value);
                  
                  // Save back to storage
                  await storageBridge.setItem(storageKey, JSON.stringify(filteredItems));
                  
                  return { data: deletedItems, error: null };
                } catch (error) {
                  console.error('Error in delete:', error);
                  return { 
                    data: null, 
                    error: error instanceof Error ? error : new Error(String(error))
                  };
                }
              }
            };
          }
        };
      }
    };
  };
}

// Create and export the mock supabase client
export const supabase = new LocalSupabaseClient();