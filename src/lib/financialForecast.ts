import { formatCurrency } from '../utils/currency';

export interface EducationPayment {
  month: string;
  amount: number;
  description?: string;
}

export interface MonthlyForecast {
  month: string;
  income: number;
  loans: number;
  expenses: number;
  education_payment: number;
  net_cashflow: number;
  remaining_savings: number;
}

export interface FinancialForecastResult {
  education_reserved: number;
  initial_remaining_savings: number;
  monthly_forecast: MonthlyForecast[];
  final_savings: number;
  risk_level: 'Low' | 'Medium' | 'High';
  advice: string[];
}

export interface LoanPayment {
  month: string;
  amount: number;
}

export interface ForecastInputs {
  monthly_income: number;
  monthly_loans: number;
  monthly_expenses: number;
  current_savings: number;
  education_schedule: EducationPayment[];
  loan_schedule?: LoanPayment[]; // Optional month-specific loan payments
  forecast_months?: string[];
}

export function financialRiskForecast(inputs: ForecastInputs): FinancialForecastResult {
  const {
    monthly_income,
    monthly_loans,
    monthly_expenses,
    current_savings,
    education_schedule,
    loan_schedule = [],
    forecast_months = ['Sep', 'Oct', 'Nov', 'Dec', 'Jan']
  } = inputs;

  // Convert education schedule to a map for easier lookup
  const educationMap = education_schedule.reduce((acc, payment) => {
    acc[payment.month] = payment.amount;
    return acc;
  }, {} as Record<string, number>);

  // Convert loan schedule to a map for easier lookup
  const loanMap = loan_schedule.reduce((acc, payment) => {
    acc[payment.month] = payment.amount;
    return acc;
  }, {} as Record<string, number>);

  // Initialize result tracking
  const monthly_deficit_log: MonthlyForecast[] = [];
  let total_deficit = 0;
  let education_reserved = 0;

  // Reserve education fees
  education_schedule.forEach(payment => {
    education_reserved += payment.amount;
  });

  let remaining_savings = current_savings - education_reserved;
  const initial_available_savings = remaining_savings;

  // Forecast for specified months
  forecast_months.forEach(month => {
    // Education payment in this month
    const edu_payment = educationMap[month] || 0;

    // Loan payment for this month (use month-specific if available, otherwise default)
    const loan_payment = loanMap[month] || monthly_loans;

    // Monthly cash flow = income - (loans + expenses + edu)
    const total_outflow = loan_payment + monthly_expenses + edu_payment;
    const net_balance = monthly_income - total_outflow;

    // Track deficit only when negative
    if (net_balance < 0) {
      total_deficit += Math.abs(net_balance);
    }

    // Update remaining savings: if net_balance is negative, it reduces savings
    remaining_savings += net_balance;

    monthly_deficit_log.push({
      month,
      income: monthly_income,
      loans: loan_payment, // Use the month-specific loan payment
      expenses: monthly_expenses,
      education_payment: edu_payment,
      net_cashflow: net_balance,
      remaining_savings: Math.max(0, remaining_savings) // Don't show negative savings
    });
  });

  // Assess risk level
  const risk_level: 'Low' | 'Medium' | 'High' =
    remaining_savings < 0 ? 'High' :
    remaining_savings < 100000 ? 'Medium' :
    'Low';

  // Generate advice
  const advice = generateAdvice(remaining_savings, total_deficit, monthly_loans, monthly_income);

  return {
    education_reserved,
    initial_remaining_savings: current_savings - education_reserved,
    monthly_forecast: monthly_deficit_log,
    final_savings: remaining_savings,
    risk_level,
    advice
  };
}

function generateAdvice(final_savings: number, total_deficit: number, monthly_loans: number, monthly_income: number): string[] {
  const advice: string[] = [];

  if (final_savings < 0) {
    advice.push("⚠️ Your savings will be fully depleted. Urgent action needed.");
  } else if (final_savings < 100000) {
    advice.push("⚠️ Your financial buffer is low. Consider reducing expenses or restructuring loans.");
  } else {
    advice.push("✅ Your financial position appears stable for the forecast period.");
  }

  advice.push("👉 Suggested Actions:");

  if (final_savings < 0 || final_savings < 100000) {
    const target_loan_reduction = Math.ceil((monthly_loans - (monthly_income * 0.8)) / 1000) * 1000;
    const target_income_increase = Math.ceil((monthly_loans + 30000 - monthly_income) / 1000) * 1000;

    advice.push(`- Negotiate with bank to lower monthly loan payments by ${formatCurrency(target_loan_reduction)}`);
    advice.push(`- Increase monthly income by at least ${formatCurrency(target_income_increase)}`);
    advice.push("- Explore part-time or freelance income options");
    advice.push("- Delay non-essential expenses until financial situation improves");
    advice.push("- Consider consolidating high-interest loans");
  } else {
    advice.push("- Maintain current spending discipline");
    advice.push("- Consider building additional emergency fund");
    advice.push("- Look for investment opportunities for excess savings");
  }

  return advice;
}

// Helper function to create default education schedule
export function createDefaultEducationSchedule(): EducationPayment[] {
  return [
    { month: 'Sep', amount: 243253, description: 'University + School fees' },
    { month: 'Nov', amount: 83724, description: 'Mid-semester payments' },
    { month: 'Dec', amount: 105389, description: 'End of semester fees' },
    { month: 'Jan', amount: 54890, description: 'New semester fees' }
  ];
}

// Helper function to get current month forecast
export function getCurrentMonthForecast(forecast: FinancialForecastResult): MonthlyForecast | null {
  const currentMonth = new Date().toLocaleDateString('en-US', { month: 'short' });
  return forecast.monthly_forecast.find(f => f.month === currentMonth) || null;
}

// Helper function to get risk color for UI
export function getRiskColor(riskLevel: 'Low' | 'Medium' | 'High'): string {
  switch (riskLevel) {
    case 'Low': return 'text-green-600 bg-green-50';
    case 'Medium': return 'text-yellow-600 bg-yellow-50';
    case 'High': return 'text-red-600 bg-red-50';
  }
}
