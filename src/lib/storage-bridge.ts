/**
 * Storage Bridge
 * 
 * This module bridges localStorage operations to the Python backend
 * for persistent data storage in the desktop application.
 */

// Define a type for the PyWebView API
interface PyWebViewApi {
  get_item: (key: string) => Promise<string | null>;
  set_item: (key: string, value: string) => Promise<boolean>;
  remove_item: (key: string) => Promise<boolean>;
  get_all_keys: () => Promise<string>;
  dump_storage: () => Promise<string>;
}

// Define a type for the PyWebView window object
interface PyWebViewWindow {
  pywebview?: {
    api: PyWebViewApi;
  };
}

// A promise that resolves when PyWebView API is ready
let pywebviewReadyPromise: Promise<boolean>;
let resolvePywebviewReady: ((value: boolean) => void) | null = null;

if (typeof window !== 'undefined') {
  pywebviewReadyPromise = new Promise<boolean>((resolve) => {
    resolvePywebviewReady = resolve;
  });
  
  // Listen for pywebview API ready event
  window.addEventListener('pywebview-api-ready', () => {
    console.log('PyWebView API is ready, storage bridge initialized');
    if (resolvePywebviewReady) {
      resolvePywebviewReady(true);
    }
  });
  
  // Fallback - if not in PyWebView, resolve immediately
  // Use a timeout to ensure the event listener has a chance to be added
  setTimeout(() => {
    if (typeof (window as unknown as PyWebViewWindow).pywebview === 'undefined') {
      console.log('Not running in PyWebView, using regular localStorage');
      if (resolvePywebviewReady) {
        resolvePywebviewReady(false);
      }
    }
  }, 0); // Use a small timeout to defer execution
}

// Create a storage bridge that mimics the localStorage API
class StorageBridge {
  private isPyWebView: boolean;
  private readyPromise: Promise<boolean>;
  private pendingOperations: Array<() => void>;
  private initializationStarted: boolean;
  
  constructor() {
    this.pendingOperations = [];
    this.initializationStarted = false;
    this.isPyWebView = typeof window !== 'undefined' && 
      typeof (window as unknown as PyWebViewWindow).pywebview !== 'undefined';
    this.readyPromise = pywebviewReadyPromise;
    
    // DEBUG: Log initial state
    if (typeof window !== 'undefined') {
      console.log('StorageBridge constructor');
      console.log('isPyWebView:', this.isPyWebView);
      
      // If running in browser, initialize immediately
      if (!this.isPyWebView) {
        console.log('Using regular localStorage for storage');
      } else {
        console.log('Using PyWebView API for storage');
      }
    }
  }

  /**
   * Initialize the storage bridge
   * This ensures the bridge is ready before operations are performed
   */
  async initialize(): Promise<boolean> {
    if (this.initializationStarted) {
      return this.readyPromise;
    }
    
    this.initializationStarted = true;
    console.log('Initializing StorageBridge...');
    
    try {
      const isPyWebView = await this.readyPromise;
      console.log('StorageBridge initialization complete, isPyWebView:', isPyWebView);
      
      // Process any pending operations
      for (const op of this.pendingOperations) {
        op();
      }
      this.pendingOperations = [];
      
      return isPyWebView;
    } catch (error) {
      console.error('StorageBridge initialization error:', error);
      return false;
    }
  }

  /**
   * Get an item from storage
   */
  async getItem(key: string): Promise<string | null> {
    // Ensure bridge is initialized
    await this.initialize();
    
    if (!this.isPyWebView) {
      // In browser mode, use regular localStorage
      return localStorage.getItem(key);
    }
    
    // Use the Python backend to get the item
    try {
      console.log(`StorageBridge.getItem(${key})`);
      const pywebviewWindow = window as unknown as PyWebViewWindow;
      // Ensure pywebview and api are available before calling get_item
      const result = await pywebviewWindow.pywebview?.api?.get_item(key);
      console.log(`StorageBridge.getItem(${key}) result:`, result);
      // Explicitly return null if result is undefined
      return result === undefined ? null : result;
    } catch (error) {
      console.error('Error in getItem:', error);
      return null;
    }
  }

  /**
   * Get an item synchronously (falls back to localStorage in browser)
   * This is mainly for backward compatibility
   */
  getItemSync(key: string): string | null {
    if (!this.isPyWebView) {
      // In browser mode, use regular localStorage
      return localStorage.getItem(key);
    }
    
    console.warn('getItemSync called in PyWebView mode - this is not reliable');
    // Since we can't do sync API calls, we'll return null and queue up an async operation
    this.pendingOperations.push(async () => {
      try {
        const pywebviewWindow = window as unknown as PyWebViewWindow;
        await pywebviewWindow.pywebview?.api.get_item(key);
      } catch (error) {
        console.error('Error in queued getItem:', error);
      }
    });
    
    return null;
  }

  /**
   * Set an item in storage
   */
  async setItem(key: string, value: string): Promise<void> {
    // Ensure bridge is initialized
    await this.initialize();
    
    if (!this.isPyWebView) {
      // In browser mode, use regular localStorage
      localStorage.setItem(key, value);
      return;
    }
    
    // Use the Python backend to set the item
    try {
      console.log(`StorageBridge.setItem(${key})`);
      const pywebviewWindow = window as unknown as PyWebViewWindow;
      const result = await pywebviewWindow.pywebview?.api.set_item(key, value);
      console.log(`StorageBridge.setItem(${key}) result:`, result);
    } catch (error) {
      console.error('Error in setItem:', error);
    }
  }

  /**
   * Set an item synchronously (falls back to localStorage in browser)
   */
  setItemSync(key: string, value: string): void {
    if (!this.isPyWebView) {
      // In browser mode, use regular localStorage
      localStorage.setItem(key, value);
      return;
    }
    
    console.warn('setItemSync called in PyWebView mode - async operation queued');
    // Queue up an async operation
    this.pendingOperations.push(async () => {
      try {
        const pywebviewWindow = window as unknown as PyWebViewWindow;
        await pywebviewWindow.pywebview?.api.set_item(key, value);
      } catch (error) {
        console.error('Error in queued setItem:', error);
      }
    });
  }

  /**
   * Remove an item from storage
   */
  async removeItem(key: string): Promise<void> {
    // Ensure bridge is initialized
    await this.initialize();
    
    if (!this.isPyWebView) {
      // In browser mode, use regular localStorage
      localStorage.removeItem(key);
      return;
    }
    
    // Use the Python backend to remove the item
    try {
      console.log(`StorageBridge.removeItem(${key})`);
      const pywebviewWindow = window as unknown as PyWebViewWindow;
      const result = await pywebviewWindow.pywebview?.api.remove_item(key);
      console.log(`StorageBridge.removeItem(${key}) result:`, result);
    } catch (error) {
      console.error('Error in removeItem:', error);
    }
  }

  /**
   * Get all keys in storage
   */
  async getAllKeys(): Promise<string[]> {
    // Ensure bridge is initialized
    await this.initialize();
    
    if (!this.isPyWebView) {
      // In browser mode, use regular localStorage
      return Object.keys(localStorage);
    }
    
    // Use the Python backend to get all keys
    try {
      console.log('StorageBridge.getAllKeys()');
      const pywebviewWindow = window as unknown as PyWebViewWindow;
      const keysJson = await pywebviewWindow.pywebview?.api.get_all_keys();
      const keys = keysJson ? JSON.parse(keysJson) : [];
      console.log('StorageBridge.getAllKeys() result:', keys);
      return keys;
    } catch (error) {
      console.error('Error in getAllKeys:', error);
      return [];
    }
  }
  
  /**
   * Dump all storage contents (for debugging)
   */
  async dumpStorage(): Promise<Record<string, string>> {
    // Ensure bridge is initialized
    await this.initialize();
    
    if (!this.isPyWebView) {
      // In browser mode, use regular localStorage
      const result: Record<string, string> = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          result[key] = localStorage.getItem(key) || '';
        }
      }
      return result;
    }
    
    // Use the Python backend
    try {
      console.log('StorageBridge.dumpStorage()');
      const pywebviewWindow = window as unknown as PyWebViewWindow;
      const storageJson = await pywebviewWindow.pywebview?.api.dump_storage();
      const storage = storageJson ? JSON.parse(storageJson) : {};
      console.log('StorageBridge.dumpStorage() result:', storage);
      return storage;
    } catch (error) {
      console.error('Error in dumpStorage:', error);
      return {};
    }
  }
}

// Create a singleton instance
const storageBridge = new StorageBridge();

// Initialize the bridge immediately
if (typeof window !== 'undefined') {
  storageBridge.initialize().then(isPyWebView => {
    console.log('StorageBridge initialized, isPyWebView:', isPyWebView);
  });
}

// Export the bridge
export default storageBridge;
