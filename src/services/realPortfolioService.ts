/**
 * Real Portfolio Service - Integrates portfolio management with live EGX stock data
 */

import { Portfolio, StockHolding, PortfolioSummary } from '../types/investment';
import { stockDataService } from './stockDataService';

export interface PortfolioTransaction {
  portfolio_id: string;
  symbol: string;
  shares: number;
  price: number;
  transaction_type: 'buy' | 'sell';
  fees?: number;
  notes?: string;
}

class RealPortfolioService {
  private portfolios: Map<string, Portfolio> = new Map();
  private holdings: Map<string, StockHolding[]> = new Map(); // portfolio_id -> holdings[]

  constructor() {
    this.loadFromStorage();
    console.log('💼 Real Portfolio Service initialized');
  }

  /**
   * Load portfolios and holdings from localStorage
   */
  private loadFromStorage() {
    try {
      // Load portfolios
      const portfoliosData = localStorage.getItem('real_portfolios');
      if (portfoliosData) {
        const portfolioArray = JSON.parse(portfoliosData);
        portfolioArray.forEach((p: Portfolio) => this.portfolios.set(p.id, p));
      }

      // Load holdings
      const holdingsData = localStorage.getItem('real_portfolio_holdings');
      if (holdingsData) {
        const holdingsMap = JSON.parse(holdingsData);
        Object.entries(holdingsMap).forEach(([portfolioId, holdings]) => {
          this.holdings.set(portfolioId, holdings as StockHolding[]);
        });
      }

      console.log(`📂 Loaded ${this.portfolios.size} portfolios and holdings for ${this.holdings.size} portfolios`);
    } catch (error) {
      console.error('❌ Error loading portfolio data:', error);
    }
  }

  /**
   * Save portfolios and holdings to localStorage
   */
  private saveToStorage() {
    try {
      // Save portfolios
      const portfolioArray = Array.from(this.portfolios.values());
      localStorage.setItem('real_portfolios', JSON.stringify(portfolioArray));

      // Save holdings
      const holdingsObject = Object.fromEntries(this.holdings);
      localStorage.setItem('real_portfolio_holdings', JSON.stringify(holdingsObject));

      console.log('💾 Portfolio data saved to storage');
    } catch (error) {
      console.error('❌ Error saving portfolio data:', error);
    }
  }

  /**
   * Create a new portfolio
   */
  async createPortfolio(data: Omit<Portfolio, 'id' | 'user_id' | 'created_at' | 'updated_at' | 'current_balance'>): Promise<Portfolio> {
    const portfolio: Portfolio = {
      ...data,
      id: `portfolio_${Date.now()}`,
      user_id: 'current_user',
      current_balance: data.initial_balance, // Will be calculated from holdings
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    this.portfolios.set(portfolio.id, portfolio);
    this.holdings.set(portfolio.id, []); // Initialize empty holdings
    this.saveToStorage();

    console.log('✅ Created new portfolio:', portfolio.name);
    return portfolio;
  }

  /**
   * Get all portfolios with calculated current values
   */
  async getPortfolios(): Promise<Portfolio[]> {
    const portfoliosWithUpdatedValues = [];

    for (const portfolio of this.portfolios.values()) {
      const summary = await this.getPortfolioSummary(portfolio.id);
      const updatedPortfolio = {
        ...portfolio,
        current_balance: summary.current_value,
        updated_at: new Date().toISOString()
      };
      portfoliosWithUpdatedValues.push(updatedPortfolio);
    }

    return portfoliosWithUpdatedValues;
  }

  /**
   * Get detailed portfolio summary with live stock data
   */
  async getPortfolioSummary(portfolioId: string): Promise<PortfolioSummary> {
    const portfolio = this.portfolios.get(portfolioId);
    if (!portfolio) {
      throw new Error(`Portfolio ${portfolioId} not found`);
    }

    const holdings = this.holdings.get(portfolioId) || [];
    const updatedHoldings: StockHolding[] = [];

    let totalInvested = 0;
    let currentValue = 0;

    // Update each holding with current stock prices
    for (const holding of holdings) {
      try {
        // Get current stock price from our live data service
        const quote = await stockDataService.getStockQuote(holding.symbol);
        const currentPrice = quote?.price || holding.purchase_price;

        const holdingCurrentValue = holding.shares * currentPrice;
        const unrealizedGainLoss = holdingCurrentValue - holding.total_cost;
        const unrealizedGainLossPercentage = (unrealizedGainLoss / holding.total_cost) * 100;

        const updatedHolding: StockHolding = {
          ...holding,
          current_price: currentPrice,
          current_value: holdingCurrentValue,
          unrealized_gain_loss: unrealizedGainLoss,
          unrealized_gain_loss_percentage: unrealizedGainLossPercentage,
          updated_at: new Date().toISOString()
        };

        updatedHoldings.push(updatedHolding);
        totalInvested += holding.total_cost;
        currentValue += holdingCurrentValue;

      } catch (error) {
        console.warn(`⚠️ Could not update price for ${holding.symbol}:`, error);
        // Use last known values
        updatedHoldings.push(holding);
        totalInvested += holding.total_cost;
        currentValue += holding.current_value || holding.total_cost;
      }
    }

    const totalGainLoss = currentValue - totalInvested;
    const totalGainLossPercentage = totalInvested > 0 ? (totalGainLoss / totalInvested) * 100 : 0;

    // Find best and worst performers
    const performers = updatedHoldings
      .filter(h => h.unrealized_gain_loss_percentage !== undefined)
      .sort((a, b) => (b.unrealized_gain_loss_percentage || 0) - (a.unrealized_gain_loss_percentage || 0));

    const bestPerformer = performers[0];
    const worstPerformer = performers[performers.length - 1];

    // Calculate sector allocation (simplified - could be enhanced with real sector data)
    const sectorAllocation = this.calculateSectorAllocation(updatedHoldings);

    return {
      portfolio,
      holdings: updatedHoldings,
      total_invested: totalInvested,
      current_value: currentValue,
      total_gain_loss: totalGainLoss,
      total_gain_loss_percentage: totalGainLossPercentage,
      best_performer: bestPerformer,
      worst_performer: worstPerformer,
      sector_allocation: sectorAllocation
    };
  }

  /**
   * Add stock to portfolio (buy transaction)
   */
  async buyStock(transaction: PortfolioTransaction): Promise<StockHolding> {
    const { portfolio_id, symbol, shares, price, fees = 0, notes } = transaction;

    if (!this.portfolios.has(portfolio_id)) {
      throw new Error(`Portfolio ${portfolio_id} not found`);
    }

    // Get stock info
    const quote = await stockDataService.getStockQuote(symbol);
    const companyName = quote?.symbol || symbol; // Could be enhanced with company names

    const totalCost = (shares * price) + fees;

    const holding: StockHolding = {
      id: `holding_${Date.now()}`,
      portfolio_id,
      symbol,
      company_name: companyName,
      shares,
      purchase_price: price,
      purchase_date: new Date().toISOString(),
      average_cost: price, // For first purchase, same as purchase_price
      total_cost: totalCost,
      fees,
      notes,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add to existing holdings or create new array
    const existingHoldings = this.holdings.get(portfolio_id) || [];
    existingHoldings.push(holding);
    this.holdings.set(portfolio_id, existingHoldings);

    this.saveToStorage();

    console.log(`✅ Added ${shares} shares of ${symbol} to portfolio ${portfolio_id}`);
    return holding;
  }

  /**
   * Calculate sector allocation (simplified implementation)
   */
  private calculateSectorAllocation(holdings: StockHolding[]): { sector: string; value: number; percentage: number }[] {
    // Simplified sector mapping - could be enhanced with real sector data
    const sectorMap: { [symbol: string]: string } = {
      'COMI': 'Banking',
      'ABUK': 'Banking',
      'VALU': 'Financial Services',
      'ETEL': 'Telecommunications',
      'SWDY': 'Industrial',
      'FWRY': 'Fintech',
      'PHDC': 'Real Estate',
      'ORAS': 'Construction',
      'ISPH': 'Pharmaceuticals',
      'HRHO': 'Financial Services'
    };

    const sectorTotals: { [sector: string]: number } = {};
    let totalValue = 0;

    holdings.forEach(holding => {
      const sector = sectorMap[holding.symbol] || 'Other';
      const value = holding.current_value || holding.total_cost;
      sectorTotals[sector] = (sectorTotals[sector] || 0) + value;
      totalValue += value;
    });

    return Object.entries(sectorTotals).map(([sector, value]) => ({
      sector,
      value,
      percentage: totalValue > 0 ? (value / totalValue) * 100 : 0
    })).sort((a, b) => b.value - a.value);
  }

  /**
   * Delete a portfolio
   */
  async deletePortfolio(portfolioId: string): Promise<void> {
    this.portfolios.delete(portfolioId);
    this.holdings.delete(portfolioId);
    this.saveToStorage();
    console.log(`🗑️ Deleted portfolio ${portfolioId}`);
  }

  /**
   * Get portfolio by ID
   */
  async getPortfolio(portfolioId: string): Promise<Portfolio | null> {
    return this.portfolios.get(portfolioId) || null;
  }
}

export const realPortfolioService = new RealPortfolioService();
