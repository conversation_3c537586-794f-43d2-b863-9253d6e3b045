/**
 * Investment Data Manager
 * Integrates with the existing data_storage.py system for persistent investment data
 */

import { Portfolio, StockHolding, TradingTransaction } from '../types/investment';

interface InvestmentPyWebViewAPI {
  call_python_function: (func: string, args: any[]) => Promise<any>;
}

declare global {
  interface Window {
    investmentAPI?: InvestmentPyWebViewAPI;
  }
}

class InvestmentDataManager {
  private isDesktopApp: boolean;

  constructor() {
    this.isDesktopApp = !!window.pywebview || !!window.investmentAPI;
    console.log(`💼 Investment Data Manager initialized (${this.isDesktopApp ? 'Desktop' : 'Web'} mode)`);
  }

  /**
   * Call Python backend function
   */
  private async callPython(functionName: string, args: any[] = []): Promise<any> {
    if (!this.isDesktopApp) {
      console.warn('Desktop app not detected, using local storage fallback');
      return this.handleWebFallback(functionName, args);
    }

    try {
      // Check if PyWebView API is available
      if (window.pywebview?.api) {
        console.log(`🐍 Calling Python function: ${functionName} with args:`, args);
        
        // Call the appropriate Python method from DataStorage
        let result;
        switch (functionName) {
          case 'save_investment_portfolio':
            result = await window.pywebview.api.save_investment_portfolio(args[0]);
            break;
          case 'get_investment_portfolios':
            result = await window.pywebview.api.get_investment_portfolios();
            break;
          case 'save_stock_holding':
            result = await window.pywebview.api.save_stock_holding(args[0]);
            break;
          case 'get_portfolio_holdings':
            result = await window.pywebview.api.get_portfolio_holdings(args[0]);
            break;
          case 'update_stock_holding':
            result = await window.pywebview.api.update_stock_holding(args[0], args[1]);
            break;
          case 'delete_stock_holding':
            result = await window.pywebview.api.delete_stock_holding(args[0]);
            break;
          default:
            throw new Error(`Unknown function: ${functionName}`);
        }
        
        console.log(`🐍 Python function ${functionName} result:`, result);
        
        // Parse JSON result if it's a string
        if (typeof result === 'string') {
          try {
            return JSON.parse(result);
          } catch (e) {
            console.warn('Failed to parse Python result as JSON:', result);
            return { success: false, error: 'Invalid response format' };
          }
        }
        
        return result;
      }
      
      throw new Error('PyWebView API not available');
    } catch (error) {
      console.error(`❌ Error calling Python function ${functionName}:`, error);
      return this.handleWebFallback(functionName, args);
    }
  }

  /**
   * Fallback to localStorage for web version
   */
  private handleWebFallback(functionName: string, args: any[]): any {
    console.log(`📱 Web fallback for function: ${functionName}`, args);
    
    switch (functionName) {
      case 'get_investment_portfolios':
        const portfolios = JSON.parse(localStorage.getItem('investment_portfolios') || '[]');
        console.log('📂 Retrieved portfolios from localStorage:', portfolios);
        return portfolios;
        
      case 'save_investment_portfolio':
        const existingPortfolios = JSON.parse(localStorage.getItem('investment_portfolios') || '[]');
        const newPortfolio = args[0];
        newPortfolio.id = Date.now().toString(); // Ensure unique ID
        existingPortfolios.push(newPortfolio);
        localStorage.setItem('investment_portfolios', JSON.stringify(existingPortfolios));
        console.log('💾 Saved portfolio to localStorage:', newPortfolio);
        return { success: true, id: newPortfolio.id };
        
      case 'update_investment_portfolio':
        const allPortfolios = JSON.parse(localStorage.getItem('investment_portfolios') || '[]');
        const portfolioId = args[0];
        const updates = args[1];
        const portfolioIndex = allPortfolios.findIndex((p: Portfolio) => p.id === portfolioId);
        if (portfolioIndex >= 0) {
          allPortfolios[portfolioIndex] = { ...allPortfolios[portfolioIndex], ...updates, updated_at: new Date().toISOString() };
          localStorage.setItem('investment_portfolios', JSON.stringify(allPortfolios));
          console.log('📝 Updated portfolio in localStorage:', allPortfolios[portfolioIndex]);
          return { success: true };
        }
        return { success: false, error: 'Portfolio not found' };
        
      case 'delete_investment_portfolio':
        const portfoliosList = JSON.parse(localStorage.getItem('investment_portfolios') || '[]');
        const idToDelete = args[0];
        const filteredPortfolios = portfoliosList.filter((p: Portfolio) => p.id !== idToDelete);
        localStorage.setItem('investment_portfolios', JSON.stringify(filteredPortfolios));
        console.log('🗑️ Deleted portfolio from localStorage:', idToDelete);
        return { success: true };
        
      case 'save_stock_holding':
        const holdings = JSON.parse(localStorage.getItem('investment_stock_holdings') || '[]');
        const newHolding = args[0];
        newHolding.id = Date.now().toString();
        holdings.push(newHolding);
        localStorage.setItem('investment_stock_holdings', JSON.stringify(holdings));
        console.log('📊 Saved stock holding to localStorage:', newHolding);
        return { success: true, id: newHolding.id };
        
      case 'get_portfolio_holdings':
        const holdingsPortfolioId = args[0];
        const allHoldings = JSON.parse(localStorage.getItem('investment_stock_holdings') || '[]');
        const portfolioHoldings = allHoldings.filter((h: any) => h.portfolio_id === holdingsPortfolioId);
        console.log(`📊 Retrieved ${portfolioHoldings.length} holdings for portfolio ${holdingsPortfolioId}`);
        return portfolioHoldings;
        
      case 'update_stock_holding':
        const holdingId = args[0];
        const holdingUpdates = args[1];
        const existingHoldings = JSON.parse(localStorage.getItem('investment_stock_holdings') || '[]');
        const holdingIndex = existingHoldings.findIndex((h: any) => h.id === holdingId);
        if (holdingIndex >= 0) {
          existingHoldings[holdingIndex] = { ...existingHoldings[holdingIndex], ...holdingUpdates, updated_at: new Date().toISOString() };
          localStorage.setItem('investment_stock_holdings', JSON.stringify(existingHoldings));
          console.log('📝 Updated holding in localStorage:', existingHoldings[holdingIndex]);
          return { success: true, holding: existingHoldings[holdingIndex] };
        }
        return { success: false, error: 'Holding not found' };
        
      case 'delete_stock_holding':
        const idToDeleteHolding = args[0];
        const holdingsList = JSON.parse(localStorage.getItem('investment_stock_holdings') || '[]');
        const filteredHoldings = holdingsList.filter((h: any) => h.id !== idToDeleteHolding);
        localStorage.setItem('investment_stock_holdings', JSON.stringify(filteredHoldings));
        console.log('🗑️ Deleted holding from localStorage:', idToDeleteHolding);
        return { success: true };
        
      default:
        console.warn(`⚠️ Unknown fallback function: ${functionName}`);
        return { success: false, error: 'Function not implemented' };
    }
  }

  // PORTFOLIO MANAGEMENT

  /**
   * Get all portfolios for the current user
   */
  async getPortfolios(): Promise<Portfolio[]> {
    try {
      const result = await this.callPython('get_investment_portfolios');
      return result || [];
    } catch (error) {
      console.error('Error fetching portfolios:', error);
      return [];
    }
  }

  /**
   * Create a new portfolio
   */
  async createPortfolio(portfolio: Omit<Portfolio, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<Portfolio> {
    console.log('🆕 Creating new portfolio:', portfolio);
    try {
      const newPortfolio: Portfolio = {
        ...portfolio,
        id: Date.now().toString(),
        user_id: 'current_user',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('📤 Calling Python to save portfolio:', newPortfolio);
      const result = await this.callPython('save_investment_portfolio', [newPortfolio]);
      console.log('📥 Python save result:', result);
      
      if (result?.success) {
        const savedPortfolio = { ...newPortfolio, id: result.id || newPortfolio.id };
        console.log('✅ Portfolio created successfully:', savedPortfolio);
        return savedPortfolio;
      }
      
      throw new Error(`Failed to save portfolio: ${result?.error || 'Unknown error'}`);
    } catch (error) {
      console.error('❌ Error creating portfolio:', error);
      throw error;
    }
  }

  /**
   * Update an existing portfolio
   */
  async updatePortfolio(portfolioId: string, updates: Partial<Portfolio>): Promise<Portfolio> {
    try {
      const updatedPortfolio = {
        ...updates,
        id: portfolioId,
        updated_at: new Date().toISOString()
      };

      const result = await this.callPython('update_investment_portfolio', [portfolioId, updatedPortfolio]);
      
      if (result?.success) {
        return result.portfolio;
      }
      
      throw new Error('Failed to update portfolio');
    } catch (error) {
      console.error('Error updating portfolio:', error);
      throw error;
    }
  }

  /**
   * Delete a portfolio
   */
  async deletePortfolio(portfolioId: string): Promise<boolean> {
    try {
      const result = await this.callPython('delete_investment_portfolio', [portfolioId]);
      return result?.success || false;
    } catch (error) {
      console.error('Error deleting portfolio:', error);
      return false;
    }
  }

  // STOCK HOLDINGS MANAGEMENT

  /**
   * Get all holdings for a portfolio
   */
  async getPortfolioHoldings(portfolioId: string): Promise<StockHolding[]> {
    try {
      const result = await this.callPython('get_portfolio_holdings', [portfolioId]);
      return result || [];
    } catch (error) {
      console.error('Error fetching holdings:', error);
      return [];
    }
  }

  /**
   * Add a stock purchase to portfolio
   */
  async addStockHolding(holding: {
    portfolio_id: string;
    symbol: string;
    shares: number;
    purchase_price: number;
    purchase_date?: string;
    company_name?: string;
    fees?: number;
  }): Promise<StockHolding> {
    try {
      const newHolding: StockHolding = {
        id: Date.now().toString(),
        user_id: 'current_user',
        portfolio_id: holding.portfolio_id,
        symbol: holding.symbol,
        company_name: holding.company_name || holding.symbol, // Use symbol as fallback
        shares: holding.shares,
        purchase_price: holding.purchase_price,
        purchase_date: holding.purchase_date || new Date().toISOString(),
        average_cost: holding.purchase_price,
        total_cost: holding.shares * holding.purchase_price,
        current_price: 0, // Will be updated by stock service
        current_value: 0, // Will be calculated
        unrealized_gain_loss: 0, // Will be calculated
        fees: holding.fees || 0,
        created_at: holding.purchase_date || new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const result = await this.callPython('save_stock_holding', [newHolding]);
      
      if (result?.success) {
        return { ...newHolding, id: result.id || newHolding.id };
      }
      
      throw new Error('Failed to save stock holding');
    } catch (error) {
      console.error('Error adding stock holding:', error);
      throw error;
    }
  }

  /**
   * Update stock holding (for price updates, additional purchases, etc.)
   */
  async updateStockHolding(holdingId: string, updates: Partial<StockHolding>): Promise<StockHolding> {
    try {
      const result = await this.callPython('update_stock_holding', [holdingId, {
        ...updates,
        updated_at: new Date().toISOString()
      }]);
      
      if (result?.success) {
        return result.holding;
      }
      
      throw new Error('Failed to update stock holding');
    } catch (error) {
      console.error('Error updating stock holding:', error);
      throw error;
    }
  }

  /**
   * Remove/sell stock holding
   */
  async removeStockHolding(holdingId: string): Promise<boolean> {
    try {
      const result = await this.callPython('delete_stock_holding', [holdingId]);
      return result?.success || false;
    } catch (error) {
      console.error('Error removing stock holding:', error);
      return false;
    }
  }

  // TRANSACTION HISTORY

  /**
   * Get trading transaction history
   */
  async getTransactionHistory(portfolioId?: string): Promise<TradingTransaction[]> {
    try {
      const result = await this.callPython('get_trading_transactions', [portfolioId]);
      return result || [];
    } catch (error) {
      console.error('Error fetching transaction history:', error);
      return [];
    }
  }

  /**
   * Record a new trading transaction
   */
  async recordTransaction(transaction: {
    portfolio_id: string;
    symbol: string;
    transaction_type: 'buy' | 'sell';
    shares: number;
    price: number;
    fees?: number;
    notes?: string;
  }): Promise<TradingTransaction> {
    try {
      const newTransaction: TradingTransaction = {
        id: Date.now().toString(),
        user_id: 'current_user',
        portfolio_id: transaction.portfolio_id,
        symbol: transaction.symbol,
        transaction_type: transaction.transaction_type,
        shares: transaction.shares,
        price: transaction.price,
        total_amount: transaction.shares * transaction.price,
        fees: transaction.fees || 0,
        notes: transaction.notes,
        transaction_date: new Date().toISOString(),
        created_at: new Date().toISOString()
      };

      const result = await this.callPython('save_trading_transaction', [newTransaction]);
      
      if (result?.success) {
        return { ...newTransaction, id: result.id || newTransaction.id };
      }
      
      throw new Error('Failed to save transaction');
    } catch (error) {
      console.error('Error recording transaction:', error);
      throw error;
    }
  }

  // WATCHLIST MANAGEMENT

  /**
   * Get user's watchlist
   */
  async getWatchlist(): Promise<string[]> {
    try {
      const result = await this.callPython('get_investment_watchlist');
      return result || [];
    } catch (error) {
      console.error('Error fetching watchlist:', error);
      return [];
    }
  }

  /**
   * Add symbol to watchlist
   */
  async addToWatchlist(symbol: string): Promise<boolean> {
    try {
      const result = await this.callPython('add_to_watchlist', [symbol]);
      return result?.success || false;
    } catch (error) {
      console.error('Error adding to watchlist:', error);
      return false;
    }
  }

  /**
   * Remove symbol from watchlist
   */
  async removeFromWatchlist(symbol: string): Promise<boolean> {
    try {
      const result = await this.callPython('remove_from_watchlist', [symbol]);
      return result?.success || false;
    } catch (error) {
      console.error('Error removing from watchlist:', error);
      return false;
    }
  }

  // PORTFOLIO ANALYTICS

  /**
   * Calculate portfolio performance metrics
   */
  async getPortfolioAnalytics(portfolioId: string): Promise<{
    total_value: number;
    total_cost: number;
    total_gain_loss: number;
    gain_loss_percentage: number;
    top_performers: Array<{ symbol: string; gain_loss_percentage: number }>;
    allocation: Array<{ symbol: string; percentage: number; value: number }>;
  }> {
    try {
      const result = await this.callPython('calculate_portfolio_analytics', [portfolioId]);
      return result || {
        total_value: 0,
        total_cost: 0,
        total_gain_loss: 0,
        gain_loss_percentage: 0,
        top_performers: [],
        allocation: []
      };
    } catch (error) {
      console.error('Error calculating portfolio analytics:', error);
      return {
        total_value: 0,
        total_cost: 0,
        total_gain_loss: 0,
        gain_loss_percentage: 0,
        top_performers: [],
        allocation: []
      };
    }
  }
}

// Export singleton instance
export const investmentDataManager = new InvestmentDataManager();
