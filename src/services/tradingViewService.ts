/**
 * TradingView Real-time Data Service
 * Extracted and adapted from TradingView-API for Financial Advisor integration
 */

import { StockQuote } from '../types/investment';

// Egyptian Exchange stock symbols
export const EGX_SYMBOLS = [
  { symbol: 'EGX:COMI', name: 'Commercial International Bank', type: 'bank' },
  { symbol: 'EGX:SWDY', name: 'El Sewedy Electric', type: 'industrial' },
  { symbol: 'EGX:FWRY', name: 'Fawry Banking Technology', type: 'fintech' },
  { symbol: 'EGX:PHDC', name: 'Palm Hills Development', type: 'real_estate' },
  { symbol: 'EGX:ORAS', name: 'Orascom Construction', type: 'construction' },
  { symbol: 'EGX:ISPH', name: 'Ibn Sina Pharma', type: 'pharma' },
  { symbol: 'EGX:HRHO', name: 'EFG Holding', type: 'financial' },
  { symbol: 'EGX:TALAAT', name: 'Talaat Moustafa Group', type: 'real_estate' },
  { symbol: 'EGX:ETEL', name: 'Egyptian Telecommunications', type: 'telecom' },
  { symbol: 'EGX:EKHO', name: 'El Kahera Housing', type: 'real_estate' }
];

interface TradingViewStockData {
  symbol: string;
  name: string;
  type: string;
  price: number;
  open: number;
  high: number;
  low: number;
  volume: number;
  change: number;
  changePercent: number;
  timestamp: string;
}

class TradingViewService {
  private isInitialized: boolean = false;
  private stockData: Map<string, TradingViewStockData> = new Map();
  private updateCallbacks: Set<(data: TradingViewStockData) => void> = new Set();
  private serverUrl: string = 'http://localhost:3000'; // Default TradingView server URL
  private reconnectInterval: NodeJS.Timeout | null = null;
  private isConnected: boolean = false;

  constructor() {
    console.log('📊 TradingView Service initialized');
    // Auto-initialize when service is created
    this.initialize().catch(error => {
      console.warn('⚠️ TradingView auto-initialization failed:', error);
    });
  }

  /**
   * Configure the TradingView server URL
   */
  public setServerUrl(url: string) {
    this.serverUrl = url;
    console.log(`🔧 TradingView server URL set to: ${url}`);
  }

  /**
   * Check if TradingView server is available
   */
  public async checkServerAvailability(): Promise<boolean> {
    console.log(`🔍 Checking TradingView server at: ${this.serverUrl}`);
    try {
      // Create a timeout controller for the request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch(`${this.serverUrl}/api/stocks`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });
      
      clearTimeout(timeoutId);
      this.isConnected = response.ok;
      console.log(`📡 TradingView server check result: ${response.ok ? 'Connected ✅' : 'Failed ❌'} (Status: ${response.status})`);
      return response.ok;
    } catch (error) {
      console.warn('⚠️ TradingView server not available:', error);
      this.isConnected = false;
      return false;
    }
  }

  /**
   * Initialize connection to TradingView server
   */
  public async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    console.log('🚀 Initializing TradingView connection...');

    const isAvailable = await this.checkServerAvailability();
    
    if (isAvailable) {
      console.log('✅ TradingView server is available');
      this.setupWebSocketConnection();
      this.isInitialized = true;
      return true;
    } else {
      console.log('❌ TradingView server not available - using fallback data');
      this.generateFallbackData();
      return false;
    }
  }

  /**
   * Setup WebSocket connection for real-time updates
   */
  private setupWebSocketConnection() {
    try {
      // Note: In a real implementation, we would use Socket.IO client
      // For now, we'll poll the REST API for updates
      this.startPollingUpdates();
    } catch (error) {
      console.error('❌ WebSocket connection failed:', error);
      this.generateFallbackData();
    }
  }

  /**
   * Start polling for stock updates
   */
  private startPollingUpdates() {
    const pollInterval = setInterval(async () => {
      if (!this.isConnected) {
        const available = await this.checkServerAvailability();
        if (!available) return;
      }

      try {
        const response = await fetch(`${this.serverUrl}/api/stocks`);
        if (response.ok) {
          const stocks = await response.json();
          this.processStockUpdates(stocks);
        }
      } catch (error) {
        console.warn('⚠️ Failed to fetch stock updates:', error);
        this.isConnected = false;
      }
    }, 5000); // Poll every 5 seconds

    // Store interval for cleanup
    this.reconnectInterval = pollInterval;
  }

  /**
   * Process stock updates from TradingView server
   */
  private processStockUpdates(stocks: any[]) {
    stocks.forEach(stock => {
      const stockData: TradingViewStockData = {
        symbol: this.normalizeSymbol(stock.symbol),
        name: stock.name,
        type: stock.type || 'unknown',
        price: stock.price || 0,
        open: stock.open || 0,
        high: stock.high || 0,
        low: stock.low || 0,
        volume: stock.volume || 0,
        change: stock.change || 0,
        changePercent: stock.changePercent || 0,
        timestamp: stock.timestamp || new Date().toISOString()
      };

      this.stockData.set(stockData.symbol, stockData);
      
      // Notify callbacks
      this.updateCallbacks.forEach(callback => {
        try {
          callback(stockData);
        } catch (error) {
          console.error('Error in update callback:', error);
        }
      });
    });
  }

  /**
   * Generate fallback data when TradingView server is not available
   */
  private generateFallbackData() {
    console.log('📊 Generating fallback stock data...');
    
    EGX_SYMBOLS.forEach(stock => {
      const basePrice = this.getBasePriceForSymbol(stock.symbol);
      const randomChange = (Math.random() - 0.5) * basePrice * 0.03; // ±3% change
      const currentPrice = basePrice + randomChange;
      
      const stockData: TradingViewStockData = {
        symbol: this.normalizeSymbol(stock.symbol),
        name: stock.name,
        type: stock.type,
        price: currentPrice,
        open: basePrice,
        high: currentPrice + Math.abs(randomChange) * 0.5,
        low: currentPrice - Math.abs(randomChange) * 0.5,
        volume: Math.floor(Math.random() * 1000000) + 100000,
        change: randomChange,
        changePercent: (randomChange / basePrice) * 100,
        timestamp: new Date().toISOString()
      };
      
      this.stockData.set(stockData.symbol, stockData);
    });

    console.log(`📊 Generated fallback data for ${EGX_SYMBOLS.length} stocks`);
  }

  /**
   * Get stock quote in our standard format
   */
  public async getStockQuote(symbol: string): Promise<StockQuote | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const normalizedSymbol = this.normalizeSymbol(symbol);
    const stockData = this.stockData.get(normalizedSymbol);
    
    if (stockData) {
      return {
        symbol: normalizedSymbol,
        price: stockData.price,
        change: stockData.change,
        change_percentage: stockData.changePercent,
        volume: stockData.volume,
        last_updated: stockData.timestamp
      };
    }

    // Try to fetch from server if not in cache
    if (this.isConnected) {
      try {
        const response = await fetch(`${this.serverUrl}/api/stocks/${symbol}`);
        if (response.ok) {
          const stock = await response.json();
          return {
            symbol: this.normalizeSymbol(stock.symbol),
            price: stock.price || 0,
            change: stock.change || 0,
            change_percentage: stock.changePercent || 0,
            volume: stock.volume || 0,
            last_updated: stock.timestamp || new Date().toISOString()
          };
        }
      } catch (error) {
        console.warn(`⚠️ Failed to fetch quote for ${symbol}:`, error);
      }
    }

    return null;
  }

  /**
   * Get multiple stock quotes
   */
  public async getMultipleQuotes(symbols: string[]): Promise<StockQuote[]> {
    const promises = symbols.map(symbol => this.getStockQuote(symbol));
    const results = await Promise.all(promises);
    return results.filter(quote => quote !== null) as StockQuote[];
  }

  /**
   * Subscribe to real-time updates
   */
  public onStockUpdate(callback: (data: TradingViewStockData) => void): () => void {
    this.updateCallbacks.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.updateCallbacks.delete(callback);
    };
  }

  /**
   * Get technical analysis for a symbol
   */
  public async getTechnicalAnalysis(symbol: string): Promise<any> {
    if (!this.isConnected) {
      return null;
    }

    try {
      const response = await fetch(`${this.serverUrl}/api/technical-analysis/${symbol}`);
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.warn(`⚠️ Failed to get technical analysis for ${symbol}:`, error);
    }

    return null;
  }

  /**
   * Get all available EGX stocks
   */
  public getAvailableSymbols(): string[] {
    return EGX_SYMBOLS.map(stock => this.normalizeSymbol(stock.symbol));
  }

  /**
   * Get stock info by symbol
   */
  public getStockInfo(symbol: string): TradingViewStockData | null {
    return this.stockData.get(this.normalizeSymbol(symbol)) || null;
  }

  /**
   * Check if service is connected to TradingView server
   */
  public isConnectedToServer(): boolean {
    return this.isConnected;
  }

  /**
   * Get connection status
   */
  public getConnectionStatus(): string {
    if (this.isConnected) {
      return 'Connected to TradingView server';
    } else if (this.isInitialized) {
      return 'Using fallback data (TradingView server unavailable)';
    } else {
      return 'Not initialized';
    }
  }

  /**
   * Normalize symbol (remove EGX: prefix for internal use)
   */
  private normalizeSymbol(symbol: string): string {
    return symbol.replace('EGX:', '').toUpperCase();
  }

  /**
   * Get base price for fallback data generation
   */
  private getBasePriceForSymbol(symbol: string): number {
    const prices: Record<string, number> = {
      'EGX:COMI': 52.15,
      'EGX:SWDY': 24.70,
      'EGX:FWRY': 11.34,
      'EGX:PHDC': 8.90,
      'EGX:ORAS': 45.20,
      'EGX:ISPH': 18.75,
      'EGX:HRHO': 95.50,
      'EGX:TALAAT': 15.80,
      'EGX:ETEL': 18.30,
      'EGX:EKHO': 140.25
    };
    
    return prices[symbol] || 50.00;
  }

  /**
   * Get debug information about the service state
   */
  public getDebugInfo() {
    return {
      isInitialized: this.isInitialized,
      isConnected: this.isConnected,
      serverUrl: this.serverUrl,
      stockCount: this.stockData.size,
      stocks: Array.from(this.stockData.keys())
    };
  }

  /**
   * Cleanup resources
   */
  public cleanup() {
    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval);
      this.reconnectInterval = null;
    }
    
    this.updateCallbacks.clear();
    this.stockData.clear();
    this.isInitialized = false;
    this.isConnected = false;
    
    console.log('🧹 TradingView service cleaned up');
  }
}

// Export singleton instance
export const tradingViewService = new TradingViewService();

// Export for integration with existing stock data service
export { TradingViewService };
