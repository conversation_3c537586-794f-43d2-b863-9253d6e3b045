/**
 * Bond Data Service
 * Handles fetching bond data from TradingView API and manages local bond data
 */

import { Bond } from '../types/investment';

// Egyptian Bond symbols available from our TradingView server
export const EGX_BOND_SYMBOLS = [
  'EGX:GOVT10Y', 'EGX:GOVT5Y', 'EGX:COMI_BOND', 'EGX:NBE_BOND', 'EGX:QNB_BOND'
];

interface TradingViewBondData {
  symbol: string;
  name: string;
  issuer: string;
  bond_type: 'government' | 'corporate';
  face_value: number;
  current_price: number;
  coupon_rate: number;
  yield_to_maturity: number;
  maturity_date: string;
  rating: string;
  price_change: number;
  price_change_percent: number;
  last_updated: string;
}

class BondDataService {
  private readonly API_BASE = 'http://localhost:3000';
  private isConnected = false;
  private connectionChecked = false;
  private bondCache: Bond[] = [];
  private lastCacheUpdate = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes for bonds

  constructor() {
    this.checkConnection();
  }

  private async checkConnection(): Promise<boolean> {
    if (this.connectionChecked && this.isConnected) {
      return true;
    }

    try {
      console.log('🔗 Checking TradingView server connection for bonds...');
      const response = await fetch(`${this.API_BASE}/health`, {
        method: 'GET',
        headers: { 'Accept': 'application/json' },
      });

      if (response.ok) {
        const data = await response.json();
        this.isConnected = true;
        this.connectionChecked = true;
        console.log(`✅ Bond service connected to TradingView server - Bonds available: ${data.bonds}`);
        return true;
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.warn('⚠️ TradingView server not available for bonds:', error);
      this.isConnected = false;
      this.connectionChecked = true;
      return false;
    }
  }

  public async getBondQuote(symbol: string): Promise<TradingViewBondData | null> {
    if (!(await this.checkConnection())) {
      return null;
    }

    try {
      console.log(`📊 Fetching bond quote for: ${symbol}`);
      const response = await fetch(`${this.API_BASE}/api/bonds/${symbol}`);
      
      if (response.ok) {
        const data: TradingViewBondData = await response.json();
        console.log(`✅ Bond quote received for ${symbol}:`, data);
        return data;
      } else if (response.status === 404) {
        console.warn(`⚠️ Bond not found: ${symbol}`);
        return null;
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error(`❌ Error fetching bond quote for ${symbol}:`, error);
      return null;
    }
  }

  public async searchBonds(query: string): Promise<Bond[]> {
    console.log(`🔍 Searching bonds for: "${query}"`);

    if (!(await this.checkConnection())) {
      console.warn('⚠️ TradingView server not available, returning empty results');
      return [];
    }

    try {
      // First try direct search endpoint
      const response = await fetch(`${this.API_BASE}/api/bonds/search/${encodeURIComponent(query)}`);
      
      if (response.ok) {
        const bondData: TradingViewBondData[] = await response.json();
        console.log(`📋 Search found ${bondData.length} bonds for "${query}"`);
        
        // Convert to Bond type
        const bonds = bondData.map(data => this.convertTradingViewToBond(data));
        return bonds;
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error(`❌ Error searching bonds:`, error);
      return [];
    }
  }

  public async getAllBonds(): Promise<Bond[]> {
    console.log('📊 Loading all available bonds...');

    // Check cache first
    const now = Date.now();
    if (this.bondCache.length > 0 && (now - this.lastCacheUpdate) < this.CACHE_DURATION) {
      console.log(`📋 Returning cached bonds (${this.bondCache.length} bonds)`);
      return this.bondCache;
    }

    if (!(await this.checkConnection())) {
      console.warn('⚠️ TradingView server not available, returning cached data or empty array');
      return this.bondCache;
    }

    try {
      const response = await fetch(`${this.API_BASE}/api/bonds`);
      
      if (response.ok) {
        const bondData: TradingViewBondData[] = await response.json();
        console.log(`📋 Loaded ${bondData.length} bonds from TradingView server`);
        
        // Convert to Bond type
        const bonds = bondData.map(data => this.convertTradingViewToBond(data));
        
        // Update cache
        this.bondCache = bonds;
        this.lastCacheUpdate = now;
        
        return bonds;
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Error loading bonds:', error);
      return this.bondCache; // Return cached data if available
    }
  }

  private convertTradingViewToBond(data: TradingViewBondData): Bond {
    return {
      id: data.symbol,
      user_id: 'system', // System bonds available to all users
      name: data.name,
      issuer: data.issuer,
      face_value: data.face_value,
      coupon_rate: data.coupon_rate,
      maturity_date: data.maturity_date,
      current_price: data.current_price,
      yield_to_maturity: data.yield_to_maturity,
      rating: data.rating,
      bond_type: data.bond_type,
      price_change: data.price_change,
      price_change_percent: data.price_change_percent,
      created_at: new Date(data.last_updated).toISOString(),
      updated_at: new Date(data.last_updated).toISOString()
    };
  }

  public getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      connectionChecked: this.connectionChecked,
      lastCacheUpdate: this.lastCacheUpdate,
      cachedBonds: this.bondCache.length
    };
  }

  public async refreshCache(): Promise<void> {
    console.log('🔄 Refreshing bond cache...');
    this.lastCacheUpdate = 0; // Force cache refresh
    await this.getAllBonds();
  }
}

// Export singleton instance
export const bondDataService = new BondDataService();
