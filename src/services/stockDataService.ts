/**
 * Stock Data Service
 * Handles fetching stock data from various sources and manages local data
 */

import { Stock, MarketOverview, StockQuote } from '../types/investment';
import { tradingViewService } from './tradingViewService';

// Egyptian Stock Exchange symbols for reference
export const EGX_SYMBOLS = [
  'COMI', 'ETEL', 'TALAAT', 'ORWE', 'HRHO', 'EKHO', 'CCAP', 'SWDY',
  'AMEN', 'ABUK', 'MCDR', 'EAST', 'EMFD', 'CLHO', 'JUFO', 'OTMT'
];

// Default mock data for demonstration (will be replaced with real data when available)
const DEFAULT_STOCKS: Stock[] = [
  {
    id: '1',
    symbol: 'COMI',
    name: 'Commercial International Bank',
    price: 52.15,
    change: 1.25,
    volume: 1250000,
    market_cap: 76*********,
    liquidity_zone: { support: 50.80, resistance: 53.40 },
    order_blocks: { bullish: [49.75, 48.90], bearish: [54.20, 55.30] },
    ai_score: 7.8,
    recommendation: 'Buy',
    analysis: {
      smcSignals: {
        choch: true,
        mss: true,
        fvg: [{ price: 51.20, direction: 'up' }, { price: 53.80, direction: 'up' }]
      },
      predictions: {
        short_term: 'Bullish momentum expected',
        medium_term: 'Strong upward trend likely',
        long_term: 'Positive outlook with growth potential'
      },
      risk_analysis: { volatility: 0.25, beta: 1.2, sharpe_ratio: 1.8 }
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

class StockDataService {
  private apiKey: string | null = null;
  private baseUrl: string = '';
  private dataSource: 'mock' | 'api' | 'manual' | 'tradingview' = 'mock';
  private userStocks: Map<string, StockQuote> = new Map();

  constructor() {
    // Check if we have API credentials configured
    this.initializeDataSource();
  }

  private initializeDataSource() {
    // Try TradingView first, then fall back to other sources
    this.dataSource = 'tradingview';
    console.log('📊 Stock Data Service initialized with TradingView integration');
    console.log('💡 Will attempt TradingView, then fallback to mock data');
  }

  /**
   * Configure API for real stock data
   */
  public configureAPI(provider: 'alphavantage' | 'yahoo' | 'finnhub', apiKey: string) {
    this.apiKey = apiKey;
    
    switch (provider) {
      case 'alphavantage':
        this.baseUrl = 'https://www.alphavantage.co/query';
        break;
      case 'yahoo':
        this.baseUrl = 'https://yahoo-finance15.p.rapidapi.com';
        break;
      case 'finnhub':
        this.baseUrl = 'https://finnhub.io/api/v1';
        break;
    }
    
    this.dataSource = 'api';
    console.log(`📊 Stock Data Service configured for ${provider}`);
  }

  /**
   * Add or update manual stock data
   */
  public addManualStock(symbol: string, data: Partial<StockQuote>) {
    const existingData = this.userStocks.get(symbol) || {
      symbol,
      price: 0,
      change: 0,
      change_percentage: 0,
      volume: 0,
      last_updated: new Date().toISOString()
    };

    this.userStocks.set(symbol, {
      ...existingData,
      ...data,
      last_updated: new Date().toISOString()
    });

    console.log(`📝 Manual data added for ${symbol}:`, data);
  }

  /**
   * Get stock quote (current price and basic info)
   */
  public async getStockQuote(symbol: string): Promise<StockQuote | null> {
    try {
      // First check if we have manual data for this symbol
      if (this.userStocks.has(symbol)) {
        return this.userStocks.get(symbol)!;
      }

      switch (this.dataSource) {
        case 'tradingview':
          // Try TradingView first, fallback to mock if not available
          try {
            const tvQuote = await tradingViewService.getStockQuote(symbol);
            if (tvQuote) {
              return tvQuote;
            }
          } catch (error) {
            console.warn(`TradingView service unavailable for ${symbol}, using mock data:`, error);
          }
          return this.getMockQuote(symbol);
        case 'api':
          return await this.fetchFromAPI(symbol);
        case 'mock':
        default:
          return this.getMockQuote(symbol);
      }
    } catch (error) {
      console.error(`Error fetching quote for ${symbol}:`, error);
      return this.getMockQuote(symbol);
    }
  }

  /**
   * Get multiple stock quotes
   */
  public async getMultipleQuotes(symbols: string[]): Promise<StockQuote[]> {
    const promises = symbols.map(symbol => this.getStockQuote(symbol));
    const results = await Promise.all(promises);
    return results.filter(quote => quote !== null) as StockQuote[];
  }

  /**
   * Search for stocks by symbol or name using TradingView server's comprehensive EGX search
   */
  public async searchStocks(query: string): Promise<Stock[]> {
    console.log(`🔍 Searching for EGX stocks with query: "${query}"`);
    
    if (this.dataSource === 'tradingview') {
      try {
        console.log('📡 Making API call to:', `http://localhost:3000/search?q=${encodeURIComponent(query)}`);
        
        // Use the TradingView server's search endpoint to find ANY EGX stock
        const response = await fetch(`http://localhost:3000/search?q=${encodeURIComponent(query)}`);
        
        console.log('📊 API Response status:', response.status, response.statusText);
        
        if (!response.ok) {
          throw new Error(`Search API failed: ${response.status}`);
        }
        
        const searchResults = await response.json();
        console.log('🎯 RAW TradingView search results:', searchResults);
        console.log('🔢 Number of results:', searchResults.length);
        
        // Log first result in detail
        if (searchResults.length > 0) {
          console.log('📝 First result details:', {
            symbol: searchResults[0].symbol,
            name: searchResults[0].name,
            price: searchResults[0].price,
            change: searchResults[0].change,
            volume: searchResults[0].volume,
            source: searchResults[0].source
          });
        }
        
        // Convert search results to Stock format
        const stocks = searchResults.map((result: any) => {
          console.log(`🔄 Converting result for ${result.symbol}:`, {
            input_price: result.price,
            input_change: result.change,
            input_volume: result.volume
          });
          
          const convertedStock = this.convertSearchResultToStock(result);
          
          console.log(`✅ Converted ${result.symbol}:`, {
            output_price: convertedStock.price,
            output_change: convertedStock.change,
            output_volume: convertedStock.volume
          });
          
          return convertedStock;
        });
        
        console.log('✅ Final converted stocks:', stocks.map((s: Stock) => ({ 
          symbol: s.symbol, 
          price: s.price, 
          change: s.change, 
          volume: s.volume 
        })));
        
        return stocks;
      } catch (error) {
        console.error('❌ TradingView search failed:', error);
        console.warn('⚠️ TradingView search failed, falling back to limited symbols:', error);
        
        // Fallback to searching through the limited available symbols
        try {
          const availableSymbols = tradingViewService.getDebugInfo().stocks || [];
          console.log('📊 Available TradingView symbols:', availableSymbols);
          
          // Filter symbols that match the query
          const matchingSymbols = availableSymbols.filter((symbol: string) => 
            symbol.toLowerCase().includes(query.toLowerCase()) ||
            this.getStockNameFromSymbol(symbol).toLowerCase().includes(query.toLowerCase())
          );
          
          console.log('🎯 Matching symbols:', matchingSymbols);
          
          // Get quotes for matching symbols and convert to Stock format
          const stockPromises = matchingSymbols.map(async (symbol: string) => {
            const quote = await tradingViewService.getStockQuote(symbol);
            if (quote) {
              return this.convertQuoteToStock(symbol, quote);
            }
            return null;
          });
          
          const stocks = await Promise.all(stockPromises);
          const validStocks = stocks.filter(stock => stock !== null) as Stock[];
          
          console.log('✅ Found stocks from TradingView fallback:', validStocks);
          return validStocks;
        } catch (fallbackError) {
          console.warn('⚠️ TradingView fallback also failed:', fallbackError);
        }
      }
    }
    
    // Final fallback to mock data search
    console.log('📋 Using mock data for search');
    const mockStocks = await this.getAllStocks();
    return mockStocks.filter(stock => 
      stock.symbol.toLowerCase().includes(query.toLowerCase()) ||
      stock.name.toLowerCase().includes(query.toLowerCase())
    );
  }

  /**
   * Get all available stocks with real data
   */
  public async getAllStocks(): Promise<Stock[]> {
    console.log('📊 Getting all available stocks...');
    
    if (this.dataSource === 'tradingview') {
      try {
        const debugInfo = tradingViewService.getDebugInfo();
        const availableSymbols = debugInfo.stocks || [];
        console.log('📈 Available symbols from TradingView:', availableSymbols);
        
        // Get real data for all available symbols
        const stockPromises = availableSymbols.map(async (symbol: string) => {
          const quote = await tradingViewService.getStockQuote(symbol);
          if (quote) {
            return this.convertQuoteToStock(symbol, quote);
          }
          return null;
        });
        
        const stocks = await Promise.all(stockPromises);
        const validStocks = stocks.filter(stock => stock !== null) as Stock[];
        
        console.log('✅ Retrieved real stocks from TradingView:', validStocks.length);
        return validStocks;
      } catch (error) {
        console.warn('⚠️ Failed to get TradingView stocks:', error);
      }
    }
    
    console.log('📋 Using default mock stocks');
    return DEFAULT_STOCKS;
  }

  /**
   * Convert a StockQuote to a Stock object
   */
  private convertQuoteToStock(symbol: string, quote: any): Stock {
    const cleanSymbol = symbol.replace('EGX:', '');
    return {
      id: cleanSymbol,
      symbol: cleanSymbol,
      name: this.getStockNameFromSymbol(symbol),
      price: quote.price || 0,
      change: quote.change || 0,
      volume: quote.volume || 0,
      market_cap: this.estimateMarketCap(quote.price || 0),
      liquidity_zone: { 
        support: (quote.price || 0) * 0.97, 
        resistance: (quote.price || 0) * 1.03 
      },
      order_blocks: { 
        bullish: [(quote.price || 0) * 0.95, (quote.price || 0) * 0.93], 
        bearish: [(quote.price || 0) * 1.05, (quote.price || 0) * 1.07] 
      },
      ai_score: this.calculateAIScore(quote.change || 0),
      recommendation: this.getRecommendation(quote.change || 0),
      analysis: {
        smcSignals: {
          choch: (quote.change || 0) > 0,
          mss: Math.abs(quote.change || 0) > 1,
          fvg: [
            { price: (quote.price || 0) * 0.98, direction: 'up' },
            { price: (quote.price || 0) * 1.02, direction: 'up' }
          ]
        },
        predictions: {
          short_term: quote.change > 0 ? 'Bullish momentum expected' : 'Bearish pressure observed',
          medium_term: Math.abs(quote.change) > 1 ? 'Strong trend likely' : 'Sideways movement expected',
          long_term: 'Data-driven analysis pending'
        },
        risk_analysis: {
          volatility: Math.abs(quote.change_percentage || 0) / 100,
          beta: 1.0,
          sharpe_ratio: 1.5
        }
      },
      created_at: quote.last_updated || new Date().toISOString(),
      updated_at: quote.last_updated || new Date().toISOString()
    };
  }

  /**
   * Convert search result from TradingView server to Stock object
   */
  private convertSearchResultToStock(result: any): Stock {
    const cleanSymbol = result.symbol;
    
    // Debug: Log the raw values and their types
    console.log(`🔄 Converting search result for ${cleanSymbol}:`, {
      raw_price: result.price,
      raw_price_type: typeof result.price,
      raw_change: result.change,
      raw_change_type: typeof result.change,
      raw_volume: result.volume,
      raw_volume_type: typeof result.volume
    });
    
    // Ensure numeric values are properly parsed
    const price = parseFloat(result.price) || 0;
    const change = parseFloat(result.change) || 0;
    const volume = parseInt(result.volume) || 0;
    
    console.log(`✅ Parsed values for ${cleanSymbol}:`, {
      parsed_price: price,
      parsed_price_type: typeof price,
      parsed_change: change,
      parsed_change_type: typeof change,
      parsed_volume: volume,
      parsed_volume_type: typeof volume
    });
    
    return {
      id: cleanSymbol,
      symbol: cleanSymbol,
      name: result.name || this.getStockNameFromSymbol(result.symbol),
      price: price,
      change: change,
      volume: volume,
      market_cap: this.estimateMarketCap(price),
      liquidity_zone: { 
        support: price * 0.97, 
        resistance: price * 1.03 
      },
      order_blocks: { 
        bullish: [price * 0.95, price * 0.93], 
        bearish: [price * 1.05, price * 1.07] 
      },
      ai_score: this.calculateAIScore(change),
      recommendation: this.getRecommendation(change),
      analysis: {
        smcSignals: {
          choch: change > 0,
          mss: Math.abs(change) > 1,
          fvg: [
            { price: price * 0.98, direction: 'up' },
            { price: price * 1.02, direction: 'up' }
          ]
        },
        predictions: {
          short_term: change > 0 ? 'Bullish momentum expected' : 'Bearish pressure observed',
          medium_term: Math.abs(change) > 1 ? 'Strong trend likely' : 'Sideways movement expected',
          long_term: 'Data-driven analysis pending'
        },
        risk_analysis: {
          volatility: Math.abs(result.changePercent || 0) / 100,
          beta: 1.0,
          sharpe_ratio: 1.5
        }
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  /**
   * Get stock name from symbol
   */
  private getStockNameFromSymbol(symbol: string): string {
    const symbolMap: Record<string, string> = {
      'EGX:COMI': 'Commercial International Bank',
      'EGX:SWDY': 'El Sewedy Electric',
      'EGX:FWRY': 'Fawry Banking Technology',
      'EGX:PHDC': 'Palm Hills Development',
      'EGX:ORAS': 'Orascom Construction',
      'EGX:ISPH': 'Ibn Sina Pharma',
      'EGX:HRHO': 'EFG Holding',
      'EGX:EGX30': 'EGX 30 Index',
      'COMI': 'Commercial International Bank',
      'SWDY': 'El Sewedy Electric',
      'FWRY': 'Fawry Banking Technology',
      'PHDC': 'Palm Hills Development',
      'ORAS': 'Orascom Construction',
      'ISPH': 'Ibn Sina Pharma',
      'HRHO': 'EFG Holding',
      'EGX30': 'EGX 30 Index'
    };
    
    return symbolMap[symbol] || symbolMap[symbol.replace('EGX:', '')] || symbol;
  }

  /**
   * Estimate market cap based on price
   */
  private estimateMarketCap(price: number): number {
    // Rough estimation based on price ranges for EGX stocks
    if (price > 100) return price * *********0; // Large cap
    if (price > 50) return price * *********;   // Mid cap
    return price * *********;                   // Small cap
  }

  /**
   * Calculate AI score based on price change
   */
  private calculateAIScore(change: number): number {
    const baseScore = 5.0;
    const changeScore = Math.min(Math.max(change * 0.5, -2.5), 2.5);
    return Math.min(Math.max(baseScore + changeScore, 1), 10);
  }

  /**
   * Get recommendation based on price change
   */
  private getRecommendation(change: number): 'Buy' | 'Sell' | 'Hold' {
    if (change > 1) return 'Buy';
    if (change < -1) return 'Sell';
    return 'Hold';
  }

  /**
   * Get market overview data
   */
  public async getMarketOverview(): Promise<MarketOverview> {
    console.log('📊 Getting market overview from stock data service...');
    
    // Try to get real data from TradingView first
    if (this.dataSource === 'tradingview') {
      try {
        console.log('🔄 Attempting to get TradingView market data...');
        const debugInfo = tradingViewService.getDebugInfo();
        console.log('🔧 TradingView debug info in getMarketOverview:', debugInfo);
        
        // Get some sample stocks for market overview
        const sampleQuotes = await Promise.all([
          tradingViewService.getStockQuote('COMI'),
          tradingViewService.getStockQuote('SWDY'),
          tradingViewService.getStockQuote('FWRY')
        ]);
        
        console.log('📈 Sample quotes from TradingView:', sampleQuotes);
        
        // If we got real data, create a market overview from it
        const validQuotes = sampleQuotes.filter(q => q !== null);
        if (validQuotes.length > 0) {
          console.log('✅ Using TradingView data for market overview');
          return {
            market_status: 'open',
            major_indices: [
              { name: 'EGX 30', value: 18450, change: 125, change_percentage: 0.68 },
              { name: 'EGX 70', value: 3250, change: -15, change_percentage: -0.46 },
              { name: 'EGX 100', value: 5680, change: 45, change_percentage: 0.80 }
            ],
            market_sentiment: 'bullish',
            top_gainers: [], // TODO: Convert StockQuote to Stock format
            top_losers: [], // TODO: Convert StockQuote to Stock format
            most_active: [] // TODO: Convert StockQuote to Stock format
          };
        }
      } catch (error) {
        console.warn('⚠️ Failed to get TradingView market data:', error);
      }
    }
    
    console.log('📊 Using default market overview data');
    // This would typically come from a market data API
    return {
      market_status: 'open',
      major_indices: [
        { name: 'EGX 30', value: 18450, change: 125, change_percentage: 0.68 },
        { name: 'EGX 70', value: 3250, change: -15, change_percentage: -0.46 },
        { name: 'EGX 100', value: 5680, change: 45, change_percentage: 0.80 }
      ],
      market_sentiment: 'bullish',
      top_gainers: [],
      top_losers: [],
      most_active: []
    };
  }

  /**
   * Fetch data from configured API
   */
  private async fetchFromAPI(symbol: string): Promise<StockQuote | null> {
    if (!this.apiKey) {
      console.warn('API key not configured, falling back to mock data');
      return this.getMockQuote(symbol);
    }

    // TODO: Implement actual API calls based on configured provider
    // This is a template for when you configure real APIs
    
    try {
      const response = await fetch(`${this.baseUrl}/quote?symbol=${symbol}`, {
        headers: {
          'X-RapidAPI-Key': this.apiKey,
          'X-RapidAPI-Host': 'yahoo-finance15.p.rapidapi.com'
        }
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      
      // Transform API response to our StockQuote format
      // (This will vary based on the API provider)
      return {
        symbol: data.symbol,
        price: data.regularMarketPrice,
        change: data.regularMarketChange,
        change_percentage: data.regularMarketChangePercent,
        volume: data.regularMarketVolume,
        last_updated: new Date().toISOString()
      };
    } catch (error) {
      console.error(`API fetch failed for ${symbol}:`, error);
      return this.getMockQuote(symbol);
    }
  }

  /**
   * Get mock quote data
   */
  private getMockQuote(symbol: string): StockQuote {
    // Generate realistic mock data based on symbol
    const basePrice = this.getBasePriceForSymbol(symbol);
    const randomChange = (Math.random() - 0.5) * basePrice * 0.05; // ±5% change
    
    return {
      symbol,
      price: basePrice + randomChange,
      change: randomChange,
      change_percentage: (randomChange / basePrice) * 100,
      volume: Math.floor(Math.random() * 2000000) + 100000,
      last_updated: new Date().toISOString()
    };
  }

  /**
   * Get base price for mock data generation
   */
  private getBasePriceForSymbol(symbol: string): number {
    const prices: Record<string, number> = {
      'COMI': 52.15,
      'ETEL': 18.30,
      'TALAAT': 15.80,
      'ORWE': 95.50,
      'HRHO': 24.70,
      'EKHO': 140.25,
      'CCAP': 78.90,
      'SWDY': 45.30
    };
    
    return prices[symbol] || 50.00; // Default price
  }

  /**
   * Get current data source
   */
  public getDataSource(): string {
    switch (this.dataSource) {
      case 'tradingview':
        const debugInfo = tradingViewService.getDebugInfo();
        return `TradingView ${debugInfo.isConnected ? '✅ Connected' : '❌ Disconnected'} (${debugInfo.stockCount} stocks)`;
      case 'api':
        return 'Real-time API data';
      case 'manual':
        return 'Manual user input';
      case 'mock':
      default:
        return 'Mock data (for demonstration)';
    }
  }

  /**
   * Get TradingView debug information
   */
  public getTradingViewDebugInfo() {
    return tradingViewService.getDebugInfo();
  }

  /**
   * Check if API is configured
   */
  public isAPIConfigured(): boolean {
    return this.dataSource === 'api' && this.apiKey !== null;
  }
}

// Export singleton instance
export const stockDataService = new StockDataService();

// Export helper functions
export const formatCurrency = (amount: number, currency: string = 'EGP'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(amount);
};

export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('en-US').format(num);
};

export const formatPercentage = (num: number): string => {
  return `${num >= 0 ? '+' : ''}${num.toFixed(2)}%`;
};
