import { useMemo } from 'react';
import type { Loan, CD, CashFlowProjection, FinancialSituation, RiskMetrics, InjectionStrategy, ScenarioResult, AIRecommendation } from '../types/cashflow';
import { 
  calculateFinancialSituation, 
  generateCashFlowProjection, 
  calculateRiskMetrics, 
  generateInjectionStrategy, 
  generateScenarios, 
  generateAIRecommendations 
} from '../utils/cashflowUtils';

interface UseCashFlowCalculationsProps {
  loans?: Loan[];
  cds?: CD[];
  startingBalance: number;
  customLivingExpenses: number;
  monthsToProject: number;
  isMonthPaid: (loanId: string, month: string) => boolean;
}

export function useCashFlowCalculations({
  loans,
  cds,
  startingBalance,
  customLivingExpenses,
  monthsToProject,
  isMonthPaid
}: UseCashFlowCalculationsProps) {
  
  // Calculate current financial situation
  const currentSituation = useMemo<FinancialSituation>(() => {
    return calculateFinancialSituation(loans, cds, customLivingExpenses);
  }, [loans, cds, customLivingExpenses]);

  // Generate dynamic cash flow projection
  const cashFlowProjection = useMemo<CashFlowProjection[]>(() => {
    console.log('Original Loan Data:', loans?.map(loan => ({
      name: loan.name,
      next_payment_date: loan.next_payment_date,
      end_date: loan.end_date,
      monthly_payment: loan.monthly_payment
    })));
    
    return generateCashFlowProjection(
      loans,
      cds,
      startingBalance,
      customLivingExpenses,
      monthsToProject,
      isMonthPaid
    );
  }, [loans, cds, startingBalance, customLivingExpenses, monthsToProject, isMonthPaid]);

  // Calculate critical analysis
  const criticalAnalysis = useMemo(() => {
    const negativeMonths = cashFlowProjection.filter(p => p.ending_balance < 0).length;
    const totalNegativeFlow = cashFlowProjection.reduce((sum, p) => 
      p.ending_balance < 0 ? sum + Math.abs(p.ending_balance) : sum, 0
    );
    
    return {
      negativeMonths,
      totalNegativeFlow,
      averageNegativeFlow: negativeMonths > 0 ? totalNegativeFlow / negativeMonths : 0
    };
  }, [cashFlowProjection]);

  // Calculate risk metrics
  const riskMetrics = useMemo<RiskMetrics>(() => {
    return calculateRiskMetrics(cashFlowProjection);
  }, [cashFlowProjection]);

  // Generate cash injection strategy
  const injectionStrategy = useMemo<InjectionStrategy | null>(() => {
    return generateInjectionStrategy(currentSituation, riskMetrics);
  }, [currentSituation, riskMetrics]);

  // Generate scenario analysis
  const scenarios = useMemo<ScenarioResult[]>(() => {
    const generateProjectionForScenario = (expenses: number) => {
      return generateCashFlowProjection(
        loans,
        cds,
        startingBalance,
        expenses,
        monthsToProject,
        isMonthPaid
      );
    };
    
    return generateScenarios(currentSituation, generateProjectionForScenario);
  }, [loans, cds, startingBalance, monthsToProject, isMonthPaid, currentSituation]);

  // Generate AI recommendations
  const aiRecommendations = useMemo<AIRecommendation[]>(() => {
    return generateAIRecommendations(currentSituation, riskMetrics);
  }, [currentSituation, riskMetrics]);

  return {
    currentSituation,
    cashFlowProjection,
    criticalAnalysis,
    riskMetrics,
    injectionStrategy,
    scenarios,
    aiRecommendations
  };
}
