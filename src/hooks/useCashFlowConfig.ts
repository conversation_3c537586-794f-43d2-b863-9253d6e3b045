import { useMemo, useEffect } from 'react';
import { useSupabaseQuery } from './useSupabaseQuery';
import { differenceInMonths, parseISO } from 'date-fns';
import type { CashFlowConfig, Loan } from '../types/cashflow';

export function useCashFlowConfig(loans?: Loan[]) {
  // Add persistence for configuration settings
  const { 
    data: configData, 
    addItem: addConfig, 
    updateItem: updateConfig
  } = useSupabaseQuery<CashFlowConfig>('cash_flow_config');

  // Get current config or use defaults
  const currentConfig = configData?.[0] || {
    starting_balance: 987641,
    custom_living_expenses: 55000,
    months_to_project: 120
  };

  // Dynamic calculation based on original loan end dates
  const defaultMonthsToProject = useMemo(() => {
    if (!loans || loans.length === 0) return 120; // Default 10 years if no loans

    // Find the loan with the latest original end date
    const latestEndDate = loans.reduce((latest, loan) => {
      try {
        const endDate = parseISO(loan.end_date);
        return endDate > latest ? endDate : latest;
      } catch (error) {
        console.error('Error parsing end date for loan:', loan.name, error);
        return latest;
      }
    }, new Date());

    // Calculate months from now to the latest loan end date
    const monthsToLatestLoan = Math.max(0, differenceInMonths(latestEndDate, new Date()));

    // Add some buffer months for analysis (12 months)
    return Math.max(120, monthsToLatestLoan + 12);
  }, [loans]);

  // Update monthsToProject when loans change (only if user hasn't customized it)
  useEffect(() => {
    // Only auto-update if using the default value
    if (currentConfig.months_to_project === 120) {
      updateConfiguration({ months_to_project: defaultMonthsToProject });
    }
  }, [defaultMonthsToProject, currentConfig.months_to_project]);

  // Helper functions for updating configuration with persistence
  const updateConfiguration = async (updates: Partial<CashFlowConfig>) => {
    try {
      if (configData?.[0]?.id) {
        // Update existing config
        await updateConfig(configData[0].id, updates);
      } else {
        // Create new config
        await addConfig({
          starting_balance: currentConfig.starting_balance,
          custom_living_expenses: currentConfig.custom_living_expenses,
          months_to_project: currentConfig.months_to_project,
          ...updates
        });
      }
    } catch (error) {
      console.error('Error updating configuration:', error);
    }
  };

  const setStartingBalance = (value: number) => {
    updateConfiguration({ starting_balance: value });
  };

  const setCustomLivingExpenses = (value: number) => {
    updateConfiguration({ custom_living_expenses: value });
  };

  const setMonthsToProject = (value: number) => {
    updateConfiguration({ months_to_project: value });
  };

  return {
    config: currentConfig,
    setStartingBalance,
    setCustomLivingExpenses,
    setMonthsToProject,
    defaultMonthsToProject
  };
}
