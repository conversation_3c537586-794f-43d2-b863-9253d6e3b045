import { useEffect } from 'react';
import { useSupabaseQuery } from './useSupabaseQuery';
import { parseISO, addMonths, format } from 'date-fns';
import type { PaidMonth, Loan } from '../types/cashflow';

export function usePaidMonths(loans?: Loan[]) {
  // Add persistence for paid months using the same storage system
  const { 
    data: paidMonthsData, 
    addItem: addPaidMonth, 
    deleteItem: deletePaidMonth
  } = useSupabaseQuery<PaidMonth>('paid_months');

  // Use persisted paid months data instead of local state
  const paidMonths = paidMonthsData || [];

  // Helper functions for managing paid months with persistence
  const markMonthAsPaid = async (loanId: string, month: string) => {
    const exists = paidMonths.some(p => p.loan_id === loanId && p.month === month);
    if (!exists) {
      try {
        await addPaidMonth({ loan_id: loanId, month });
      } catch (error) {
        console.error('Error adding paid month:', error);
      }
    }
  };

  const markMonthAsUnpaid = async (loanId: string, month: string) => {
    const existingItem = paidMonths.find(p => p.loan_id === loanId && p.month === month);
    if (existingItem && 'id' in existingItem) {
      try {
        await deletePaidMonth(existingItem.id as string);
      } catch (error) {
        console.error('Error removing paid month:', error);
      }
    }
  };

  const isMonthPaid = (loanId: string, month: string) => {
    return paidMonths.some(p => p.loan_id === loanId && p.month === month);
  };

  // Auto-mark past months as paid (before next_payment_date)
  useEffect(() => {
    if (!loans || loans.length === 0 || !paidMonthsData) return;
    
    // Only run initial auto-marking if no paid months exist yet
    if (paidMonths.length > 0) return;
    
    const autoMarkPaidMonths = async () => {
      for (const loan of loans) {
        try {
          const nextPaymentDate = parseISO(loan.next_payment_date);
          const startDate = parseISO(loan.start_date);
          
          // Mark all months from start_date to the month BEFORE next_payment_date as paid
          let checkDate = new Date(startDate);
          while (checkDate < nextPaymentDate) {
            const monthKey = format(checkDate, 'yyyy-MM');
            await markMonthAsPaid(loan.id, monthKey);
            checkDate = addMonths(checkDate, 1);
          }
        } catch (error) {
          console.error('Error auto-marking paid months for', loan.name, error);
        }
      }
    };
    
    autoMarkPaidMonths();
  }, [loans, paidMonthsData]); // Depend on both loans and paidMonthsData

  return {
    paidMonths,
    markMonthAsPaid,
    markMonthAsUnpaid,
    isMonthPaid
  };
}
