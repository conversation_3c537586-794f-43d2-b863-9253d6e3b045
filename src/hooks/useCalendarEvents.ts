/**
 * Calendar Events Custom Hook
 * Manages data fetching and event generation for the calendar
 */

import { useState, useEffect, useMemo } from 'react';
import { CalendarEvent, Loan, CD, FinancialGoal, Expense, CustomEvent, EventTimeHorizon } from '../types/calendar';
import { localDataStore } from '../lib/local-data-store';
import { useAuthStore } from '../store/authStore';
import {
  generateLoanEvents,
  generateCDEvents,
  generateGoalEvents,
  generateExpenseEvents,
  generateCustomEvents,
  filterEvents,
  sortEventsByDate
} from '../utils/calendarUtils';

export function useCalendarEvents(
  timeHorizon: EventTimeHorizon,
  typeFilter: string,
  categoryFilter: string,
  searchQuery: string
) {
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Data state
  const [loans, setLoans] = useState<Loan[]>([]);
  const [cds, setCds] = useState<CD[]>([]);
  const [goals, setGoals] = useState<FinancialGoal[]>([]);
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [customEvents, setCustomEvents] = useState<CustomEvent[]>([]);

  // Load data from local storage
  useEffect(() => {
    async function loadData() {
      if (!user?.id) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const [loansData, cdsData, goalsData, expensesData, customEventsData] = await Promise.all([
          localDataStore.data.loadCollection<Loan>('loans'),
          localDataStore.data.loadCollection<CD>('cds'),
          localDataStore.data.loadCollection<FinancialGoal>('financial_goals'),
          localDataStore.data.loadCollection<Expense>('expenses'),
          localDataStore.data.loadCollection<CustomEvent>('custom_events')
        ]);

        setLoans(loansData.filter(loan => loan.user_id === user.id));
        setCds(cdsData.filter(cd => cd.user_id === user.id));
        setGoals(goalsData.filter(goal => goal.user_id === user.id));
        setExpenses(expensesData.filter(expense => expense.user_id === user.id));
        setCustomEvents(customEventsData.filter(event => event.user_id === user.id));
      } catch (err) {
        console.error('Error loading calendar data:', err);
        setError('Failed to load calendar data');
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [user?.id]);

  // Generate calendar events
  const calendarEvents = useMemo(() => {
    const allEvents: CalendarEvent[] = [
      ...generateLoanEvents(loans, timeHorizon),
      ...generateCDEvents(cds),
      ...generateGoalEvents(goals),
      ...generateExpenseEvents(expenses),
      ...generateCustomEvents(customEvents)
    ];

    const filteredEvents = filterEvents(allEvents, searchQuery, typeFilter, categoryFilter);
    return sortEventsByDate(filteredEvents);
  }, [loans, cds, goals, expenses, customEvents, timeHorizon, searchQuery, typeFilter, categoryFilter]);

  // Functions to add/update/delete custom events
  const addCustomEvent = async (event: Omit<CustomEvent, 'id' | 'created_at'>) => {
    if (!user?.id) return;

    const newEvent: CustomEvent = {
      ...event,
      id: Date.now().toString(),
      created_at: new Date().toISOString(),
      user_id: user.id
    };

    try {
      await localDataStore.data.addItem('custom_events', newEvent);
      setCustomEvents(prev => [...prev, newEvent]);
      return newEvent;
    } catch (err) {
      console.error('Error adding custom event:', err);
      throw new Error('Failed to add event');
    }
  };

  const updateCustomEvent = async (id: string, updates: Partial<CustomEvent>) => {
    try {
      const updatedEvent = { ...customEvents.find(e => e.id === id), ...updates } as CustomEvent;
      await localDataStore.data.updateItem('custom_events', id, updatedEvent);
      setCustomEvents(prev => prev.map(event => 
        event.id === id ? { ...event, ...updates } : event
      ));
    } catch (err) {
      console.error('Error updating custom event:', err);
      throw new Error('Failed to update event');
    }
  };

  const deleteCustomEvent = async (id: string) => {
    try {
      await localDataStore.data.removeItem('custom_events', id);
      setCustomEvents(prev => prev.filter(event => event.id !== id));
    } catch (err) {
      console.error('Error deleting custom event:', err);
      throw new Error('Failed to delete event');
    }
  };

  // Refresh data function
  const refreshData = () => {
    if (user?.id) {
      // Trigger re-fetch by updating a dependency
      setLoading(true);
      setTimeout(() => setLoading(false), 100);
    }
  };

  return {
    // Data
    loans,
    cds,
    goals,
    expenses,
    customEvents,
    calendarEvents,
    
    // State
    loading,
    error,
    
    // Functions
    addCustomEvent,
    updateCustomEvent,
    deleteCustomEvent,
    refreshData,
    
    // Setters for external updates
    setLoans,
    setCds,
    setGoals,
    setExpenses,
    setCustomEvents
  };
}
