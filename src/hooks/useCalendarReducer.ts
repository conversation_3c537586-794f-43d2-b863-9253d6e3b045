/**
 * Calendar State Reducer
 * Manages complex state for the Calendar component using useReducer
 */

import { CalendarState, CalendarAction } from '../types/calendar';

export const initialCalendarState: CalendarState = {
  // Modal state
  isModalOpen: false,
  selectedDate: null,
  selectedEvent: null,
  
  // Data state
  loans: [],
  cds: [],
  goals: [],
  expenses: [],
  customEvents: [],
  
  // Form state
  eventTitle: '',
  eventDescription: '',
  eventAmount: '',
  eventColor: '',
  isRecurring: false,
  recurrencePattern: 'monthly',
  recurrenceEndDate: '',
  recurrenceCount: '',
  reminder: false,
  reminderTime: 30,
  reminderUnit: 'minutes',
  eventCategory: '',
  
  // Filter and pagination state
  eventTimeHorizon: 'month',
  eventTypeFilter: 'all',
  eventCategoryFilter: 'all',
  searchQuery: '',
  currentPage: 1
};

export function calendarReducer(state: CalendarState, action: CalendarAction): CalendarState {
  switch (action.type) {
    case 'SET_MODAL_OPEN':
      return { ...state, isModalOpen: action.payload };
    
    case 'SET_SELECTED_DATE':
      return { ...state, selectedDate: action.payload };
    
    case 'SET_SELECTED_EVENT':
      return { ...state, selectedEvent: action.payload };
    
    case 'SET_LOANS':
      return { ...state, loans: action.payload };
    
    case 'SET_CDS':
      return { ...state, cds: action.payload };
    
    case 'SET_GOALS':
      return { ...state, goals: action.payload };
    
    case 'SET_EXPENSES':
      return { ...state, expenses: action.payload };
    
    case 'SET_CUSTOM_EVENTS':
      return { ...state, customEvents: action.payload };
    
    case 'SET_EVENT_TITLE':
      return { ...state, eventTitle: action.payload };
    
    case 'SET_EVENT_DESCRIPTION':
      return { ...state, eventDescription: action.payload };
    
    case 'SET_EVENT_AMOUNT':
      return { ...state, eventAmount: action.payload };
    
    case 'SET_EVENT_COLOR':
      return { ...state, eventColor: action.payload };
    
    case 'SET_IS_RECURRING':
      return { ...state, isRecurring: action.payload };
    
    case 'SET_RECURRENCE_PATTERN':
      return { ...state, recurrencePattern: action.payload };
    
    case 'SET_RECURRENCE_END_DATE':
      return { ...state, recurrenceEndDate: action.payload };
    
    case 'SET_RECURRENCE_COUNT':
      return { ...state, recurrenceCount: action.payload };
    
    case 'SET_REMINDER':
      return { ...state, reminder: action.payload };
    
    case 'SET_REMINDER_TIME':
      return { ...state, reminderTime: action.payload };
    
    case 'SET_REMINDER_UNIT':
      return { ...state, reminderUnit: action.payload };
    
    case 'SET_EVENT_CATEGORY':
      return { ...state, eventCategory: action.payload };
    
    case 'SET_EVENT_TIME_HORIZON':
      return { ...state, eventTimeHorizon: action.payload, currentPage: 1 }; // Reset page when changing horizon
    
    case 'SET_EVENT_TYPE_FILTER':
      return { ...state, eventTypeFilter: action.payload, currentPage: 1 }; // Reset page when filtering
    
    case 'SET_EVENT_CATEGORY_FILTER':
      return { ...state, eventCategoryFilter: action.payload, currentPage: 1 }; // Reset page when filtering
    
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload, currentPage: 1 }; // Reset page when searching
    
    case 'SET_CURRENT_PAGE':
      return { ...state, currentPage: action.payload };
    
    case 'RESET_FORM':
      return {
        ...state,
        // Reset form fields
        eventTitle: '',
        eventDescription: '',
        eventAmount: '',
        eventColor: '',
        isRecurring: false,
        recurrencePattern: 'monthly',
        recurrenceEndDate: '',
        recurrenceCount: '',
        reminder: false,
        reminderTime: 30,
        reminderUnit: 'minutes',
        eventCategory: '',
        // Reset modal state
        isModalOpen: false,
        selectedDate: null,
        selectedEvent: null
      };
    
    default:
      return state;
  }
}
