import { useMemo } from 'react';
import { formatCurrency } from '../utils/currency';
import type { CashFlowProjection } from '../types/cashflow';

export function useCashFlowChart(cashFlowProjection: CashFlowProjection[]) {
  const chartData = useMemo(() => {
    // Show only first 24 months for chart clarity
    const chartProjection = cashFlowProjection.slice(0, 24);
    const labels = chartProjection.map(p => p.date);
    const balanceData = chartProjection.map(p => p.ending_balance);
    
    return {
      labels,
      datasets: [
        {
          label: 'Account Balance',
          data: balanceData,
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          tension: 0.1,
          pointBackgroundColor: balanceData.map(balance => balance < 0 ? '#ef4444' : '#10b981'),
        }
      ]
    };
  }, [cashFlowProjection]);

  const chartOptions = useMemo(() => ({
    responsive: true,
    plugins: {
      title: {
        display: true,
        text: 'Cash Flow Projection (24 Months)'
      },
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: false,
        ticks: {
          callback: function(value: any) {
            return formatCurrency(value);
          }
        }
      }
    },
    elements: {
      point: {
        radius: 4,
        hoverRadius: 6
      }
    }
  }), []);

  return {
    chartData,
    chartOptions
  };
}
