/**
 * Navigation Configuration
 * Improved navigation structure with logical grouping and better UX
 */

import {
  LayoutDashboard,
  PiggyBank,
  Receipt,
  Wallet,
  Building2,
  Banknote,
  BarChart2,
  Target,
  Calendar,
  DollarSign,
  Settings as SettingsIcon,
  LineChart,
  TrendingUp,
  Calculator,
  ShieldCheck,
  Briefcase,
  Bell,
  Eye,
  Activity,
  PieChart
} from 'lucide-react';

export interface NavigationItem {
  name: string;
  href: string;
  icon: any;
  description?: string;
  badge?: string;
  isNew?: boolean;
  isAdvanced?: boolean;
}

export interface NavigationGroup {
  name: string;
  icon?: any;
  items: NavigationItem[];
  isCollapsible?: boolean;
  defaultExpanded?: boolean;
}

export const navigationConfig: NavigationGroup[] = [
  {
    name: 'Overview',
    items: [
      {
        name: 'Dashboard',
        href: '/',
        icon: LayoutDashboard,
        description: 'Financial overview and key metrics'
      }
    ]
  },
  {
    name: 'Assets & Accounts',
    items: [
      {
        name: 'Bank Accounts',
        href: '/bank-accounts',
        icon: Building2,
        description: 'Manage your bank accounts and balances'
      },
      {
        name: 'CDs',
        href: '/cds',
        icon: Banknote,
        description: 'Certificate of Deposits and interest tracking'
      },
      {
        name: 'Net Worth',
        href: '/net-worth',
        icon: DollarSign,
        description: 'Track your total assets and net worth'
      }
    ],
    isCollapsible: true,
    defaultExpanded: true
  },
  {
    name: 'Spending & Budgets',
    items: [
      {
        name: 'Budgets',
        href: '/budgets',
        icon: PiggyBank,
        description: 'Plan and track your spending budgets'
      },
      {
        name: 'Expenses',
        href: '/expenses',
        icon: Receipt,
        description: 'Track and categorize your expenses'
      }
    ],
    isCollapsible: true,
    defaultExpanded: true
  },
  {
    name: 'Debt Management',
    items: [
      {
        name: 'Loans',
        href: '/loans',
        icon: Wallet,
        description: 'Manage your loans and payments'
      },
      {
        name: 'Loan Coverage Fund',
        href: '/loan-coverage-fund',
        icon: ShieldCheck,
        description: 'Plan funding for loan payments'
      },
      {
        name: 'Debt Payoff Simulator',
        href: '/debt-payoff-simulator',
        icon: Calculator,
        description: 'Simulate different payoff strategies',
        isAdvanced: true
      }
    ],
    isCollapsible: true,
    defaultExpanded: true
  },
  {
    name: 'Investments',
    items: [
      {
        name: 'Portfolio Overview',
        href: '/investments',
        icon: Briefcase,
        description: 'Manage your investment portfolios',
        isNew: true
      },
      {
        name: 'Stock Market',
        href: '/investments/stocks',
        icon: TrendingUp,
        description: 'Browse and analyze stocks',
        isNew: true
      },
      {
        name: 'Bonds',
        href: '/investments/bonds',
        icon: PieChart,
        description: 'Fixed income investments',
        isNew: true
      },
      {
        name: 'Watchlists',
        href: '/investments/watchlists',
        icon: Eye,
        description: 'Track your favorite stocks',
        isNew: true
      },
      {
        name: 'Alerts',
        href: '/investments/alerts',
        icon: Bell,
        description: 'Price and technical alerts',
        isNew: true
      },
      {
        name: 'Market Analysis',
        href: '/investments/analysis',
        icon: Activity,
        description: 'Technical analysis and insights',
        isAdvanced: true,
        isNew: true
      }
    ],
    isCollapsible: true,
    defaultExpanded: true
  },
  {
    name: 'Planning & Goals',
    items: [
      {
        name: 'Financial Goals',
        href: '/goals',
        icon: Target,
        description: 'Set and track your financial goals'
      },
      {
        name: 'Calendar',
        href: '/calendar',
        icon: Calendar,
        description: 'Financial events and payment schedule'
      }
    ],
    isCollapsible: true,
    defaultExpanded: true
  },
  {
    name: 'Analysis & Reports',
    items: [
      {
        name: 'Cash Flow Analysis',
        href: '/cash-flow-analysis',
        icon: LineChart,
        description: 'Analyze your cash flow and projections',
        isAdvanced: true
      },
      {
        name: 'Reports',
        href: '/reports',
        icon: BarChart2,
        description: 'Generate financial reports and insights'
      },
      {
        name: 'Financial Timeline',
        href: '/financial-timeline',
        icon: Calendar,
        description: 'Visualize your financial journey',
        isAdvanced: true
      },
      {
        name: 'Timeline Projections',
        href: '/timeline-projections',
        icon: TrendingUp,
        description: 'Project future financial scenarios',
        isAdvanced: true
      },
      {
        name: 'Financial Forecast',
        href: '/financial-forecast',
        icon: Calculator,
        description: 'Advanced financial forecasting',
        isAdvanced: true
      }
    ],
    isCollapsible: true,
    defaultExpanded: false
  },
  {
    name: 'Settings',
    items: [
      {
        name: 'Settings',
        href: '/settings',
        icon: SettingsIcon,
        description: 'Configure your app preferences'
      }
    ]
  }
];

// Helper function to get all navigation items in a flat array
export function getAllNavigationItems(): NavigationItem[] {
  return navigationConfig.reduce((acc, group) => {
    return acc.concat(group.items);
  }, [] as NavigationItem[]);
}

// Helper function to find a navigation item by href
export function findNavigationItem(href: string): NavigationItem | undefined {
  return getAllNavigationItems().find(item => item.href === href);
}

// Helper function to get navigation breadcrumb
export function getNavigationBreadcrumb(href: string): { group: string; item: NavigationItem } | null {
  for (const group of navigationConfig) {
    const item = group.items.find(item => item.href === href);
    if (item) {
      return { group: group.name, item };
    }
  }
  return null;
}
