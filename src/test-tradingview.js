// Test TradingView Service Connection
// This file can be imported in the browser console to test the service

import { tradingViewService } from './services/tradingViewService.js';
import { stockDataService } from './services/stockDataService.js';

window.testTradingView = async function() {
  console.log('🧪 Testing TradingView Service...');
  
  // Test 1: Check debug info
  const debugInfo = tradingViewService.getDebugInfo();
  console.log('📊 TradingView Debug Info:', debugInfo);
  
  // Test 2: Check server availability
  const isAvailable = await tradingViewService.checkServerAvailability();
  console.log('🌐 Server Available:', isAvailable);
  
  // Test 3: Try to get a stock quote
  try {
    const quote = await tradingViewService.getStockQuote('COMI');
    console.log('💰 COMI Quote:', quote);
  } catch (error) {
    console.error('❌ Error getting quote:', error);
  }
  
  // Test 4: Test stock data service
  const dataSource = stockDataService.getDataSource();
  console.log('📈 Stock Data Source:', dataSource);
  
  // Test 5: Try stock data service quote
  try {
    const serviceQuote = await stockDataService.getStockQuote('COMI');
    console.log('🏢 Stock Service COMI Quote:', serviceQuote);
  } catch (error) {
    console.error('❌ Error getting service quote:', error);
  }
  
  return {
    debugInfo,
    isAvailable,
    dataSource
  };
};

console.log('🚀 TradingView test function loaded! Run: testTradingView()');
