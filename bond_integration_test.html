<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bond API Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .bond-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .bond-symbol { font-weight: bold; color: #007bff; font-size: 1.2em; }
        .bond-price { font-size: 1.1em; color: #28a745; }
        .bond-yield { color: #6c757d; }
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        .government { border-left: 4px solid #28a745; }
        .corporate { border-left: 4px solid #ffc107; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        #loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .rating {
            background-color: #e9ecef;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>💰 Bond API Integration Test</h1>
    
    <div class="container">
        <h2>🔗 Bond Service Status</h2>
        <div id="connection-status" class="status info">Checking bond service...</div>
        <button onclick="testConnection()">Test Connection</button>
        <button onclick="testSearch()">Test Search</button>
        <button onclick="testSpecificBond()">Test Specific Bond</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div class="container">
        <h2>🔍 Bond Search Test</h2>
        <input type="text" id="searchInput" placeholder="Search bonds (e.g., Government, CIB, Corporate)" value="Government">
        <button onclick="searchBonds()">Search</button>
        <div id="search-results"></div>
    </div>

    <div class="container">
        <h2>💰 Egyptian Government & Corporate Bonds</h2>
        <div id="loading">Loading Egyptian bonds...</div>
        <div id="bond-data" class="grid"></div>
    </div>

    <div class="container">
        <h2>📊 Bond Market Summary</h2>
        <div id="market-summary"></div>
    </div>

    <div class="container">
        <h2>🔧 Raw API Response</h2>
        <pre id="raw-response">No data yet...</pre>
    </div>

    <div class="container">
        <h2>📋 Test Results Log</h2>
        <div id="test-log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        let testResults = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testResults.push({ timestamp, message, type });
            
            const logDiv = document.getElementById('test-log');
            const logElement = document.createElement('div');
            logElement.className = `status ${type}`;
            logElement.textContent = logEntry;
            logDiv.appendChild(logElement);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        async function testConnection() {
            try {
                log('Testing connection to Bond API...', 'info');
                const response = await fetch(`${API_BASE}/health`);
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('connection-status').className = 'status success';
                    document.getElementById('connection-status').textContent = 
                        `✅ Connected! Stocks: ${data.stocks}, Bonds: ${data.bonds} - ${data.message}`;
                    log(`Connection test successful - ${data.bonds} bonds available`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('connection-status').className = 'status error';
                document.getElementById('connection-status').textContent = 
                    `❌ Connection failed: ${error.message}`;
                log(`Connection test failed: ${error.message}`, 'error');
            }
        }

        async function testSearch() {
            try {
                log('Testing bond search functionality...', 'info');
                const response = await fetch(`${API_BASE}/api/bonds/search/government`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`Search test successful: Found ${data.length} government bonds`, 'success');
                    document.getElementById('raw-response').textContent = JSON.stringify(data, null, 2);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`Search test failed: ${error.message}`, 'error');
            }
        }

        async function testSpecificBond() {
            try {
                log('Testing specific bond retrieval...', 'info');
                const response = await fetch(`${API_BASE}/api/bonds/EGX:GOVT10Y`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`Specific bond test successful: ${data.name}`, 'success');
                    document.getElementById('raw-response').textContent = JSON.stringify(data, null, 2);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`Specific bond test failed: ${error.message}`, 'error');
            }
        }

        async function searchBonds() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                log('Please enter a search query', 'warning');
                return;
            }

            try {
                log(`Searching bonds for: ${query}`, 'info');
                const response = await fetch(`${API_BASE}/api/bonds/search/${encodeURIComponent(query)}`);
                
                if (response.ok) {
                    const data = await response.json();
                    displaySearchResults(data);
                    log(`Search completed: ${data.length} results for "${query}"`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`Search failed: ${error.message}`, 'error');
                document.getElementById('search-results').innerHTML = 
                    `<div class="status error">Search failed: ${error.message}</div>`;
            }
        }

        function displaySearchResults(results) {
            const container = document.getElementById('search-results');
            
            if (results.length === 0) {
                container.innerHTML = '<div class="status warning">No bonds found</div>';
                return;
            }

            container.innerHTML = results.map(bond => `
                <div class="bond-card ${bond.bond_type}">
                    <div class="bond-symbol">${bond.symbol}</div>
                    <div class="bond-name">${bond.name}</div>
                    <div class="bond-price">Price: ${bond.current_price.toLocaleString()} EGP</div>
                    <div class="bond-yield">Yield: ${bond.yield_to_maturity}%</div>
                    <div class="rating">Rating: ${bond.rating}</div>
                    <div class="${(bond.price_change || 0) >= 0 ? 'positive' : 'negative'}">
                        Change: ${bond.price_change || 0} EGP (${bond.price_change_percent || 0}%)
                    </div>
                    <small>Maturity: ${bond.maturity_date}</small>
                </div>
            `).join('');
        }

        async function loadBondData() {
            try {
                log('Loading all Egyptian bonds...', 'info');
                document.getElementById('loading').style.display = 'block';
                
                const response = await fetch(`${API_BASE}/api/bonds`);
                
                if (response.ok) {
                    const bonds = await response.json();
                    displayBondData(bonds);
                    updateMarketSummary(bonds);
                    log(`Bond data loaded successfully: ${bonds.length} bonds`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
                
            } catch (error) {
                log(`Failed to load bond data: ${error.message}`, 'error');
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        function displayBondData(bonds) {
            const container = document.getElementById('bond-data');
            
            container.innerHTML = bonds.map(bond => {
                const changeClass = (bond.price_change || 0) >= 0 ? 'positive' : 'negative';
                const changeSymbol = (bond.price_change || 0) >= 0 ? '+' : '';
                
                return `
                    <div class="bond-card ${bond.bond_type}">
                        <div class="bond-symbol">${bond.symbol}</div>
                        <div class="bond-name">${bond.name}</div>
                        <div class="bond-issuer">Issuer: ${bond.issuer}</div>
                        <div class="bond-price">${bond.current_price.toLocaleString()} EGP</div>
                        <div class="bond-yield">Yield: ${bond.yield_to_maturity}%</div>
                        <div class="rating">Rating: ${bond.rating}</div>
                        <div class="${changeClass}">
                            ${changeSymbol}${bond.price_change || 0} EGP (${changeSymbol}${bond.price_change_percent || 0}%)
                        </div>
                        <div><small>Face Value: ${bond.face_value.toLocaleString()} EGP</small></div>
                        <div><small>Coupon: ${bond.coupon_rate}%</small></div>
                        <div><small>Maturity: ${bond.maturity_date}</small></div>
                        <div><small>Type: ${bond.bond_type.charAt(0).toUpperCase() + bond.bond_type.slice(1)}</small></div>
                    </div>
                `;
            }).join('');
        }

        function updateMarketSummary(bonds) {
            const government = bonds.filter(b => b.bond_type === 'government');
            const corporate = bonds.filter(b => b.bond_type === 'corporate');
            
            const avgGovYield = government.reduce((sum, b) => sum + b.yield_to_maturity, 0) / government.length;
            const avgCorpYield = corporate.reduce((sum, b) => sum + b.yield_to_maturity, 0) / corporate.length;
            
            const positiveChanges = bonds.filter(b => (b.price_change || 0) > 0).length;
            const negativeChanges = bonds.filter(b => (b.price_change || 0) < 0).length;
            
            document.getElementById('market-summary').innerHTML = `
                <div class="grid">
                    <div class="bond-card">
                        <h3>Government Bonds</h3>
                        <div>Count: ${government.length}</div>
                        <div>Avg Yield: ${avgGovYield.toFixed(2)}%</div>
                        <div>Rating: B+</div>
                    </div>
                    <div class="bond-card">
                        <h3>Corporate Bonds</h3>
                        <div>Count: ${corporate.length}</div>
                        <div>Avg Yield: ${avgCorpYield.toFixed(2)}%</div>
                        <div>Rating: BB-</div>
                    </div>
                    <div class="bond-card">
                        <h3>Market Movement</h3>
                        <div class="positive">Up: ${positiveChanges}</div>
                        <div class="negative">Down: ${negativeChanges}</div>
                        <div>Unchanged: ${bonds.length - positiveChanges - negativeChanges}</div>
                    </div>
                </div>
            `;
        }

        function clearResults() {
            document.getElementById('search-results').innerHTML = '';
            document.getElementById('bond-data').innerHTML = '';
            document.getElementById('market-summary').innerHTML = '';
            document.getElementById('raw-response').textContent = 'No data yet...';
            document.getElementById('test-log').innerHTML = '';
            testResults = [];
            log('Results cleared', 'info');
        }

        // Initialize the page
        window.addEventListener('load', async () => {
            log('Page loaded, initializing bond tests...', 'info');
            await testConnection();
            await loadBondData();
        });

        // Auto-refresh bond data every 2 minutes
        setInterval(loadBondData, 2 * 60 * 1000);
    </script>
</body>
</html>
