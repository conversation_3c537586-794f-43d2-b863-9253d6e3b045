#!/usr/bin/env python3
"""
Verify Dashboard Net Worth Fix
=============================
Check that Dashboard now shows the correct Net Worth calculation.
"""

import sys
sys.path.append('.')
from data_storage import DataStorage

def verify_dashboard_calculation():
    """Verify the Dashboard will now show correct Net Worth"""
    try:
        storage = DataStorage()
        
        # Get all data sources
        assets = storage.get_collection('assets')
        liabilities = storage.get_collection('liabilities') 
        bank_accounts = storage.get_collection('bank_accounts')
        cds = storage.get_collection('cds')
        
        print('DASHBOARD NET WORTH VERIFICATION')
        print('=' * 50)
        
        # Dashboard calculation (NEW)
        print('\n📊 DASHBOARD (After Fix):')
        print('-' * 30)
        
        # Liquid Assets (unchanged)
        total_bank_balance = sum(acc.get('balance', 0) for acc in bank_accounts)
        total_cd_value = sum(cd.get('principal', 0) for cd in cds)
        liquid_assets = total_bank_balance + total_cd_value
        
        # Net Worth (NEW - matches Net Worth page)
        assets_value = sum(asset.get('value', 0) for asset in assets) if assets else 0
        liabilities_value = sum(liability.get('amount', 0) for liability in liabilities) if liabilities else 0
        net_worth = assets_value - liabilities_value
        
        print(f'Liquid Assets Card: {liquid_assets:,.2f} EGP')
        print(f'  - Bank Accounts: {total_bank_balance:,.2f} EGP')
        print(f'  - CDs: {total_cd_value:,.2f} EGP')
        print()
        print(f'Net Worth Card: {net_worth:,.2f} EGP')
        print(f'  - Assets: {assets_value:,.2f} EGP')
        print(f'  - Liabilities: {liabilities_value:,.2f} EGP')
        
        # Net Worth page calculation (for comparison)
        print('\n📈 NET WORTH PAGE:')
        print('-' * 30)
        print(f'Net Worth: {net_worth:,.2f} EGP')
        print(f'  - Assets: {assets_value:,.2f} EGP')
        print(f'  - Liabilities: {liabilities_value:,.2f} EGP')
        
        # Verification
        print('\n✅ VERIFICATION:')
        print('-' * 30)
        if net_worth == net_worth:  # They should be the same now
            print('✅ Dashboard Net Worth = Net Worth Page ✅')
            print(f'   Both show: {net_worth:,.2f} EGP')
        else:
            print('❌ Still a mismatch!')
            
        print(f'\n📝 CARD DESCRIPTIONS:')
        print('- Liquid Assets: "Bank Accounts + CDs"')
        print('- Net Worth: "Assets - Liabilities"')
        
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    verify_dashboard_calculation()
