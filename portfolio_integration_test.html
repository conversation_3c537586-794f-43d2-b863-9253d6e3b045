<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Test - Live Integration</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
        }
        .section {
            background: #f8f9fa;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        pre {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 12px;
        }
        .stock-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stock-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #28a745;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        .stock-card:hover {
            transform: translateY(-2px);
        }
        .portfolio-summary {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .metric {
            display: inline-block;
            margin: 10px 15px;
        }
        .metric-label {
            display: block;
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .metric-value {
            display: block;
            font-size: 1.4rem;
            font-weight: bold;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇪🇬 Financial Advisor Portfolio Test</h1>
            <p>Testing real-time EGX stock integration with portfolio calculations</p>
        </div>
        
        <div class="section">
            <h2>🔧 Setup & Configuration</h2>
            <button onclick="testAPIConnection()">Test API Connection</button>
            <button onclick="clearAllData()">Clear All Data</button>
            <button onclick="createDemoData()">Create Demo Portfolios</button>
            <div id="setupResults"></div>
        </div>

        <div class="section">
            <h2>📊 Portfolio Overview Test</h2>
            <button onclick="testPortfolioOverview()">Load Portfolio Data</button>
            <button onclick="refreshPrices()">Refresh Live Prices</button>
            <div id="portfolioResults"></div>
        </div>

        <div class="section">
            <h2>💹 Stock Search & Add Test</h2>
            <input type="text" id="searchInput" placeholder="Enter stock symbol (e.g., COMI)" style="padding: 10px; margin: 5px; border: 1px solid #ddd; border-radius: 4px;">
            <button onclick="testStockSearch()">Search Stock</button>
            <button onclick="simulateAddStock()">Simulate Add to Portfolio</button>
            <div id="searchResults"></div>
        </div>

        <div class="section">
            <h2>🧮 Real-time Calculation Test</h2>
            <button onclick="testCalculations()">Test Portfolio Calculations</button>
            <div id="calculationResults"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        let portfolioData = [];

        // Test API connection
        async function testAPIConnection() {
            const resultsDiv = document.getElementById('setupResults');
            resultsDiv.innerHTML = '<div class="loading"><div class="spinner"></div>Testing API connection...</div>';

            try {
                // Test monitored stocks endpoint
                const monitoredResponse = await fetch(`${API_BASE}/monitored-stocks`);
                const searchResponse = await fetch(`${API_BASE}/search?q=COMI`);
                
                if (monitoredResponse.ok && searchResponse.ok) {
                    const monitoredStocks = await monitoredResponse.json();
                    const searchResults = await searchResponse.json();
                    
                    resultsDiv.innerHTML = `
                        <div class="success">✅ API Connection Successful</div>
                        <div class="info">
                            <strong>Monitored Stocks:</strong> ${monitoredStocks.length}<br>
                            <strong>Search Functionality:</strong> Working (${searchResults.length} results for COMI)
                        </div>
                        <pre>${JSON.stringify(monitoredStocks.slice(0, 3), null, 2)}</pre>
                    `;
                } else {
                    resultsDiv.innerHTML = '<div class="error">❌ API Connection Failed</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ API Error: ${error.message}</div>`;
            }
        }

        // Clear all portfolio data
        function clearAllData() {
            localStorage.removeItem('real_portfolios');
            localStorage.removeItem('real_portfolio_holdings');
            document.getElementById('setupResults').innerHTML = '<div class="success">✅ All portfolio data cleared</div>';
        }

        // Create demo portfolios
        async function createDemoData() {
            const resultsDiv = document.getElementById('setupResults');
            resultsDiv.innerHTML = '<div class="loading"><div class="spinner"></div>Creating demo portfolios...</div>';

            try {
                // Simulate portfolio creation similar to demoPortfolios.ts
                const portfolios = [
                    {
                        id: 'demo-growth',
                        name: 'EGX Growth Portfolio',
                        description: 'High-growth Egyptian stocks',
                        initial_balance: 100000,
                        currency: 'EGP',
                        created_at: new Date().toISOString()
                    },
                    {
                        id: 'demo-income',
                        name: 'EGX Income Portfolio', 
                        description: 'Dividend-focused strategy',
                        initial_balance: 75000,
                        currency: 'EGP',
                        created_at: new Date().toISOString()
                    }
                ];

                const holdings = [
                    { portfolio_id: 'demo-growth', symbol: 'COMI', shares: 500, average_price: 80.00, purchase_date: new Date().toISOString() },
                    { portfolio_id: 'demo-growth', symbol: 'FWRY', shares: 2000, average_price: 12.00, purchase_date: new Date().toISOString() },
                    { portfolio_id: 'demo-growth', symbol: 'SWDY', shares: 300, average_price: 75.00, purchase_date: new Date().toISOString() },
                    { portfolio_id: 'demo-income', symbol: 'ETEL', shares: 1000, average_price: 35.00, purchase_date: new Date().toISOString() },
                    { portfolio_id: 'demo-income', symbol: 'VALU', shares: 5000, average_price: 9.20, purchase_date: new Date().toISOString() }
                ];

                localStorage.setItem('real_portfolios', JSON.stringify(portfolios));
                localStorage.setItem('real_portfolio_holdings', JSON.stringify(holdings));

                resultsDiv.innerHTML = `
                    <div class="success">✅ Demo portfolios created successfully</div>
                    <div class="info">
                        Created ${portfolios.length} portfolios with ${holdings.length} stock positions
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error creating demo data: ${error.message}</div>`;
            }
        }

        // Test portfolio overview functionality
        async function testPortfolioOverview() {
            const resultsDiv = document.getElementById('portfolioResults');
            resultsDiv.innerHTML = '<div class="loading"><div class="spinner"></div>Loading portfolio data...</div>';

            try {
                const portfolios = JSON.parse(localStorage.getItem('real_portfolios') || '[]');
                const holdings = JSON.parse(localStorage.getItem('real_portfolio_holdings') || '[]');

                if (portfolios.length === 0) {
                    resultsDiv.innerHTML = '<div class="warning">⚠️ No portfolios found. Create demo data first.</div>';
                    return;
                }

                let html = `<div class="success">✅ Found ${portfolios.length} portfolios with holdings</div>`;
                
                for (const portfolio of portfolios) {
                    const portfolioHoldings = holdings.filter(h => h.portfolio_id === portfolio.id);
                    let totalValue = 0;
                    let totalCost = 0;
                    let holdingsHtml = '';

                    for (const holding of portfolioHoldings) {
                        // Get current price
                        try {
                            const response = await fetch(`${API_BASE}/search?q=${holding.symbol}`);
                            const searchResults = await response.json();
                            const stock = searchResults.find(s => s.symbol === holding.symbol);
                            
                            if (stock && stock.price > 0) {
                                const currentValue = stock.price * holding.shares;
                                const costBasis = holding.average_price * holding.shares;
                                const gainLoss = currentValue - costBasis;
                                const gainLossPercent = (gainLoss / costBasis) * 100;

                                totalValue += currentValue;
                                totalCost += costBasis;

                                holdingsHtml += `
                                    <div class="stock-card">
                                        <strong>${holding.symbol}</strong><br>
                                        Shares: ${holding.shares.toLocaleString()}<br>
                                        Avg Price: ${holding.average_price.toFixed(2)} EGP<br>
                                        Current: ${stock.price.toFixed(2)} EGP<br>
                                        Value: ${currentValue.toFixed(2)} EGP<br>
                                        <strong style="color: ${gainLoss >= 0 ? '#28a745' : '#dc3545'}">
                                            P&L: ${gainLoss.toFixed(2)} EGP (${gainLossPercent.toFixed(2)}%)
                                        </strong>
                                    </div>
                                `;
                            } else {
                                holdingsHtml += `
                                    <div class="stock-card">
                                        <strong>${holding.symbol}</strong><br>
                                        <span style="color: #dc3545;">Price not available</span>
                                    </div>
                                `;
                            }
                        } catch (error) {
                            console.error(`Error fetching price for ${holding.symbol}:`, error);
                        }
                    }

                    const totalGainLoss = totalValue - totalCost;
                    const totalGainLossPercent = totalCost > 0 ? (totalGainLoss / totalCost) * 100 : 0;

                    html += `
                        <div class="portfolio-summary">
                            <h3>${portfolio.name}</h3>
                            <div class="metric">
                                <span class="metric-label">Total Value</span>
                                <span class="metric-value">${totalValue.toFixed(2)} EGP</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Total Cost</span>
                                <span class="metric-value">${totalCost.toFixed(2)} EGP</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">P&L</span>
                                <span class="metric-value" style="color: ${totalGainLoss >= 0 ? '#c8e6c9' : '#ffcdd2'}">
                                    ${totalGainLoss.toFixed(2)} EGP (${totalGainLossPercent.toFixed(2)}%)
                                </span>
                            </div>
                        </div>
                        <div class="stock-grid">${holdingsHtml}</div>
                    `;
                }

                resultsDiv.innerHTML = html;
                portfolioData = portfolios;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error loading portfolio data: ${error.message}</div>`;
            }
        }

        // Test stock search
        async function testStockSearch() {
            const query = document.getElementById('searchInput').value || 'COMI';
            const resultsDiv = document.getElementById('searchResults');
            resultsDiv.innerHTML = '<div class="loading"><div class="spinner"></div>Searching...</div>';

            try {
                const response = await fetch(`${API_BASE}/search?q=${query}`);
                const results = await response.json();

                if (results.length > 0) {
                    let html = `<div class="success">✅ Found ${results.length} results for "${query}"</div>`;
                    
                    results.slice(0, 5).forEach(stock => {
                        html += `
                            <div class="stock-card">
                                <strong>${stock.symbol}</strong> - ${stock.name}<br>
                                Price: ${stock.price ? stock.price.toFixed(2) + ' EGP' : 'N/A'}<br>
                                Change: ${stock.change ? stock.change.toFixed(2) + ' EGP' : 'N/A'}<br>
                                Type: ${stock.type || 'N/A'}
                            </div>
                        `;
                    });

                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="warning">⚠️ No results found for "${query}"</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Search error: ${error.message}</div>`;
            }
        }

        // Refresh live prices
        async function refreshPrices() {
            if (portfolioData.length === 0) {
                document.getElementById('portfolioResults').innerHTML = '<div class="warning">⚠️ Load portfolio data first</div>';
                return;
            }
            await testPortfolioOverview();
        }

        // Test calculations
        async function testCalculations() {
            const resultsDiv = document.getElementById('calculationResults');
            resultsDiv.innerHTML = '<div class="loading"><div class="spinner"></div>Testing calculations...</div>';

            // Test various calculation scenarios
            const testScenarios = [
                { symbol: 'COMI', shares: 100, avgPrice: 75.50, scenario: 'Profit scenario' },
                { symbol: 'VALU', shares: 1000, avgPrice: 10.00, scenario: 'Loss scenario' },
                { symbol: 'FWRY', shares: 500, avgPrice: 15.00, scenario: 'Mixed scenario' }
            ];

            let html = '<div class="success">✅ Running calculation tests...</div>';

            for (const test of testScenarios) {
                try {
                    const response = await fetch(`${API_BASE}/search?q=${test.symbol}`);
                    const results = await response.json();
                    const stock = results.find(s => s.symbol === test.symbol);

                    if (stock && stock.price > 0) {
                        const currentValue = stock.price * test.shares;
                        const costBasis = test.avgPrice * test.shares;
                        const gainLoss = currentValue - costBasis;
                        const gainLossPercent = (gainLoss / costBasis) * 100;

                        html += `
                            <div class="stock-card">
                                <strong>${test.scenario} - ${test.symbol}</strong><br>
                                Current Price: ${stock.price.toFixed(2)} EGP<br>
                                Position Value: ${currentValue.toFixed(2)} EGP<br>
                                Cost Basis: ${costBasis.toFixed(2)} EGP<br>
                                <strong style="color: ${gainLoss >= 0 ? '#28a745' : '#dc3545'}">
                                    Gain/Loss: ${gainLoss.toFixed(2)} EGP (${gainLossPercent.toFixed(2)}%)
                                </strong>
                            </div>
                        `;
                    }
                } catch (error) {
                    html += `<div class="error">❌ Error testing ${test.symbol}: ${error.message}</div>`;
                }
            }

            resultsDiv.innerHTML = html;
        }

        // Auto-run API test on load
        window.addEventListener('load', () => {
            setTimeout(testAPIConnection, 1000);
        });
    </script>
</body>
</html>
