<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Holdings Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { margin: 5px; padding: 8px 16px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Debug Holdings Data Structure</h1>
    <p>This page helps debug the holdings data to understand why edit/remove buttons might not be working.</p>

    <div class="debug-section">
        <h3>Test Holdings Data from Desktop Storage</h3>
        <button onclick="debugHoldings()">Debug Holdings Structure</button>
        <div id="holdings-debug"></div>
    </div>

    <div class="debug-section">
        <h3>Test Edit/Remove Functions</h3>
        <button onclick="testEditFunction()">Test Edit Function</button>
        <button onclick="testRemoveFunction()">Test Remove Function</button>
        <div id="function-test"></div>
    </div>

    <script>
        async function debugHoldings() {
            const resultDiv = document.getElementById('holdings-debug');
            
            try {
                resultDiv.innerHTML = '<p>Debugging holdings data...</p>';
                
                let portfolios = [];
                let allHoldings = [];
                
                if (window.pywebview?.api) {
                    // Get portfolios from Python
                    const portfoliosResponse = await window.pywebview.api.get_investment_portfolios();
                    console.log('Raw portfolios response:', portfoliosResponse);
                    
                    if (typeof portfoliosResponse === 'string') {
                        portfolios = JSON.parse(portfoliosResponse);
                    } else {
                        portfolios = portfoliosResponse || [];
                    }
                    
                    console.log('Parsed portfolios:', portfolios);
                    
                    // Get holdings for each portfolio
                    for (const portfolio of portfolios) {
                        try {
                            const holdingsResponse = await window.pywebview.api.get_portfolio_holdings(portfolio.id);
                            console.log(`Holdings for portfolio ${portfolio.id}:`, holdingsResponse);
                            
                            let holdings;
                            if (typeof holdingsResponse === 'string') {
                                holdings = JSON.parse(holdingsResponse);
                            } else {
                                holdings = holdingsResponse || [];
                            }
                            
                            for (const holding of holdings) {
                                allHoldings.push({
                                    portfolio: portfolio.name,
                                    portfolioId: portfolio.id,
                                    holding: holding
                                });
                            }
                        } catch (error) {
                            console.error(`Error getting holdings for portfolio ${portfolio.id}:`, error);
                        }
                    }
                } else {
                    // Browser mode - check localStorage
                    portfolios = JSON.parse(localStorage.getItem('investment_portfolios') || '[]');
                    const holdings = JSON.parse(localStorage.getItem('investment_stock_holdings') || '[]');
                    
                    for (const holding of holdings) {
                        const portfolio = portfolios.find(p => p.id === holding.portfolio_id);
                        allHoldings.push({
                            portfolio: portfolio ? portfolio.name : 'Unknown Portfolio',
                            portfolioId: holding.portfolio_id,
                            holding: holding
                        });
                    }
                }
                
                let html = `
                    <div>
                        <h4>Debug Results</h4>
                        <p><strong>Environment:</strong> ${window.pywebview ? 'Desktop App' : 'Browser'}</p>
                        <p><strong>Total Portfolios:</strong> ${portfolios.length}</p>
                        <p><strong>Total Holdings:</strong> ${allHoldings.length}</p>
                        
                        <h5>Holdings Structure:</h5>
                `;
                
                if (allHoldings.length > 0) {
                    for (const item of allHoldings) {
                        const holding = item.holding;
                        html += `
                            <div style="border: 1px solid #ccc; margin: 10px 0; padding: 10px; border-radius: 5px;">
                                <h6>Portfolio: ${item.portfolio}</h6>
                                <p><strong>Symbol:</strong> ${holding.symbol}</p>
                                <p><strong>Has ID:</strong> ${holding.id ? 'YES (' + holding.id + ')' : 'NO'}</p>
                                <p><strong>Shares:</strong> ${holding.shares}</p>
                                <p><strong>Average Cost:</strong> ${holding.average_cost}</p>
                                <pre>${JSON.stringify(holding, null, 2)}</pre>
                            </div>
                        `;
                    }
                } else {
                    html += '<p>No holdings found</p>';
                }
                
                html += `
                        <h5>Raw Data:</h5>
                        <h6>Portfolios:</h6>
                        <pre>${JSON.stringify(portfolios, null, 2)}</pre>
                    </div>
                `;
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div>
                        <h4>Debug Error</h4>
                        <p>Error: ${error.message}</p>
                        <pre>${error.stack}</pre>
                    </div>
                `;
            }
        }

        async function testEditFunction() {
            const resultDiv = document.getElementById('function-test');
            
            try {
                if (!window.pywebview?.api) {
                    resultDiv.innerHTML = '<p>Desktop app not detected. Cannot test Python functions.</p>';
                    return;
                }
                
                resultDiv.innerHTML = '<p>Testing edit function...</p>';
                
                // Test updating a holding
                const testUpdates = {
                    shares: 150,
                    average_cost: 85,
                    total_cost: 150 * 85,
                    updated_at: new Date().toISOString()
                };
                
                const response = await window.pywebview.api.update_stock_holding('test_holding_id', testUpdates);
                console.log('Edit test response:', response);
                
                resultDiv.innerHTML = `
                    <div>
                        <h4>Edit Function Test</h4>
                        <p><strong>Function Called:</strong> update_stock_holding</p>
                        <p><strong>Response:</strong></p>
                        <pre>${JSON.stringify(response, null, 2)}</pre>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div>
                        <h4>Edit Function Test Error</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testRemoveFunction() {
            const resultDiv = document.getElementById('function-test');
            
            try {
                if (!window.pywebview?.api) {
                    resultDiv.innerHTML = '<p>Desktop app not detected. Cannot test Python functions.</p>';
                    return;
                }
                
                resultDiv.innerHTML = '<p>Testing remove function...</p>';
                
                const response = await window.pywebview.api.delete_stock_holding('test_holding_id');
                console.log('Remove test response:', response);
                
                resultDiv.innerHTML = `
                    <div>
                        <h4>Remove Function Test</h4>
                        <p><strong>Function Called:</strong> delete_stock_holding</p>
                        <p><strong>Response:</strong></p>
                        <pre>${JSON.stringify(response, null, 2)}</pre>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div>
                        <h4>Remove Function Test Error</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        // Auto-run on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('Auto-running holdings debug...');
                debugHoldings();
            }, 500);
        });
    </script>
</body>
</html>
