#!/usr/bin/env python3
"""
Verify Loan Changes

This script verifies that the loan remaining balances have been updated correctly.
"""

import os
import json

# Configuration
DATA_DIR = os.path.join(os.path.expanduser("~"), ".financial_advisor")
LOANS_DIR = os.path.join(DATA_DIR, "loans")

def verify_loan_changes():
    """Verify that the loan remaining balances have been updated correctly"""
    try:
        # Check if the loans directory exists
        if not os.path.exists(LOANS_DIR):
            print(f"Loans directory not found at: {LOANS_DIR}")
            return False
            
        # Get all loan files
        loan_files = [f for f in os.listdir(LOANS_DIR) if f.endswith('.json')]
        
        if not loan_files:
            print("No loan files found.")
            return False
            
        print(f"Found {len(loan_files)} loan files.")
        
        # Process each loan
        for loan_file in loan_files:
            file_path = os.path.join(LOANS_DIR, loan_file)
            
            try:
                with open(file_path, 'r') as f:
                    loan_data = json.load(f)
                    
                # Print current loan info
                print(f"\nLoan: {loan_data.get('name', 'Unknown')}")
                print(f"  Principal: {loan_data.get('principal', 0)}")
                print(f"  Remaining Balance: {loan_data.get('remaining_balance', 0)}")
                print(f"  Interest Rate: {loan_data.get('interest_rate', 0)}%")
                print(f"  Term Months: {loan_data.get('term_months', 0)}")
                print(f"  Monthly Payment: {loan_data.get('monthly_payment', 0)}")
                print(f"  Next Payment Date: {loan_data.get('next_payment_date', 'Unknown')}")
                
            except json.JSONDecodeError:
                print(f"Error decoding JSON in file: {file_path}")
                continue
            except Exception as e:
                print(f"Error processing loan file {loan_file}: {e}")
                continue
                
        return True
    except Exception as e:
        print(f"Error verifying loan changes: {e}")
        return False

def main():
    print("\nFinancial Advisor - Verify Loan Changes")
    print("=" * 60)
    print("This script verifies that the loan remaining balances have been updated correctly.")
    
    # Verify the loan changes
    if verify_loan_changes():
        print("\nVerification complete.")
    else:
        print("\nVerification failed.")

if __name__ == "__main__":
    main()
