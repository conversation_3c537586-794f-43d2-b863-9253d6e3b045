import os
import sys
import subprocess
import time
import webview
from data_storage import DataStorage

# Constants
APP_NAME = "Financial Advisor"
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
DEBUG_MODE = True

def build_app():
    """Build the React app"""
    print("Building React app...")
    
    # Set environment variables for the build
    os.environ["VITE_USE_LOCAL_STORE"] = "true"
    os.environ["REACT_APP_USE_LOCAL_STORE"] = "true"
    
    # Run the build command
    try:
        subprocess.run(
            "npm run build",
            shell=True,
            check=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        print("Build completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Build failed: {e}")
        return False

def on_loaded(window):
    """Called when the webpage has loaded"""
    print("WebView loaded - page is ready")
    
    # Clean indexes to remove phantom references first
    try:
        # Use JavaScript to call the API methods
        if hasattr(window, 'evaluate_js'):
            # Use evaluate_js to run storage maintenance through JavaScript
            window.evaluate_js("""
                if (window.pywebview && window.pywebview.api) {
                    console.log('Running storage maintenance through pywebview.api');
                    window.pywebview.api.clean_indexes().then(result => {
                        console.log('Storage index cleanup result:', result);
                    });
                    window.pywebview.api.repair_storage().then(result => {
                        console.log('Storage repair result:', result);
                    });
                } else {
                    console.error('pywebview.api not available for storage maintenance');
                }
            """)
            print("Storage maintenance scheduled through JavaScript")
        else:
            print("Window does not support evaluate_js, skipping storage maintenance")
            
        # Also inject JavaScript to ensure the frontend knows about the API
        if hasattr(window, 'evaluate_js'):
            window.evaluate_js("""
                // Make sure the pywebview API is properly exposed to the frontend
                if (!window.pywebview) {
                    window.pywebview = { api: {} };
                }
                
                // Expose the API methods
                if (window.pywebview && !window.pywebview.api) {
                    window.pywebview.api = {};
                }
                
                // Log the API status
                console.log('PyWebView API status:', window.pywebview ? 'Available' : 'Not available');
                if (window.pywebview && window.pywebview.api) {
                    console.log('API methods:', Object.keys(window.pywebview.api));
                }
                
                // Set the environment variable properly
                window.USE_LOCAL_STORE = true;
                console.log('USE_LOCAL_STORE set to:', window.USE_LOCAL_STORE);
            """)
            print("PyWebView API setup complete")
    except Exception as e:
        print(f"Error performing storage maintenance: {e}")
        import traceback
        traceback.print_exc()

def launch_app():
    """Launch the desktop application with simplified storage"""
    print(f"Starting {APP_NAME}...")
    print(f"Storage location: {os.path.join(os.path.expanduser('~'), '.financial_advisor')}")
    
    try:
        # Initialize the data storage
        storage = DataStorage()
        
        # Set environment variables
        os.environ["VITE_USE_LOCAL_STORE"] = "true"
        os.environ["REACT_APP_USE_LOCAL_STORE"] = "true"
        print("Environment variables set:")
        print(f"VITE_USE_LOCAL_STORE = {os.environ.get('VITE_USE_LOCAL_STORE')}")
        print(f"REACT_APP_USE_LOCAL_STORE = {os.environ.get('REACT_APP_USE_LOCAL_STORE')}")
        
        # Get the path to the built app
        dist_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'dist')
        index_path = os.path.join(dist_path, 'index.html')
        
        if not os.path.exists(index_path):
            print(f"Error: Built app not found at {index_path}")
            print("Please run 'npm run build' first")
            return
        
        # Create the URL for the local file
        url = f"file://{index_path}"
        print(f"Loading app from: {url}")
        
        # Create the window with storage API exposed
        window = webview.create_window(
            title=APP_NAME,
            url=url,
            width=WINDOW_WIDTH,
            height=WINDOW_HEIGHT,
            min_size=(800, 600),
            js_api=storage,
            text_select=True
        )
        
        # Register the loaded event handler
        window.events.loaded += on_loaded
        
        # Start the window
        webview.start(debug=DEBUG_MODE)
        
    except Exception as e:
        print(f"Error in desktop app: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Build the app first
    if build_app():
        # Then launch it
        launch_app()
    else:
        print("Failed to build the app. Exiting.")
        sys.exit(1)
