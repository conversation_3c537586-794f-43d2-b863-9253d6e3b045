import os
import sys
import subprocess
import shutil
import traceback

def log(message):
    """Log a message to console"""
    print(f"[RESTORE] {message}")

def restore_app():
    """Restore the app to its original state"""
    log("Starting app restoration...")
    
    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    log(f"Current directory: {current_dir}")
    
    # Remove temporary files we created
    temp_files = [
        "storage-bridge.js",
        "fix-data-loading.py",
        "fix-currency.py",
        "check-data.py",
        "run-with-server.py",
        "run-dev-app.py",
        "direct-run.py",
        "show-data.py",
        "simple-dashboard.py",
        "dashboard.html",
        "temp_index.html"
    ]
    
    for file in temp_files:
        file_path = os.path.join(current_dir, file)
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                log(f"Removed temporary file: {file}")
            except Exception as e:
                log(f"Error removing file {file}: {e}")
    
    # Run npm install to restore any dependencies
    log("Running npm install to restore dependencies...")
    try:
        subprocess.run(
            "npm install",
            shell=True,
            check=True,
            cwd=current_dir
        )
        log("Dependencies restored successfully")
    except subprocess.CalledProcessError as e:
        log(f"Error restoring dependencies: {e}")
    
    # Build the app
    log("Building the app...")
    try:
        subprocess.run(
            "npm run build",
            shell=True,
            check=True,
            cwd=current_dir
        )
        log("App built successfully")
    except subprocess.CalledProcessError as e:
        log(f"Error building app: {e}")
    
    # Run the original app
    log("Starting the original app...")
    try:
        subprocess.Popen(
            "python app.py",
            shell=True,
            cwd=current_dir
        )
        log("App started successfully")
    except Exception as e:
        log(f"Error starting app: {e}")
        
        # Try the alternative app.py
        try:
            subprocess.Popen(
                "python build-and-run-full.py",
                shell=True,
                cwd=current_dir
            )
            log("Alternative app started successfully")
        except Exception as e2:
            log(f"Error starting alternative app: {e2}")
    
    log("App restoration completed")
    return True

if __name__ == "__main__":
    restore_app()
