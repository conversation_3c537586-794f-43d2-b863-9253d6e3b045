#!/usr/bin/env python3

"""
Calendar Optimization Results
============================
Measure and compare the optimization results.
"""

import os

def measure_component_size(file_path, component_name):
    """Measure component size and complexity"""
    if not os.path.exists(file_path):
        return {
            'name': component_name,
            'exists': False,
            'lines': 0,
            'size': 0,
            'useState_count': 0,
            'useEffect_count': 0
        }
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    total_lines = len(lines)
    file_size = len(content)
    
    # Count React patterns
    use_state_count = content.count('useState')
    use_effect_count = content.count('useEffect')
    
    return {
        'name': component_name,
        'exists': True,
        'lines': total_lines,
        'size': file_size,
        'useState_count': use_state_count,
        'useEffect_count': use_effect_count
    }

def analyze_optimization():
    """Analyze the optimization results"""
    
    print("📊 CALENDAR OPTIMIZATION RESULTS")
    print("=" * 50)
    
    # Original Calendar component
    original = measure_component_size('src/pages/Calendar.tsx', 'Original Calendar')
    
    # Optimized Calendar component
    optimized = measure_component_size('src/pages/CalendarOptimized.tsx', 'Optimized Calendar')
    
    # Sub-components
    sub_components = [
        measure_component_size('src/components/CalendarView.tsx', 'CalendarView'),
        measure_component_size('src/components/EventModal.tsx', 'EventModal'),
        measure_component_size('src/components/EventForm.tsx', 'EventForm'),
        measure_component_size('src/components/Toast.tsx', 'Toast'),
        measure_component_size('src/types/calendar.ts', 'Calendar Types'),
        measure_component_size('src/utils/calendarUtils.ts', 'Calendar Utils'),
        measure_component_size('src/hooks/useCalendarReducer.ts', 'Calendar Reducer'),
        measure_component_size('src/hooks/useCalendarEvents.ts', 'Calendar Events Hook')
    ]
    
    print(f"🔍 COMPARISON:")
    print(f"  Original Calendar:")
    print(f"    • Lines: {original['lines']:,}")
    print(f"    • Size: {original['size']:,} bytes")
    print(f"    • useState hooks: {original['useState_count']}")
    print(f"    • useEffect hooks: {original['useEffect_count']}")
    
    print(f"\n  Optimized Calendar:")
    print(f"    • Lines: {optimized['lines']:,}")
    print(f"    • Size: {optimized['size']:,} bytes")
    print(f"    • useState hooks: {optimized['useState_count']}")
    print(f"    • useEffect hooks: {optimized['useEffect_count']}")
    
    print(f"\n📈 IMPROVEMENTS:")
    if original['exists'] and optimized['exists']:
        lines_reduction = original['lines'] - optimized['lines']
        size_reduction = original['size'] - optimized['size']
        state_reduction = original['useState_count'] - optimized['useState_count']
        
        print(f"  • Lines reduced: {lines_reduction:,} ({lines_reduction/original['lines']*100:.1f}%)")
        print(f"  • Size reduced: {size_reduction:,} bytes ({size_reduction/original['size']*100:.1f}%)")
        print(f"  • useState hooks reduced: {state_reduction} ({state_reduction/original['useState_count']*100:.1f}%)")
    
    print(f"\n🔧 SUB-COMPONENTS CREATED:")
    total_sub_lines = 0
    total_sub_size = 0
    
    for component in sub_components:
        if component['exists']:
            print(f"  • {component['name']}: {component['lines']:,} lines, {component['size']:,} bytes")
            total_sub_lines += component['lines']
            total_sub_size += component['size']
    
    print(f"\n📊 TOTAL MODULAR CODE:")
    print(f"  • Total lines: {optimized['lines'] + total_sub_lines:,}")
    print(f"  • Total size: {optimized['size'] + total_sub_size:,} bytes")
    print(f"  • Number of files: {len([c for c in sub_components if c['exists']]) + 1}")
    
    print(f"\n💡 OPTIMIZATION BENEFITS:")
    print(f"  ✅ Code is now split into {len([c for c in sub_components if c['exists']]) + 1} focused files")
    print(f"  ✅ Main component reduced from {original['useState_count']} to {optimized['useState_count']} useState hooks")
    print(f"  ✅ Added useReducer for complex state management")
    print(f"  ✅ Memoization with useMemo and useCallback implemented")
    print(f"  ✅ Custom hooks for data management")
    print(f"  ✅ Reusable sub-components created")
    print(f"  ✅ Type safety improved with dedicated types file")
    print(f"  ✅ Utility functions extracted for reusability")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"  1. Replace the original Calendar component with CalendarOptimized")
    print(f"  2. Test all functionality to ensure nothing is broken")
    print(f"  3. Apply similar optimization to CashFlowAnalysis component")
    print(f"  4. Continue with UX improvements and accessibility")

def main():
    """Main function"""
    print("⚡ CALENDAR COMPONENT OPTIMIZATION ANALYSIS")
    print("=" * 50)
    print("Analyzing the results of the Calendar component optimization")
    print()
    
    analyze_optimization()
    
    print(f"\n🚀 OPTIMIZATION COMPLETE!")
    print("The Calendar component has been successfully optimized and modularized.")

if __name__ == '__main__':
    main()
