<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Investment Data Manager Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { margin: 5px; padding: 8px 16px; cursor: pointer; }
        .log { margin: 5px 0; padding: 5px; background: #f0f0f0; border-radius: 3px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>Investment Data Manager Integration Test</h1>
    <p>This test verifies the desktop app integration with Python storage for portfolio and stock holdings.</p>

    <div id="test-1" class="test-section info">
        <h3>Test 1: Environment Detection</h3>
        <button onclick="testEnvironment()">Test Environment</button>
        <div id="env-result"></div>
    </div>

    <div id="test-2" class="test-section info">
        <h3>Test 2: Create Portfolio</h3>
        <button onclick="testCreatePortfolio()">Create Test Portfolio</button>
        <div id="portfolio-result"></div>
    </div>

    <div id="test-3" class="test-section info">
        <h3>Test 3: Add Stock Holding</h3>
        <button onclick="testAddStock()">Add Test Stock</button>
        <div id="stock-result"></div>
    </div>

    <div id="test-4" class="test-section info">
        <h3>Test 4: Get Portfolio Holdings</h3>
        <button onclick="testGetHoldings()">Get Holdings</button>
        <div id="holdings-result"></div>
    </div>

    <div id="logs" class="test-section info">
        <h3>Console Logs</h3>
        <div id="log-container"></div>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>

    <script>
        let testPortfolioId = null;

        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function addLog(type, message) {
            const logContainer = document.getElementById('log-container');
            const logDiv = document.createElement('div');
            logDiv.className = 'log';
            logDiv.innerHTML = `<strong>[${type.toUpperCase()}]</strong> ${new Date().toLocaleTimeString()}: ${message}`;
            logContainer.appendChild(logDiv);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        console.log = function(...args) {
            addLog('log', args.join(' '));
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            addLog('error', args.join(' '));
            originalError.apply(console, args);
        };

        console.warn = function(...args) {
            addLog('warn', args.join(' '));
            originalWarn.apply(console, args);
        };

        function clearLogs() {
            document.getElementById('log-container').innerHTML = '';
        }

        function testEnvironment() {
            const resultDiv = document.getElementById('env-result');
            try {
                resultDiv.innerHTML = '<p>Testing environment...</p>';
                
                const isDesktop = !!window.pywebview;
                const hasAPI = !!window.pywebview?.api;
                const apiMethods = hasAPI ? Object.getOwnPropertyNames(window.pywebview.api) : [];
                
                console.log('Environment check:', {
                    isDesktop,
                    hasAPI,
                    apiMethods: apiMethods.slice(0, 10) // Show first 10 methods
                });

                const html = `
                    <div class="${isDesktop ? 'success' : 'error'}">
                        <h4>${isDesktop ? '✅' : '❌'} Desktop App Detection</h4>
                        <p><strong>PyWebView:</strong> ${isDesktop ? 'Detected' : 'Not detected'}</p>
                        <p><strong>API Available:</strong> ${hasAPI ? 'Yes' : 'No'}</p>
                        <p><strong>API Methods:</strong> ${apiMethods.length} methods available</p>
                        ${apiMethods.length > 0 ? `<p><strong>Sample methods:</strong> ${apiMethods.slice(0, 5).join(', ')}</p>` : ''}
                    </div>
                `;
                resultDiv.innerHTML = html;
                document.getElementById('test-1').className = `test-section ${isDesktop ? 'success' : 'error'}`;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Environment Test Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                document.getElementById('test-1').className = 'test-section error';
            }
        }

        async function testCreatePortfolio() {
            const resultDiv = document.getElementById('portfolio-result');
            try {
                resultDiv.innerHTML = '<p>Creating test portfolio...</p>';
                
                const testPortfolio = {
                    name: 'Test Portfolio ' + Date.now(),
                    description: 'Test portfolio for integration testing',
                    current_balance: 10000
                };

                console.log('Creating portfolio:', testPortfolio);

                if (window.pywebview?.api?.save_investment_portfolio) {
                    const result = await window.pywebview.api.save_investment_portfolio(testPortfolio);
                    console.log('Portfolio creation result:', result);
                    
                    let parsedResult;
                    if (typeof result === 'string') {
                        parsedResult = JSON.parse(result);
                    } else {
                        parsedResult = result;
                    }
                    
                    if (parsedResult.success) {
                        testPortfolioId = parsedResult.id || Date.now().toString();
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h4>✅ Portfolio Created Successfully</h4>
                                <p><strong>Portfolio ID:</strong> ${testPortfolioId}</p>
                                <p><strong>Name:</strong> ${testPortfolio.name}</p>
                                <pre>${JSON.stringify(parsedResult, null, 2)}</pre>
                            </div>
                        `;
                        document.getElementById('test-2').className = 'test-section success';
                    } else {
                        throw new Error(parsedResult.error || 'Unknown error');
                    }
                } else {
                    throw new Error('save_investment_portfolio method not available');
                }
            } catch (error) {
                console.error('Portfolio creation error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Portfolio Creation Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                document.getElementById('test-2').className = 'test-section error';
            }
        }

        async function testAddStock() {
            const resultDiv = document.getElementById('stock-result');
            try {
                if (!testPortfolioId) {
                    throw new Error('No test portfolio available. Please create a portfolio first.');
                }
                
                resultDiv.innerHTML = '<p>Adding test stock...</p>';
                
                const testStock = {
                    id: Date.now().toString(),
                    user_id: 'test_user',
                    portfolio_id: testPortfolioId,
                    symbol: 'COMI',
                    company_name: 'Commercial International Bank',
                    shares: 100,
                    purchase_price: 80,
                    purchase_date: new Date().toISOString(),
                    average_cost: 80,
                    total_cost: 8000,
                    current_price: 0,
                    current_value: 0,
                    unrealized_gain_loss: 0,
                    fees: 0,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                console.log('Adding stock:', testStock);

                if (window.pywebview?.api?.save_stock_holding) {
                    const result = await window.pywebview.api.save_stock_holding(testStock);
                    console.log('Stock addition result:', result);
                    
                    let parsedResult;
                    if (typeof result === 'string') {
                        parsedResult = JSON.parse(result);
                    } else {
                        parsedResult = result;
                    }
                    
                    if (parsedResult.success) {
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h4>✅ Stock Added Successfully</h4>
                                <p><strong>Stock:</strong> ${testStock.symbol} (${testStock.company_name})</p>
                                <p><strong>Shares:</strong> ${testStock.shares}</p>
                                <p><strong>Price:</strong> ${testStock.purchase_price} EGP</p>
                                <pre>${JSON.stringify(parsedResult, null, 2)}</pre>
                            </div>
                        `;
                        document.getElementById('test-3').className = 'test-section success';
                    } else {
                        throw new Error(parsedResult.error || 'Unknown error');
                    }
                } else {
                    throw new Error('save_stock_holding method not available');
                }
            } catch (error) {
                console.error('Stock addition error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Stock Addition Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                document.getElementById('test-3').className = 'test-section error';
            }
        }

        async function testGetHoldings() {
            const resultDiv = document.getElementById('holdings-result');
            try {
                if (!testPortfolioId) {
                    throw new Error('No test portfolio available. Please create a portfolio first.');
                }
                
                resultDiv.innerHTML = '<p>Getting portfolio holdings...</p>';
                
                console.log('Getting holdings for portfolio:', testPortfolioId);

                if (window.pywebview?.api?.get_portfolio_holdings) {
                    const result = await window.pywebview.api.get_portfolio_holdings(testPortfolioId);
                    console.log('Holdings result:', result);
                    
                    let holdings;
                    if (typeof result === 'string') {
                        holdings = JSON.parse(result);
                    } else {
                        holdings = result;
                    }
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Holdings Retrieved Successfully</h4>
                            <p><strong>Portfolio ID:</strong> ${testPortfolioId}</p>
                            <p><strong>Holdings Count:</strong> ${holdings.length}</p>
                            <pre>${JSON.stringify(holdings, null, 2)}</pre>
                        </div>
                    `;
                    document.getElementById('test-4').className = 'test-section success';
                } else {
                    throw new Error('get_portfolio_holdings method not available');
                }
            } catch (error) {
                console.error('Get holdings error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Get Holdings Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                document.getElementById('test-4').className = 'test-section error';
            }
        }

        // Auto-run environment test on page load
        window.addEventListener('load', () => {
            setTimeout(testEnvironment, 500);
        });
    </script>
</body>
</html>
