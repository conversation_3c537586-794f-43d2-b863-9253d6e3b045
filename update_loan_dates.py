#!/usr/bin/env python3

"""
Update Loan Next Payment Dates
===============================
The audit revealed most loans have old next payment dates (2022/2023).
This script will update them to current/future dates for proper cash flow projections.
"""

import sys
sys.path.append('.')
from data_storage import DataStorage
from datetime import datetime, timedelta
import calendar

def parse_date(date_str):
    """Parse date string to datetime object"""
    if not date_str:
        return None
    try:
        return datetime.fromisoformat(date_str.replace('Z', '+00:00')).replace(tzinfo=None)
    except:
        try:
            return datetime.strptime(date_str, '%Y-%m-%d')
        except:
            return None

def add_months_to_date(date_obj, months):
    """Add months to a date"""
    if not date_obj:
        return None
    month = date_obj.month - 1 + months
    year = date_obj.year + month // 12
    month = month % 12 + 1
    day = min(date_obj.day, calendar.monthrange(year, month)[1])
    return datetime(year, month, day)

def calculate_next_payment_date(start_date, end_date, current_date=None):
    """Calculate the next payment date for a loan"""
    if current_date is None:
        current_date = datetime.now()
    
    start = parse_date(start_date)
    end = parse_date(end_date)
    
    if not start or not end:
        return None
    
    # If loan has ended, no next payment
    if current_date > end:
        return None
    
    # Start from the loan start date and find the next payment after current date
    payment_date = start
    
    # Move to the first payment date after current date
    while payment_date <= current_date:
        payment_date = add_months_to_date(payment_date, 1)
        
        # Safety check - don't go past end date
        if payment_date and payment_date > end:
            return None
    
    return payment_date

def analyze_loan_dates():
    """Analyze current loan payment dates"""
    
    print("📅 LOAN DATES ANALYSIS")
    print("=" * 50)
    
    storage = DataStorage()
    loans = storage.get_collection('loans')
    current_date = datetime.now()
    
    print(f"Current Date: {current_date.strftime('%Y-%m-%d')}")
    print(f"Found {len(loans)} loans\n")
    
    issues_found = []
    
    for loan in loans:
        if not isinstance(loan, dict):
            continue
            
        name = loan.get('name', 'Unknown')
        start_date = loan.get('start_date')
        next_payment = loan.get('next_payment_date')
        end_date = loan.get('end_date')
        
        print(f"🏦 {name}")
        print(f"   Start: {start_date}")
        print(f"   Next Payment: {next_payment}")
        print(f"   End: {end_date}")
        
        # Parse dates
        next_payment_dt = parse_date(next_payment)
        end_date_dt = parse_date(end_date)
        
        # Check for issues
        issue_found = False
        
        if next_payment_dt and next_payment_dt < current_date:
            days_overdue = (current_date - next_payment_dt).days
            print(f"   ❌ Next payment is {days_overdue} days overdue!")
            issue_found = True
        
        if end_date_dt and current_date > end_date_dt:
            print(f"   ⚠️ Loan has ended - should not have next payment")
            issue_found = True
        
        if issue_found:
            # Calculate what the next payment should be
            correct_next = calculate_next_payment_date(start_date, end_date, current_date)
            if correct_next:
                print(f"   💡 Should be: {correct_next.strftime('%Y-%m-%d')}")
                issues_found.append({
                    'loan': loan,
                    'name': name,
                    'current_next': next_payment,
                    'correct_next': correct_next.strftime('%Y-%m-%d')
                })
            else:
                print(f"   💡 Should be: None (loan ended)")
                issues_found.append({
                    'loan': loan,
                    'name': name,
                    'current_next': next_payment,
                    'correct_next': None
                })
        else:
            print(f"   ✅ Dates appear correct")
        
        print("-" * 30)
    
    return issues_found

def fix_loan_dates():
    """Fix the loan next payment dates"""
    
    issues = analyze_loan_dates()
    
    if not issues:
        print("\n✅ No loan date issues found!")
        return
    
    print(f"\n🔧 FIXING LOAN DATES")
    print("=" * 50)
    print(f"Found {len(issues)} loans with date issues")
    
    storage = DataStorage()
    fixes_applied = []
    
    for issue in issues:
        loan = issue['loan']
        name = issue['name']
        correct_next = issue['correct_next']
        
        try:
            # Update the loan with correct next payment date
            loan_id = loan.get('id')
            if not loan_id:
                print(f"❌ {name}: No loan ID found")
                continue
            
            # Update the loan object
            updated_loan = loan.copy()
            updated_loan['next_payment_date'] = correct_next if correct_next else None
            
            # Save to storage
            storage.set_item(f"loans/{loan_id}", updated_loan)
            
            if correct_next:
                print(f"✅ {name}: Updated next payment to {correct_next}")
                fixes_applied.append(f"{name}: {correct_next}")
            else:
                print(f"✅ {name}: Cleared next payment (loan ended)")
                fixes_applied.append(f"{name}: Cleared (ended)")
                
        except Exception as e:
            print(f"❌ {name}: Failed to update - {e}")
    
    if fixes_applied:
        print(f"\n🎉 FIXES APPLIED:")
        for fix in fixes_applied:
            print(f"  ✅ {fix}")
        
        print(f"\n🔄 Verifying fixes...")
        # Re-run analysis to verify
        remaining_issues = analyze_loan_dates()
        if not remaining_issues:
            print(f"\n✅ All loan date issues fixed!")
        else:
            print(f"\n⚠️ {len(remaining_issues)} issues remain")
    else:
        print(f"\n❌ No fixes could be applied")

def main():
    """Main function"""
    print("🛠️ LOAN DATES UPDATE")
    print("=" * 50)
    print("This script will update loan next payment dates to current/future dates")
    print()
    
    try:
        fix_loan_dates()
        print(f"\n✅ Loan dates update completed!")
        
    except Exception as e:
        print(f"\n❌ Error during update: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
