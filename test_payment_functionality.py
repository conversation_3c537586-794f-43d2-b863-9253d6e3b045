#!/usr/bin/env python3
"""
Test Payment Management Functionality
=====================================

This script tests that payment management and cash flow projections
work correctly with our fixed loan dates.
"""

import sys
import os
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_storage import DataStorage

def test_payment_calculations():
    """Test that payment calculations work with the corrected loan dates"""
    print("Testing Payment Management Functionality")
    print("=" * 50)
    
    # Initialize storage
    storage = DataStorage()
    
    # Get all loans
    loans = storage.get_collection('loans')
    print(f"Found {len(loans)} loans to test")
    print()
    
    current_date = datetime.now()
    
    for loan in loans:
        print(f"Testing loan: {loan['name']}")
        print(f"  Principal: {loan['principal']:,}")
        print(f"  Interest Rate: {loan['interest_rate']}%")
        print(f"  Term: {loan['term_months']} months")
        print(f"  Start Date: {loan['start_date']}")
        print(f"  Next Payment: {loan['next_payment_date']}")
        print(f"  End Date: {loan['end_date']}")
        
        # Parse dates
        try:
            start_date = datetime.strptime(loan['start_date'], '%Y-%m-%d')
            next_payment = datetime.strptime(loan['next_payment_date'], '%Y-%m-%d')
            end_date = datetime.strptime(loan['end_date'], '%Y-%m-%d')
            
            # Calculate expected monthly payment
            principal = loan['principal']
            annual_rate = loan['interest_rate'] / 100
            monthly_rate = annual_rate / 12
            num_payments = loan['term_months']
            
            if monthly_rate > 0:
                monthly_payment = principal * (monthly_rate * (1 + monthly_rate)**num_payments) / ((1 + monthly_rate)**num_payments - 1)
            else:
                monthly_payment = principal / num_payments
            
            print(f"  Calculated Monthly Payment: {monthly_payment:,.2f}")
            
            # Test date logic
            months_elapsed = (current_date.year - start_date.year) * 12 + (current_date.month - start_date.month)
            remaining_months = max(0, loan['term_months'] - months_elapsed)
            
            print(f"  Months Elapsed: {months_elapsed}")
            print(f"  Remaining Months: {remaining_months}")
            
            # Calculate remaining balance
            if monthly_rate > 0 and remaining_months > 0:
                remaining_balance = principal * (((1 + monthly_rate)**num_payments - (1 + monthly_rate)**months_elapsed) / 
                                               ((1 + monthly_rate)**num_payments - 1))
            else:
                remaining_balance = 0
                
            print(f"  Estimated Remaining Balance: {remaining_balance:,.2f}")
            
            # Validate dates
            calculated_end = start_date + relativedelta(months=loan['term_months'])
            print(f"  Expected End Date: {calculated_end.strftime('%Y-%m-%d')}")
            print(f"  Stored End Date: {loan['end_date']}")
            
            if calculated_end.strftime('%Y-%m-%d') == loan['end_date']:
                print("  ✅ End date is correct")
            else:
                print("  ❌ End date mismatch")
                
            # Check if loan is still active
            if current_date < end_date:
                print("  📅 Loan is still active")
                if current_date >= next_payment:
                    print("  💰 Payment is due or overdue")
                else:
                    days_until_payment = (next_payment - current_date).days
                    print(f"  ⏰ Next payment in {days_until_payment} days")
            else:
                print("  ✅ Loan is fully paid")
                
        except Exception as e:
            print(f"  ❌ Error processing loan: {e}")
            
        print("-" * 30)
        
def test_cash_flow_projection():
    """Test cash flow projections with the corrected dates"""
    print("\nTesting Cash Flow Projections")
    print("=" * 50)
    
    storage = DataStorage()
    loans = storage.get_collection('loans')
    
    current_date = datetime.now()
    projection_months = 12
    
    print(f"Projecting cash flow for next {projection_months} months")
    print(f"Starting from: {current_date.strftime('%Y-%m-%d')}")
    print()
    
    monthly_payments = {}
    
    for month_offset in range(projection_months):
        projection_date = current_date + relativedelta(months=month_offset)
        month_key = projection_date.strftime('%Y-%m')
        monthly_payments[month_key] = 0
        
        for loan in loans:
            try:
                start_date = datetime.strptime(loan['start_date'], '%Y-%m-%d')
                end_date = datetime.strptime(loan['end_date'], '%Y-%m-%d')
                
                # Check if loan is active in this month
                if start_date <= projection_date <= end_date:
                    # Calculate monthly payment
                    principal = loan['principal']
                    annual_rate = loan['interest_rate'] / 100
                    monthly_rate = annual_rate / 12
                    num_payments = loan['term_months']
                    
                    if monthly_rate > 0:
                        monthly_payment = principal * (monthly_rate * (1 + monthly_rate)**num_payments) / ((1 + monthly_rate)**num_payments - 1)
                    else:
                        monthly_payment = principal / num_payments
                        
                    monthly_payments[month_key] += monthly_payment
                    
            except Exception as e:
                print(f"Error processing loan {loan['name']} for {month_key}: {e}")
    
    # Display projection
    print("Monthly Loan Payment Projections:")
    for month_key, total_payment in monthly_payments.items():
        print(f"  {month_key}: {total_payment:,.2f}")
        
    total_projected = sum(monthly_payments.values())
    print(f"\nTotal projected payments over {projection_months} months: {total_projected:,.2f}")
    print(f"Average monthly payment: {total_projected / projection_months:,.2f}")

if __name__ == "__main__":
    try:
        test_payment_calculations()
        test_cash_flow_projection()
        print("\n✅ All payment functionality tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
