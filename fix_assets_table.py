#!/usr/bin/env python3

"""
Fix Assets Table - Manual Asset Addition
=======================================
Add the missing liquid assets (CDs and Cash) to the Assets table
to resolve the data integrity issue.
"""

import sys
sys.path.append('.')
from data_storage import DataStorage
import json
from datetime import datetime

def add_missing_assets_to_table():
    """Add missing liquid assets to the Assets table"""
    
    print("🔧 ADDING MISSING ASSETS TO ASSETS TABLE")
    print("=" * 50)
    
    storage = DataStorage()
    
    # Get current data
    bank_accounts = storage.get_collection('bank_accounts')
    cds = storage.get_collection('cds')
    assets = storage.get_collection('assets')
    
    # Calculate totals
    total_bank_balance = sum(float(acc.get('balance', 0)) for acc in bank_accounts)
    total_cd_value = sum(float(cd.get('principal', 0)) for cd in cds)
    
    print(f"💰 Current Bank Balance: ${total_bank_balance:,.2f}")
    print(f"💿 Current CD Value: ${total_cd_value:,.2f}")
    
    # Check what's already in assets
    print(f"\n📋 Current Assets Table:")
    existing_value = 0
    has_cash = False
    has_cds = False
    
    for asset in assets:
        name = asset.get('name', 'Unknown')
        value = float(asset.get('value', 0))
        category = asset.get('category', 'Unknown')
        existing_value += value
        
        print(f"  • {name} ({category}): ${value:,.2f}")
        
        if 'cash' in name.lower() or category.lower() == 'cash':
            has_cash = True
        if 'cd' in name.lower() or 'certificate' in name.lower() or category.lower() == 'investments':
            has_cds = True
    
    print(f"\nTotal existing assets: ${existing_value:,.2f}")
    print(f"Has Cash assets: {'Yes' if has_cash else 'No'}")
    print(f"Has CD assets: {'Yes' if has_cds else 'No'}")
    
    # Add missing assets
    assets_to_add = []
    
    if not has_cds and total_cd_value > 0:
        cd_asset = {
            'id': f"cd-consolidated-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            'user_id': 'default-user',
            'name': 'Certificate of Deposits (CDs)',
            'category': 'Investments',
            'value': total_cd_value,
            'acquisition_date': datetime.now().strftime('%Y-%m-%d'),
            'notes': 'Consolidated CD holdings from CD management page',
            'created_at': datetime.now().isoformat()
        }
        assets_to_add.append(cd_asset)
    
    if not has_cash and total_bank_balance > 0:
        cash_asset = {
            'id': f"cash-consolidated-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            'user_id': 'default-user',
            'name': 'Cash and Bank Accounts',
            'category': 'Cash',
            'value': total_bank_balance,
            'acquisition_date': datetime.now().strftime('%Y-%m-%d'),
            'notes': 'Consolidated cash holdings from bank accounts',
            'created_at': datetime.now().isoformat()
        }
        assets_to_add.append(cash_asset)
    
    if not assets_to_add:
        print(f"\n✅ No missing assets to add - Assets table appears complete")
        return True
    
    print(f"\n📝 Adding {len(assets_to_add)} missing assets...")
    
    success_count = 0
    for asset in assets_to_add:
        try:
            # Convert to JSON string for storage
            asset_json = json.dumps(asset)
            storage.set_item(f"assets/{asset['id']}", asset_json)
            
            print(f"  ✅ Added {asset['name']}: ${asset['value']:,.2f}")
            success_count += 1
            
        except Exception as e:
            print(f"  ❌ Failed to add {asset['name']}: {e}")
    
    print(f"\n🎉 Successfully added {success_count}/{len(assets_to_add)} assets")
    
    # Verify the fix
    print(f"\n🔄 Verifying Assets Table...")
    updated_assets = storage.get_collection('assets')
    new_total = sum(float(asset.get('value', 0)) for asset in updated_assets)
    liquid_total = total_bank_balance + total_cd_value
    
    print(f"  📊 Updated Assets Total: ${new_total:,.2f}")
    print(f"  💵 Liquid Assets Total: ${liquid_total:,.2f}")
    
    if new_total >= liquid_total:
        print(f"  ✅ SUCCESS: Assets ≥ Liquid Assets!")
        return True
    else:
        discrepancy = liquid_total - new_total
        print(f"  ⚠️ Still missing: ${discrepancy:,.2f}")
        return False

def main():
    """Main function"""
    print("🛠️ ASSETS TABLE FIX")
    print("=" * 50)
    print("Adding missing liquid assets to resolve data integrity issue")
    print()
    
    try:
        success = add_missing_assets_to_table()
        
        if success:
            print(f"\n✅ Assets table fix completed successfully!")
            print(f"🎉 Phase 1 critical issues should now be resolved!")
        else:
            print(f"\n⚠️ Assets table fix partially completed")
            print(f"Manual review may be needed")
            
    except Exception as e:
        print(f"\n❌ Assets table fix failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
