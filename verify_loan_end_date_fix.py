#!/usr/bin/env python3

"""
Script to verify that the Loans page will now show correct end dates.
Compare what was being calculated vs what should be displayed.
"""

import sys
import os
from datetime import datetime, timedelta
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_storage import DataStorage

def parse_date(date_str):
    """Parse date string to datetime object"""
    if not date_str:
        return None
    try:
        return datetime.fromisoformat(date_str.replace('Z', '+00:00')).replace(tzinfo=None)
    except:
        try:
            return datetime.strptime(date_str, '%Y-%m-%d')
        except:
            return None

def add_months_to_date(date_obj, months):
    """Add months to a date"""
    if not date_obj:
        return None
    month = date_obj.month - 1 + months
    year = date_obj.year + month // 12
    month = month % 12 + 1
    day = min(date_obj.day, [31,29,31,30,31,30,31,31,30,31,30,31][month-1])
    return datetime(year, month, day)

def main():
    print("=== Loans Page End Date Fix Verification ===\n")
    
    storage = DataStorage()
    loans = storage.get_collection('loans')
    
    print(f"Found {len(loans)} loans\n")
    
    for loan in loans:
        name = loan.get('name', 'Unknown')
        next_payment_date = loan.get('next_payment_date')
        term_months = loan.get('term_months', 0)
        stored_end_date = loan.get('end_date')
        
        # Calculate what the OLD code was doing (incorrect)
        next_payment_parsed = parse_date(next_payment_date)
        old_calculated_end = add_months_to_date(next_payment_parsed, term_months)
        
        # What the NEW code will use (correct)
        stored_end_parsed = parse_date(stored_end_date)
        
        print(f"📋 {name}")
        print(f"   Next Payment Date: {next_payment_date}")
        print(f"   Term: {term_months} months")
        print(f"   Stored End Date: {stored_end_date}")
        print()
        print(f"   ❌ OLD Logic (next_payment + term): {old_calculated_end.strftime('%b %d, %Y') if old_calculated_end else 'N/A'}")
        print(f"   ✅ NEW Logic (stored end_date): {stored_end_parsed.strftime('%b %d, %Y') if stored_end_parsed else 'N/A'}")
        
        if old_calculated_end and stored_end_parsed:
            if old_calculated_end.date() != stored_end_parsed.date():
                print(f"   🔧 FIXED: Was showing wrong date!")
            else:
                print(f"   ✓ Already correct")
        
        print("-" * 50)

if __name__ == "__main__":
    main()
