<!DOCTYPE html>
<html>
<head>
    <title>Direct Search Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .price { font-size: 18px; font-weight: bold; color: blue; }
        .change { color: green; }
    </style>
</head>
<body>
    <h1>Direct EGX Search Test</h1>
    <input type="text" id="search" placeholder="Enter stock symbol" value="COMI">
    <button onclick="testSearch()">Search</button>
    <div id="results"></div>

    <script>
        async function testSearch() {
            const query = document.getElementById('search').value;
            const resultsDiv = document.getElementById('results');
            
            try {
                console.log('🔍 Searching for:', query);
                const response = await fetch(`http://localhost:3000/search?q=${query}`);
                const data = await response.json();
                
                console.log('📊 API Response:', data);
                
                resultsDiv.innerHTML = data.map(stock => `
                    <div class="result">
                        <strong>${stock.symbol}</strong> - ${stock.name}<br>
                        <span class="price">Price: ${stock.price} EGP</span><br>
                        <span class="change">Change: ${stock.change} (${stock.changePercent}%)</span><br>
                        <small>Volume: ${stock.volume} | Source: ${stock.source}</small>
                    </div>
                `).join('');
                
            } catch (error) {
                console.error('❌ Error:', error);
                resultsDiv.innerHTML = `<div class="result" style="color: red;">Error: ${error.message}</div>`;
            }
        }
        
        // Auto-search on load
        window.onload = () => testSearch();
    </script>
</body>
</html>
