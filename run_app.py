import webview
import os
import sys
import subprocess
import threading
import time
import signal
import webbrowser
from http.client import HTTPConnection
import socket

# Paths
current_dir = os.path.dirname(os.path.abspath(__file__))

# App configuration
APP_NAME = "Advanced Trading Advisor"
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
VITE_PORT = 5173  # Default Vite port

def is_port_in_use(port):
    """Check if a port is already in use"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0
        
def find_available_port():
    """Find an available port starting from the default port"""
    port = VITE_PORT
    while is_port_in_use(port):
        port += 1
    return port

def wait_for_server(host, port, timeout=60):
    """Wait for the development server to become available"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            conn = HTTPConnection(host, port, timeout=1)
            conn.request("HEAD", "/")
            response = conn.getresponse()
            return True
        except:
            time.sleep(0.5)
    return False

def run_dev_server():
    """Run the Vite development server in a separate process"""
    try:
        # Find an available port
        port = find_available_port()
        
        # Set environment variables for the dev server
        env = os.environ.copy()
        env["VITE_PORT"] = str(port)
        
        # Start the Vite dev server
        if sys.platform == 'win32':
            process = subprocess.Popen(
                ["npm", "run", "dev", "--", "--port", str(port)],
                cwd=current_dir,
                env=env,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
        else:
            process = subprocess.Popen(
                ["npm", "run", "dev", "--", "--port", str(port)],
                cwd=current_dir,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
        
        # Wait for server to start
        if wait_for_server("localhost", port):
            return process, f"http://localhost:{port}"
        else:
            raise Exception("Development server failed to start")
            
    except Exception as e:
        print(f"Error starting development server: {e}")
        sys.exit(1)

def launch_app():
    """Launch the desktop application"""
    # Start the dev server
    server_process, url = run_dev_server()
    
    try:
        # Create the window
        window = webview.create_window(
            APP_NAME, 
            url, 
            width=WINDOW_WIDTH, 
            height=WINDOW_HEIGHT,
            min_size=(800, 600),
            text_select=True
        )
        
        # Start the window
        webview.start(debug=False)
    finally:
        # Make sure to terminate the server when the window is closed
        if server_process:
            server_process.terminate()
            
            # On Windows, we need to make sure the process tree is terminated
            if sys.platform == 'win32':
                subprocess.call(['taskkill', '/F', '/T', '/PID', str(server_process.pid)])
            else:
                os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)

if __name__ == "__main__":
    launch_app()