import os
import json
import sys
from data_storage import DataStorage

"""
Storage Index Repair Tool
-------------------------
This script fixes issues with the Financial Advisor storage system by:
1. Cleaning up indexes to remove phantom references
2. Repairing any other storage inconsistencies
"""

def main():
    print("Financial Advisor - Storage Index Repair Tool")
    print("=============================================")
    
    try:
        # Initialize the data storage
        print("Initializing data storage...")
        storage = DataStorage()
        
        # Get the data directory
        data_dir = storage.data_dir
        print(f"Data directory: {data_dir}")
        
        # Create a backup first
        print("Creating backup before repairs...")
        backup_path = storage._create_backup()
        if backup_path:
            print(f"Backup created at: {backup_path}")
        else:
            print("Warning: Failed to create backup")
        
        # Clean indexes to remove phantom references
        print("\nStep 1: Cleaning indexes to remove phantom references...")
        clean_result = storage.clean_indexes()
        try:
            clean_result_json = json.loads(clean_result)
            if clean_result_json.get('success'):
                print(f"Success: {clean_result_json.get('message')}")
            else:
                print(f"Error: {clean_result_json.get('error')}")
        except:
            print(f"Raw result: {clean_result}")
        
        # Repair storage
        print("\nStep 2: Repairing storage inconsistencies...")
        repair_result = storage.repair_storage()
        try:
            repair_result_json = json.loads(repair_result)
            if repair_result_json.get('success'):
                print(f"Success: {repair_result_json.get('message')}")
            else:
                print(f"Error: {repair_result_json.get('error')}")
        except:
            print(f"Raw result: {repair_result}")
        
        # Get storage stats
        print("\nStep 3: Getting storage statistics...")
        stats_result = storage.get_storage_stats()
        try:
            stats_json = json.loads(stats_result)
            if stats_json.get('success'):
                print(f"Total items: {stats_json.get('total_items')}")
                print("Collections:")
                for collection, count in stats_json.get('collections', {}).items():
                    print(f"  - {collection}: {count} items")
            else:
                print(f"Error: {stats_json.get('error')}")
        except:
            print(f"Raw result: {stats_result}")
        
        print("\nStorage repair completed successfully!")
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
