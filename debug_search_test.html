<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Search Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input { padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 3px; width: 200px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Frontend Search Debug Test</h1>
    
    <div class="test-section">
        <h2>1. Test Direct API Call</h2>
        <input type="text" id="directQuery" value="VALU" placeholder="Stock symbol">
        <button onclick="testDirectAPI()">Test Direct API</button>
        <div id="directResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Frontend Service</h2>
        <input type="text" id="serviceQuery" value="VALU" placeholder="Stock symbol">
        <button onclick="testFrontendService()">Test Frontend Service</button>
        <div id="serviceResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Simulate Stock Conversion</h2>
        <button onclick="testStockConversion()">Test Stock Conversion</button>
        <div id="conversionResult" class="result"></div>
    </div>

    <script>
        // Direct API test
        async function testDirectAPI() {
            const query = document.getElementById('directQuery').value;
            const resultDiv = document.getElementById('directResult');
            
            try {
                console.log('🔍 Testing direct API call for:', query);
                const response = await fetch(`http://localhost:3000/search?q=${encodeURIComponent(query)}`);
                const data = await response.json();
                
                console.log('📋 Direct API response:', data);
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ Direct API Success</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                        <p><strong>Price:</strong> ${data.price} (type: ${typeof data.price})</p>
                    </div>
                `;
            } catch (error) {
                console.error('❌ Direct API error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Direct API Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Frontend service test
        async function testFrontendService() {
            const query = document.getElementById('serviceQuery').value;
            const resultDiv = document.getElementById('serviceResult');
            
            try {
                console.log('🔍 Testing frontend service for:', query);
                
                // Simulate the stockDataService.searchStocks call
                const response = await fetch(`http://localhost:3000/search?q=${encodeURIComponent(query)}`);
                const apiData = await response.json();
                
                console.log('📋 API data before conversion:', apiData);
                
                // Convert to Stock object (simulate what stockDataService does)
                const stockData = {
                    id: `stock_${apiData.symbol}`,
                    symbol: apiData.symbol,
                    name: apiData.name,
                    price: apiData.price,
                    change: apiData.change || 0,
                    changePercent: apiData.changePercent || 0,
                    volume: apiData.volume || 0,
                    open: apiData.open || apiData.price,
                    high: apiData.price * 1.02,
                    low: apiData.price * 0.98,
                    recommendation: 'Hold',
                    ai_score: 7.5,
                    analysis: '',
                    risk_level: 'medium',
                    pe_ratio: 15.0,
                    market_cap: **********,
                    dividend_yield: 3.5,
                    sector: 'Banking',
                    last_updated: new Date().toISOString()
                };
                
                console.log('📋 Converted stock data:', stockData);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ Frontend Service Success</h4>
                        <pre>${JSON.stringify(stockData, null, 2)}</pre>
                        <p><strong>Price:</strong> ${stockData.price} (type: ${typeof stockData.price})</p>
                        <p><strong>Formatted Price:</strong> ${stockData.price ? stockData.price.toFixed(2) : 'N/A'} EGP</p>
                    </div>
                `;
            } catch (error) {
                console.error('❌ Frontend service error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Frontend Service Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Test stock conversion with various data types
        async function testStockConversion() {
            const resultDiv = document.getElementById('conversionResult');
            
            try {
                // Test different price formats
                const testCases = [
                    { price: 83.9, desc: "Number" },
                    { price: "83.9", desc: "String number" },
                    { price: null, desc: "Null" },
                    { price: undefined, desc: "Undefined" },
                    { price: "N/A", desc: "String N/A" },
                    { price: 0, desc: "Zero" }
                ];
                
                let results = testCases.map(test => {
                    const numPrice = parseFloat(test.price);
                    const isValid = !isNaN(numPrice) && numPrice > 0;
                    
                    return {
                        input: test.price,
                        type: typeof test.price,
                        description: test.desc,
                        parsed: numPrice,
                        isValid: isValid,
                        formatted: isValid ? `${numPrice.toFixed(2)} EGP` : 'N/A'
                    };
                });
                
                console.log('🧪 Stock conversion test results:', results);
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ Stock Conversion Test</h4>
                        <table border="1" style="border-collapse: collapse; width: 100%;">
                            <tr>
                                <th>Input</th>
                                <th>Type</th>
                                <th>Description</th>
                                <th>Parsed</th>
                                <th>Valid</th>
                                <th>Formatted</th>
                            </tr>
                            ${results.map(r => `
                                <tr>
                                    <td>${r.input}</td>
                                    <td>${r.type}</td>
                                    <td>${r.description}</td>
                                    <td>${r.parsed}</td>
                                    <td>${r.isValid ? '✅' : '❌'}</td>
                                    <td>${r.formatted}</td>
                                </tr>
                            `).join('')}
                        </table>
                    </div>
                `;
            } catch (error) {
                console.error('❌ Conversion test error:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Conversion Test Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Auto-run tests on page load
        window.onload = function() {
            console.log('🧪 Debug Search Test Page Loaded');
            testDirectAPI();
        };
    </script>
</body>
</html>
