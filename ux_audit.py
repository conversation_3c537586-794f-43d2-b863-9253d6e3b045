#!/usr/bin/env python3

import os
import re

def ux_audit():
    """Audit user experience and discoverability issues"""
    
    print("🎨 USER EXPERIENCE AUDIT")
    print("=" * 50)
    
    # Check navigation structure
    print("\n1️⃣ NAVIGATION AUDIT")
    print("-" * 30)
    
    layout_path = 'src/components/Layout.tsx'
    if os.path.exists(layout_path):
        with open(layout_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Count navigation items
        nav_items = re.findall(r"name: '[^']*'", content)
        print(f"📊 Navigation items found: {len(nav_items)}")
        
        for item in nav_items:
            clean_item = item.replace('name: ', '').strip("'")
            print(f"  • {clean_item}")
            
        # Check for logical grouping
        if 'separator' in content.lower() or 'divider' in content.lower():
            print("✅ Navigation has logical grouping")
        else:
            print("⚠️  Navigation could benefit from grouping")
            
    # Check for help/documentation
    print("\n2️⃣ HELP & DISCOVERABILITY AUDIT")
    print("-" * 30)
    
    tsx_files = []
    for root, dirs, files in os.walk('src'):
        for file in files:
            if file.endswith('.tsx'):
                tsx_files.append(os.path.join(root, file))
    
    help_features = {
        'tooltips': 0,
        'help_text': 0,
        'placeholders': 0,
        'loading_states': 0,
        'error_messages': 0
    }
    
    for file_path in tsx_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().lower()
                
            if 'tooltip' in content or 'title=' in content:
                help_features['tooltips'] += 1
            if 'help' in content or 'info' in content:
                help_features['help_text'] += 1
            if 'placeholder' in content:
                help_features['placeholders'] += 1
            if 'loading' in content or 'spinner' in content:
                help_features['loading_states'] += 1
            if 'error' in content or 'warning' in content:
                help_features['error_messages'] += 1
                
        except Exception:
            continue
    
    print("📋 UX Features Found:")
    for feature, count in help_features.items():
        status = "✅" if count > 5 else "⚠️" if count > 0 else "❌"
        print(f"  {status} {feature.replace('_', ' ').title()}: {count} files")
    
    print("\n3️⃣ ACCESSIBILITY AUDIT")
    print("-" * 30)
    
    accessibility_features = {
        'aria_labels': 0,
        'alt_text': 0,
        'focus_management': 0,
        'keyboard_nav': 0
    }
    
    for file_path in tsx_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'aria-' in content:
                accessibility_features['aria_labels'] += 1
            if 'alt=' in content:
                accessibility_features['alt_text'] += 1
            if 'focus' in content.lower() or 'blur' in content.lower():
                accessibility_features['focus_management'] += 1
            if 'onkeydown' in content.lower() or 'onkeypress' in content.lower():
                accessibility_features['keyboard_nav'] += 1
                
        except Exception:
            continue
    
    print("♿ Accessibility Features:")
    for feature, count in accessibility_features.items():
        status = "✅" if count > 3 else "⚠️" if count > 0 else "❌"
        print(f"  {status} {feature.replace('_', ' ').title()}: {count} files")
    
    # Check advanced page discoverability
    print("\n4️⃣ ADVANCED FEATURES DISCOVERABILITY")
    print("-" * 30)
    
    advanced_pages = [
        'src/pages/Reports.tsx',
        'src/pages/DebtPayoffSimulator.tsx',
        'src/pages/FinancialForecast.tsx',
        'src/pages/FinancialTimeline.tsx',
        'src/pages/TimelineProjections.tsx'
    ]
    
    for page_path in advanced_pages:
        if os.path.exists(page_path):
            page_name = os.path.basename(page_path).replace('.tsx', '')
            
            with open(page_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for user guidance features
            has_description = any(term in content.lower() for term in ['description', 'subtitle', 'help'])
            has_examples = any(term in content.lower() for term in ['example', 'sample', 'demo'])
            has_tooltips = 'tooltip' in content.lower() or 'title=' in content
            has_loading = 'loading' in content.lower() or 'spinner' in content.lower()
            
            print(f"📄 {page_name}:")
            print(f"  {'✅' if has_description else '❌'} Page description/help")
            print(f"  {'✅' if has_examples else '❌'} Examples/samples")
            print(f"  {'✅' if has_tooltips else '❌'} Tooltips/guidance")
            print(f"  {'✅' if has_loading else '❌'} Loading states")
    
    print(f"\n🎯 UX AUDIT COMPLETE")

if __name__ == '__main__':
    ux_audit()
