#!/usr/bin/env python3
"""
Financial Advisor - Unified Startup Script
==========================================

Single entry point for all Financial Advisor functionality.
Replaces the 12+ startup scripts with a simple, unified interface.

Usage:
    python start.py dev             # Web development server
    python start.py desktop         # Desktop app (PyWebView)
    python start.py electron        # Electron desktop app
    python start.py cli             # Python CLI version
    python start.py build           # Build for production
    python start.py --help          # Show help

Author: Financial Advisor Team
Date: June 25, 2025
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path

def check_requirements():
    """Check if basic requirements are met"""
    issues = []
    
    # Check for Node.js/npm
    try:
        subprocess.run(["npm", "--version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        issues.append("❌ npm not found. Please install Node.js and npm.")
    
    # Check for Python dependencies
    try:
        import webview
    except ImportError:
        issues.append("⚠️  PyWebView not installed. Desktop mode will not work. Install with: pip install pywebview")
    
    # Check for package.json
    if not Path("package.json").exists():
        issues.append("❌ package.json not found. Are you in the correct directory?")
    
    if issues:
        print("🔍 Requirements Check:")
        for issue in issues:
            print(f"  {issue}")
        print()
    else:
        print("✅ All requirements check passed")
    
    return len(issues) == 0

def run_web_dev():
    """Start the web development server"""
    print("🌐 Starting Financial Advisor Web Development Server...")
    try:
        subprocess.run(["npm", "run", "dev"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start web dev server: {e}")
        return False
    except FileNotFoundError:
        print("❌ npm not found. Please install Node.js and npm.")
        return False
    return True

def run_desktop():
    """Start the desktop application"""
    print("💻 Starting Financial Advisor Desktop App...")
    try:
        # Use the simplified app launcher which has the most reliable implementation
        subprocess.run(["python", "run-simplified-app.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start desktop app: {e}")
        return False
    except FileNotFoundError:
        print("❌ run-simplified-app.py not found.")
        return False
    return True

def run_electron():
    """Start the Electron desktop application"""
    print("⚡ Starting Financial Advisor Electron App...")
    try:
        # Try the app launcher first
        if Path("app-launcher.js").exists():
            subprocess.run(["node", "app-launcher.js"], check=True)
        else:
            # Fallback to npm script
            subprocess.run(["npm", "run", "electron"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start Electron app: {e}")
        return False
    except FileNotFoundError:
        print("❌ Node.js not found. Please install Node.js.")
        return False
    return True

def run_python_cli():
    """Start the Python CLI version"""
    print("🐍 Starting Financial Advisor Python CLI...")
    try:
        subprocess.run(["python", "Loans/main.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start Python CLI: {e}")
        return False
    except FileNotFoundError:
        print("❌ Loans/main.py not found.")
        return False
    return True

def build_app():
    """Build the application for production"""
    print("🔨 Building Financial Advisor for production...")
    try:
        subprocess.run(["npm", "run", "build"], check=True)
        print("✅ Build completed successfully!")
        print("📁 Built files are in the 'dist' directory")
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        return False
    except FileNotFoundError:
        print("❌ npm not found. Please install Node.js and npm.")
        return False
    return True

def run_electron():
    """Start the Electron desktop application"""
    print("⚡ Starting Financial Advisor Electron App...")
    try:
        # Try the app launcher first
        if Path("app-launcher.js").exists():
            subprocess.run(["node", "app-launcher.js"], check=True)
        else:
            # Fallback to npm script
            subprocess.run(["npm", "run", "electron"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start Electron app: {e}")
        return False
    except FileNotFoundError:
        print("❌ Node.js not found. Please install Node.js.")
        return False
    return True

def main():
    parser = argparse.ArgumentParser(
        description="Financial Advisor - Unified Application Launcher",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python start.py dev             # Start web development server
    python start.py desktop         # Start desktop app (recommended)
    python start.py electron        # Start Electron desktop app
    python start.py cli             # Start Python CLI version
    python start.py build           # Build for production

Note: This replaces all the old .bat files with a single entry point.
        """
    )
    
    parser.add_argument(
        "command", 
        choices=["dev", "desktop", "electron", "cli", "build"], 
        help="Command to run"
    )
    
    parser.add_argument(
        "--skip-check",
        action="store_true",
        help="Skip requirements check"
    )

    args = parser.parse_args()
    
    # Check requirements unless skipped
    if not args.skip_check:
        if not check_requirements():
            print("⚠️  Some requirements are missing. You can continue with --skip-check")
            print("   but some features may not work properly.")
            response = input("Continue anyway? (y/N): ")
            if response.lower() != 'y':
                sys.exit(1)
    
    print(f"🚀 Financial Advisor Launcher - Mode: {args.command}")
    print(f"📁 Working directory: {Path.cwd()}")
    print()
    
    success = False
    try:
        if args.command == "dev":
            success = run_web_dev()
        elif args.command == "desktop":
            success = run_desktop()
        elif args.command == "electron":
            success = run_electron()
        elif args.command == "cli":
            success = run_python_cli()
        elif args.command == "build":
            success = build_app()
        
        if success:
            print("\n✅ Command completed successfully!")
        else:
            print("\n❌ Command failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
