@echo off
echo =====================================
echo FINANCIAL ADVISOR - CLEAN RESTART
echo =====================================
echo.
echo This script will:
echo - Clear application caches
echo - Restart the application with all date normalization disabled
echo.
echo Press any key to continue...
pause > nul

:: Clear caches
echo.
echo Clearing caches...
if exist "%USERPROFILE%\.financial_advisor\cache" (
    echo Removing cache directory...
    rmdir /s /q "%USERPROFILE%\.financial_advisor\cache"
)

:: Run the app
echo.
echo Starting application with date normalization disabled...
call START-HERE.bat
