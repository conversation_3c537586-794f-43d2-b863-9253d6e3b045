#!/usr/bin/env python3
"""
Clean Invalid Paid Months
=========================
Remove paid month records that are before the loan start date.
"""

import sys
sys.path.append('.')
from data_storage import DataStorage
from datetime import datetime

def clean_invalid_paid_months():
    """Remove paid months that are before loan start dates"""
    try:
        storage = DataStorage()
        
        # Get loans for reference
        loans = storage.get_collection('loans')
        loan_start_dates = {}
        
        for loan in loans:
            loan_id = loan['id']
            start_date = loan.get('start_date')
            if start_date:
                loan_start_dates[loan_id] = datetime.strptime(start_date, '%Y-%m-%d')
                print(f"Loan {loan['name']}: starts {start_date}")
        
        # Get paid months
        paid_months = storage.get_collection('paid_months')
        
        print(f'\nFound {len(paid_months)} paid month records to check')
        
        invalid_records = []
        
        for record in paid_months:
            loan_id = record.get('loan_id')
            month_str = record.get('month')  # Format: "2025-06"
            
            if loan_id in loan_start_dates and month_str:
                # Parse month (YYYY-MM format)
                month_date = datetime.strptime(month_str + '-01', '%Y-%m-%d')
                loan_start = loan_start_dates[loan_id]
                
                if month_date < loan_start:
                    loan_name = next((l['name'] for l in loans if l['id'] == loan_id), 'Unknown')
                    print(f"❌ INVALID: {loan_name} - {month_str} is before start date {loan_start.strftime('%Y-%m-%d')}")
                    invalid_records.append(record)
                else:
                    loan_name = next((l['name'] for l in loans if l['id'] == loan_id), 'Unknown')
                    print(f"✅ Valid: {loan_name} - {month_str}")
        
        if invalid_records:
            print(f'\nFound {len(invalid_records)} invalid records to remove:')
            
            for record in invalid_records:
                loan_name = next((l['name'] for l in loans if l['id'] == record['loan_id']), 'Unknown')
                print(f"  Removing: {loan_name} - {record['month']}")
                
                # Delete the invalid record
                record_id = record.get('id')
                if record_id:
                    storage.remove_item(f'paid_months/{record_id}')
                    print(f"    ✅ Deleted record {record_id}")
                    
            print(f'\n✅ Cleaned up {len(invalid_records)} invalid paid month records')
        else:
            print('\n✅ No invalid records found - all paid months are valid!')
            
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    clean_invalid_paid_months()
