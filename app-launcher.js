// app-launcher.js
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { execFile } = require('child_process');

// Path to the electron executable
const electronPath = path.join(process.env.APPDATA, 'npm', 'electron.cmd');

// Check if electron exists at the expected path
if (!fs.existsSync(electronPath)) {
  console.error(`Electron not found at ${electronPath}`);
  console.error('Please ensure Electron is installed globally with npm install -g electron');
  process.exit(1);
}

// Start dev server
console.log('Starting development server...');
const devServer = spawn('npm', ['run', 'dev'], {
  stdio: 'pipe',
  shell: true
});

let serverStarted = false;
let serverUrl = 'http://localhost:5173';

devServer.stdout.on('data', (data) => {
  const output = data.toString();
  console.log(output);
  
  // When Vite prints the "ready" message with the URL
  if (output.includes('Local:') && !serverStarted) {
    serverStarted = true;
    
    // Extract the URL from the output
    const urlMatch = output.match(/Local:\s+(http:\/\/localhost:\d+)/);
    if (urlMatch) {
      serverUrl = urlMatch[1];
    }
    
    // Launch electron app after server starts
    console.log(`Server started at ${serverUrl}`);
    console.log('Launching Electron app...');
    
    // Prepare environment variables
    const env = Object.assign({}, process.env);
    env.ELECTRON_START_URL = serverUrl;
    
    // Launch electron with our app
    const electron = execFile(electronPath, ['.'], {
      env: env
    });
    
    electron.stdout.on('data', (data) => {
      console.log(`Electron: ${data}`);
    });
    
    electron.stderr.on('data', (data) => {
      console.error(`Electron error: ${data}`);
    });
    
    electron.on('close', (code) => {
      console.log(`Electron process exited with code ${code}`);
      devServer.kill();
      process.exit(0);
    });
  }
});

devServer.stderr.on('data', (data) => {
  console.error(`Dev server error: ${data}`);
});

devServer.on('close', (code) => {
  console.log(`Dev server process exited with code ${code}`);
  process.exit(code);
});

// Handle interrupts
process.on('SIGINT', () => {
  console.log('Shutting down...');
  devServer.kill();
  process.exit(0);
});