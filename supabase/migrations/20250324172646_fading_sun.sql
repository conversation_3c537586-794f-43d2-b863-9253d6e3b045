/*
  # Fix Database Constraints

  1. Changes
    - Remove duplicate stock entries before adding unique constraint
    - Add unique constraints safely
    - Update stock_data and watchlist_items constraints
    - Add stock update trigger

  2. Security
    - Maintain existing RLS policies
    - Ensure data integrity
*/

-- First, remove any duplicate stocks keeping the latest version
DO $$
BEGIN
  -- Create temporary table to store stocks to keep
  CREATE TEMP TABLE stocks_to_keep AS
  WITH ranked_stocks AS (
    SELECT 
      id,
      symbol,
      ROW_NUMBER() OVER (PARTITION BY symbol ORDER BY updated_at DESC NULLS LAST, created_at DESC NULLS LAST, id DESC) as rn
    FROM stocks
  )
  SELECT id
  FROM ranked_stocks
  WHERE rn = 1;

  -- Delete duplicates
  DELETE FROM stocks
  WHERE id NOT IN (SELECT id FROM stocks_to_keep);

  -- Drop temporary table
  DROP TABLE stocks_to_keep;
END $$;

-- Now safely add the unique constraint
ALTER TABLE stocks ADD CONSTRAINT stocks_symbol_key UNIQUE (symbol);

-- Update stock_data constraints
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'stock_data_stock_id_date_key'
  ) THEN
    ALTER TABLE stock_data ADD CONSTRAINT stock_data_stock_id_date_key UNIQUE (stock_id, date);
  END IF;
END $$;

-- Update watchlist_items constraints
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'watchlist_items_watchlist_id_stock_id_key'
  ) THEN
    ALTER TABLE watchlist_items ADD CONSTRAINT watchlist_items_watchlist_id_stock_id_key UNIQUE (watchlist_id, stock_id);
  END IF;
END $$;

-- Create function to handle stock updates
CREATE OR REPLACE FUNCTION handle_stock_update()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for stock updates if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'handle_stock_update_trigger'
  ) THEN
    CREATE TRIGGER handle_stock_update_trigger
      BEFORE UPDATE ON stocks
      FOR EACH ROW
      EXECUTE FUNCTION handle_stock_update();
  END IF;
END $$;