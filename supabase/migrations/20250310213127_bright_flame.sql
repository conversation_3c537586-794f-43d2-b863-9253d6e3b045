/*
  # Update loan payments table

  1. Changes
    - Add loan payments table if it doesn't exist
    - Add policy for authenticated users if it doesn't exist

  2. Security
    - Enable RLS
    - Add policy for authenticated users to manage their own payments
*/

-- Create the loan_payments table if it doesn't exist
CREATE TABLE IF NOT EXISTS loan_payments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  loan_id uuid REFERENCES loans(id) NOT NULL,
  amount numeric CHECK (amount >= 0) NOT NULL,
  payment_date date NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS if not already enabled
ALTER TABLE loan_payments ENABLE ROW LEVEL SECURITY;

-- Create the policy if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'loan_payments' 
    AND policyname = 'Users can manage their own payments'
  ) THEN
    CREATE POLICY "Users can manage their own payments"
      ON loan_payments
      FOR ALL
      TO authenticated
      USING (auth.uid() = user_id);
  END IF;
END $$;