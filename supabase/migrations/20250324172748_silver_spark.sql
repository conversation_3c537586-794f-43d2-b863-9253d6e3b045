/*
  # Update RLS Policies for Stock Management

  1. Changes
    - Update RLS policies for stocks table
    - Add policies for stock creation and updates
    - Ensure proper user access control

  2. Security
    - Maintain data integrity
    - Allow authenticated users to manage their stocks
*/

-- Update RLS policies for stocks table
DROP POLICY IF EXISTS "Anyone can read stocks" ON stocks;
DROP POLICY IF EXISTS "Users can insert own stocks" ON stocks;
DROP POLICY IF EXISTS "Users can update own stocks" ON stocks;

-- Allow authenticated users to read all stocks
CREATE POLICY "Anyone can read stocks"
  ON stocks
  FOR SELECT
  TO authenticated
  USING (true);

-- Allow authenticated users to insert their own stocks
CREATE POLICY "Users can insert own stocks"
  ON stocks
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() = user_id OR
    user_id IS NULL
  );

-- Allow authenticated users to update their own stocks
CREATE POLICY "Users can update own stocks"
  ON stocks
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Update stock_data policies
DROP POLICY IF EXISTS "Users can read own stock data" ON stock_data;
DROP POLICY IF EXISTS "Users can insert own stock data" ON stock_data;

-- Allow authenticated users to read any stock data
CREATE POLICY "Anyone can read stock data"
  ON stock_data
  FOR SELECT
  TO authenticated
  USING (true);

-- Allow authenticated users to insert stock data for their stocks
CREATE POLICY "Users can insert stock data"
  ON stock_data
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM stocks
      WHERE stocks.id = stock_data.stock_id
      AND (stocks.user_id = auth.uid() OR stocks.user_id IS NULL)
    )
  );