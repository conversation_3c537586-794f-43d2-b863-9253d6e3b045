/*
  # Create users and loan payments tables

  1. New Tables
    - `users`
      - `id` (uuid, primary key)
      - `email` (text, unique)
      - `created_at` (timestamp)
    
    - `loan_payments`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references users)
      - `loan_id` (uuid, references loans)
      - `amount` (numeric)
      - `payment_date` (date)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on both tables
    - Add policies for authenticated users to manage their own data
*/

-- Create users table first
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text UNIQUE NOT NULL,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE users ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own data"
  ON users
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Create loan payments table
CREATE TABLE IF NOT EXISTS loan_payments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(id) NOT NULL,
  loan_id uuid REFERENCES loans(id) NOT NULL,
  amount numeric CHECK (amount >= 0) NOT NULL,
  payment_date date NOT NULL,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE loan_payments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own payments"
  ON loan_payments
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);