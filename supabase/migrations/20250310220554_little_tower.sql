/*
  # Create Financial Goals Table

  1. New Tables
    - `financial_goals`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to auth.users)
      - `name` (text)
      - `target_amount` (numeric)
      - `current_amount` (numeric)
      - `category` (text, enum)
      - `target_date` (date)
      - `priority` (text, enum)
      - `notes` (text)
      - `created_at` (timestamp with time zone)

  2. Security
    - Enable RLS on `financial_goals` table
    - Add policy for authenticated users to manage their own goals
*/

CREATE TABLE IF NOT EXISTS financial_goals (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  name text NOT NULL,
  target_amount numeric NOT NULL CHECK (target_amount > 0),
  current_amount numeric NOT NULL DEFAULT 0 CHECK (current_amount >= 0),
  category text NOT NULL CHECK (category IN ('savings', 'debt', 'investment', 'emergency', 'custom')),
  target_date date NOT NULL,
  priority text NOT NULL CHECK (priority IN ('low', 'medium', 'high')),
  notes text,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE financial_goals ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own goals"
  ON financial_goals
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);