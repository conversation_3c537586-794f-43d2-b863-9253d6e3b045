/*
  # Fix database constraints and policies

  1. Changes
    - Remove and recreate unique constraints safely
    - Update RLS policies for better access control
    - Fix stock data relationships

  2. Security
    - Ensure proper user access control
    - Maintain data integrity
*/

-- First, safely handle existing data
DO $$
BEGIN
  -- Remove existing constraints if they exist
  ALTER TABLE stocks DROP CONSTRAINT IF EXISTS stocks_symbol_key;
  ALTER TABLE stock_data DROP CONSTRAINT IF EXISTS stock_data_stock_id_date_key;
  ALTER TABLE watchlist_items DROP CONSTRAINT IF EXISTS watchlist_items_watchlist_id_stock_id_key;

  -- Clean up any duplicate stocks by keeping the most recently updated record
  WITH duplicates AS (
    SELECT 
      id,
      symbol,
      ROW_NUMBER() OVER (
        PARTITION BY symbol 
        ORDER BY updated_at DESC NULLS LAST, 
                 created_at DESC NULLS LAST, 
                 id DESC
      ) as row_num
    FROM stocks
  )
  DELETE FROM stocks s
  WHERE EXISTS (
    SELECT 1 FROM duplicates d
    WHERE s.id = d.id
    AND d.row_num > 1
  );

  -- Now add constraints back
  ALTER TABLE stocks ADD CONSTRAINT stocks_symbol_key UNIQUE (symbol);
  ALTER TABLE stock_data ADD CONSTRAINT stock_data_stock_id_date_key UNIQUE (stock_id, date);
  ALTER TABLE watchlist_items ADD CONSTRAINT watchlist_items_watchlist_id_stock_id_key UNIQUE (watchlist_id, stock_id);
END $$;

-- Update RLS policies
ALTER TABLE stocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE watchlist_items ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Anyone can read stocks" ON stocks;
DROP POLICY IF EXISTS "Users can insert own stocks" ON stocks;
DROP POLICY IF EXISTS "Users can update own stocks" ON stocks;

-- Create new policies
CREATE POLICY "Anyone can read stocks"
  ON stocks
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can insert own stocks"
  ON stocks
  FOR INSERT
  TO authenticated
  WITH CHECK (
    auth.uid() = user_id OR
    user_id IS NULL
  );

CREATE POLICY "Users can update own stocks"
  ON stocks
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id OR user_id IS NULL)
  WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

-- Update stock_data policies
DROP POLICY IF EXISTS "Anyone can read stock data" ON stock_data;
DROP POLICY IF EXISTS "Users can insert stock data" ON stock_data;

CREATE POLICY "Anyone can read stock data"
  ON stock_data
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can insert stock data"
  ON stock_data
  FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM stocks
      WHERE stocks.id = stock_data.stock_id
      AND (stocks.user_id = auth.uid() OR stocks.user_id IS NULL)
    )
  );

-- Ensure triggers are in place
CREATE OR REPLACE FUNCTION handle_stock_update()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS handle_stock_update_trigger ON stocks;
CREATE TRIGGER handle_stock_update_trigger
  BEFORE UPDATE ON stocks
  FOR EACH ROW
  EXECUTE FUNCTION handle_stock_update();