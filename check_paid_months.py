#!/usr/bin/env python3
"""
Check Paid Months Data
======================
Analyze the paid months data that's showing up in the debug output.
"""

import sys
sys.path.append('.')
from data_storage import DataStorage
import j<PERSON>

def check_paid_months():
    """Check current paid months data in storage"""
    try:
        storage = DataStorage()
        
        # Get loans for reference
        loans = storage.get_collection('loans')
        loan_names = {loan['id']: loan['name'] for loan in loans}
        
        # Get paid months data
        paid_months = storage.get_collection('paid_months')
        
        print('Paid Months Analysis')
        print('=' * 50)
        print(f'Found {len(paid_months)} paid month records')
        print()
        
        if paid_months:
            print('Detailed Records:')
            print('-' * 30)
            
            # Group by loan
            by_loan = {}
            for record in paid_months:
                loan_id = record.get('loan_id')
                if loan_id not in by_loan:
                    by_loan[loan_id] = []
                by_loan[loan_id].append(record)
            
            # Display by loan
            for loan_id, records in by_loan.items():
                loan_name = loan_names.get(loan_id, 'Unknown Loan')
                print(f'\nLoan: {loan_name} ({loan_id})')
                
                # Sort records by month
                sorted_records = sorted(records, key=lambda x: x.get('month', ''))
                
                for record in sorted_records:
                    month = record.get('month', 'N/A')
                    created = record.get('created_at', 'N/A')[:19] if record.get('created_at') else 'N/A'
                    print(f'  Month {month} - Marked paid on {created}')
                    
        else:
            print('No paid month records found')
            
        print('\n' + '=' * 50)
        print('Raw Debug Data (formatted):')
        print(json.dumps(paid_months, indent=2))
        
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_paid_months()
