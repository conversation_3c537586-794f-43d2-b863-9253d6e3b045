import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  base: '/', // Use root path for PyWebView
  server: {
    host: 'localhost',
    port: 5173,
    strictPort: false,
    hmr: true,
    watch: {
      usePolling: true,
    },
  },
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    sourcemap: false, // Disable sourcemaps for smaller bundles
    minify: 'terser',
    target: 'esnext',
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html')
      },
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          charts: ['chart.js', 'react-chartjs-2', 'recharts'],
          utils: ['date-fns', 'zod', 'zustand']
        }
      }
    }
  },
  define: {
    'process.env.VITE_USE_LOCAL_STORE': JSON.stringify('true'),
    'import.meta.env.VITE_USE_LOCAL_STORE': JSON.stringify('true'),
  }
});
