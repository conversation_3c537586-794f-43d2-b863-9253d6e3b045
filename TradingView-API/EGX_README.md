# 🇪🇬 EGX Stock Monitor - Real-time Egyptian Exchange

A comprehensive real-time stock monitoring application for the Egyptian Exchange (EGX) built with TradingView API, featuring both console and web-based interfaces.

## 🌟 Features

### ✅ **Real-time EGX Stock Data**
- Live price updates for major Egyptian stocks
- EGX 30 Index monitoring
- Daily change tracking with percentage calculations
- Volume and OHLC (Open, High, Low, Close) data

### ✅ **Multi-Sector Coverage**
- **Banking**: CIB (Commercial International Bank), ADIB
- **Technology**: Fawry (Banking Technology), E-Finance
- **Real Estate**: Palm Hills, SODIC, Emaar Misr
- **Industrial**: El Sewedy Electric, Orascom Construction
- **Pharmaceuticals**: Ibn Sina Pharma, Rameda
- **Financial Services**: EFG Holding

### ✅ **Web-Based Dashboard**
- Modern, responsive web interface
- Real-time updates via WebSocket
- Color-coded sector categorization
- Market overview with gainers/losers
- Mobile-friendly design

### ✅ **Console Application**
- Terminal-based monitoring
- Sector-grouped display
- Technical analysis integration
- Automated reporting

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- npm package manager

### Installation

1. **Clone and setup:**
```bash
git clone <repository-url>
cd TradingView-API
npm install @mathieuc/tradingview express socket.io
```

2. **Run Console Monitor:**
```bash
node examples/EGXStockMonitor.js
```

3. **Run Web Dashboard:**
```bash
node web-app/server.js
```
Then open: http://localhost:3000

## 📊 Available Applications

### 1. **EGX Console Monitor** (`examples/EGXStockMonitor.js`)
Real-time console application with:
- Live price updates
- Sector-based grouping
- Technical analysis
- Market summary reports

**Sample Output:**
```
🇪🇬 EGX MARKET SUMMARY
=====================================
📊 BANK SECTOR:
Commercial International Bank: 80.70 EGP 🔴 -2.15 (-2.60%)

📊 FINTECH SECTOR:
Fawry Banking Technology: 11.34 EGP 🔴 -0.07 (-0.61%)
```

### 2. **Web Dashboard** (`web-app/server.js`)
Modern web interface featuring:
- Real-time stock cards
- Market overview statistics
- WebSocket-powered updates
- Responsive design
- Color-coded sectors

### 3. **Multi-Market Monitor** (`examples/MultiMarketMonitor.js`)
Monitor multiple markets simultaneously (crypto, stocks, forex)

### 4. **Technical Analysis** (`examples/TechnicalAnalysis.js`)
Get comprehensive technical analysis for any symbol

## 🏗️ Architecture

```
TradingView-API/
├── examples/
│   ├── EGXStockMonitor.js          # Console EGX monitor
│   ├── MultiMarketMonitor.js       # Multi-market console
│   ├── TechnicalAnalysis.js        # TA analysis tool
│   └── CustomIndicatorExample.js   # Indicator examples
├── web-app/
│   ├── server.js                   # Express + Socket.IO server
│   └── public/
│       └── index.html              # Web dashboard
└── main.js                         # TradingView API entry point
```

## 🔧 API Endpoints

The web server provides REST API endpoints:

- `GET /api/stocks` - Get all stock data
- `GET /api/stocks/:symbol` - Get specific stock data
- `GET /api/technical-analysis/:symbol` - Get technical analysis

## 📈 Supported EGX Stocks

### Major Stocks Monitored:
- **EGX:EGX30** - EGX 30 Index
- **EGX:COMI** - Commercial International Bank
- **EGX:SWDY** - El Sewedy Electric
- **EGX:FWRY** - Fawry Banking Technology
- **EGX:PHDC** - Palm Hills Development
- **EGX:ORAS** - Orascom Construction
- **EGX:ISPH** - Ibn Sina Pharma
- **EGX:HRHO** - EFG Holding

### All Available EGX Stocks:
The system supports 49+ Egyptian stocks including banks, real estate, industrial, pharmaceutical, and technology companies.

## 🎯 Use Cases

### 1. **Day Trading**
- Real-time price monitoring
- Quick market overview
- Sector performance analysis

### 2. **Investment Research**
- Technical analysis integration
- Historical data access
- Multi-timeframe analysis

### 3. **Portfolio Monitoring**
- Track multiple EGX positions
- Real-time P&L calculations
- Market alerts

### 4. **Market Analysis**
- Sector rotation analysis
- Volume analysis
- Market sentiment tracking

## 🔮 Future Enhancements

### Planned Features:
- [ ] **Email/SMS Alerts** - Price movement notifications
- [ ] **Portfolio Tracking** - Personal portfolio management
- [ ] **Historical Charts** - Interactive price charts
- [ ] **Screener** - Stock filtering and screening
- [ ] **News Integration** - Market news and events
- [ ] **Mobile App** - React Native mobile application
- [ ] **Trading Signals** - Automated trading signals
- [ ] **Backtesting** - Strategy backtesting tools

## 🛠️ Technical Details

### Technologies Used:
- **Backend**: Node.js, Express.js, Socket.IO
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Data Source**: TradingView API
- **Real-time**: WebSocket connections
- **Styling**: Modern CSS with animations

### Performance:
- Real-time updates (< 1 second latency)
- Supports 50+ concurrent connections
- Lightweight architecture
- Mobile-optimized

## 📞 Support & Contributing

### Getting Help:
1. Check existing examples in `/examples` folder
2. Review TradingView API documentation
3. Join TradingView API Telegram: [t.me/tradingview_api](https://t.me/tradingview_api)

### Contributing:
1. Fork the repository
2. Create feature branch
3. Add tests for new features
4. Submit pull request

## ⚠️ Disclaimer

This tool is for educational and informational purposes only. Not financial advice. Always do your own research before making investment decisions.

## 📄 License

ISC License - See package.json for details.

---

**Built with ❤️ for the Egyptian financial community**
