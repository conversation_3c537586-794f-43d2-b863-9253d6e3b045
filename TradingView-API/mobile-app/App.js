import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  StatusBar,
  Alert,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import io from 'socket.io-client';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Stock Card Component
const StockCard = ({ stock, onPress }) => {
  const changeColor = stock.change >= 0 ? '#4CAF50' : '#F44336';
  const changeIcon = stock.change >= 0 ? 'trending-up' : 'trending-down';

  return (
    <TouchableOpacity style={styles.stockCard} onPress={() => onPress(stock)}>
      <View style={styles.stockHeader}>
        <View>
          <Text style={styles.stockName}>{stock.name}</Text>
          <Text style={styles.stockSymbol}>{stock.symbol}</Text>
        </View>
        <View style={[styles.stockType, { backgroundColor: getSectorColor(stock.type) }]}>
          <Text style={styles.stockTypeText}>{stock.type.toUpperCase()}</Text>
        </View>
      </View>
      
      <Text style={styles.stockPrice}>{stock.price?.toFixed(2) || '--'} EGP</Text>
      
      <View style={[styles.stockChange, { color: changeColor }]}>
        <Icon name={changeIcon} size={16} color={changeColor} />
        <Text style={[styles.changeText, { color: changeColor }]}>
          {stock.change >= 0 ? '+' : ''}{stock.change?.toFixed(2) || '--'} EGP
        </Text>
        <Text style={[styles.changePercent, { color: changeColor }]}>
          ({stock.change >= 0 ? '+' : ''}{stock.changePercent?.toFixed(2) || '--'}%)
        </Text>
      </View>
      
      <View style={styles.stockDetails}>
        <Text style={styles.detailText}>Vol: {stock.volume?.toLocaleString() || '--'}</Text>
        <Text style={styles.detailText}>H: {stock.high?.toFixed(2) || '--'}</Text>
        <Text style={styles.detailText}>L: {stock.low?.toFixed(2) || '--'}</Text>
      </View>
    </TouchableOpacity>
  );
};

// Market Overview Component
const MarketOverview = ({ stocks }) => {
  const connectedStocks = stocks.length;
  const gainers = stocks.filter(s => s.change > 0).length;
  const losers = stocks.filter(s => s.change < 0).length;
  const totalVolume = stocks.reduce((sum, s) => sum + (s.volume || 0), 0);

  return (
    <View style={styles.marketOverview}>
      <Text style={styles.overviewTitle}>🇪🇬 EGX Market Overview</Text>
      <View style={styles.overviewGrid}>
        <View style={styles.overviewCard}>
          <Text style={styles.overviewLabel}>Connected</Text>
          <Text style={styles.overviewValue}>{connectedStocks}</Text>
        </View>
        <View style={styles.overviewCard}>
          <Text style={styles.overviewLabel}>Gainers</Text>
          <Text style={[styles.overviewValue, { color: '#4CAF50' }]}>{gainers}</Text>
        </View>
        <View style={styles.overviewCard}>
          <Text style={styles.overviewLabel}>Losers</Text>
          <Text style={[styles.overviewValue, { color: '#F44336' }]}>{losers}</Text>
        </View>
        <View style={styles.overviewCard}>
          <Text style={styles.overviewLabel}>Volume</Text>
          <Text style={styles.overviewValue}>{formatNumber(totalVolume)}</Text>
        </View>
      </View>
    </View>
  );
};

// Home Screen
const HomeScreen = ({ navigation }) => {
  const [stocks, setStocks] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [connected, setConnected] = useState(false);
  const [socket, setSocket] = useState(null);

  useEffect(() => {
    connectToServer();
    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, []);

  const connectToServer = () => {
    const newSocket = io('http://localhost:3000');
    
    newSocket.on('connect', () => {
      setConnected(true);
      console.log('Connected to EGX server');
    });

    newSocket.on('disconnect', () => {
      setConnected(false);
      console.log('Disconnected from server');
    });

    newSocket.on('initialData', (stocksData) => {
      setStocks(stocksData);
    });

    newSocket.on('stockUpdate', (stockData) => {
      setStocks(prevStocks => {
        const updatedStocks = [...prevStocks];
        const index = updatedStocks.findIndex(s => s.symbol === stockData.symbol);
        if (index !== -1) {
          updatedStocks[index] = stockData;
        } else {
          updatedStocks.push(stockData);
        }
        return updatedStocks;
      });
    });

    setSocket(newSocket);
  };

  const onRefresh = () => {
    setRefreshing(true);
    // Reconnect to get fresh data
    if (socket) {
      socket.disconnect();
      connectToServer();
    }
    setTimeout(() => setRefreshing(false), 2000);
  };

  const handleStockPress = (stock) => {
    navigation.navigate('StockDetail', { stock });
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1976D2" />
      <View style={styles.header}>
        <Text style={styles.headerTitle}>EGX Stock Monitor</Text>
        <View style={[styles.connectionStatus, { backgroundColor: connected ? '#4CAF50' : '#F44336' }]}>
          <Text style={styles.connectionText}>{connected ? 'Connected' : 'Disconnected'}</Text>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        <MarketOverview stocks={stocks} />
        
        <View style={styles.stocksList}>
          {stocks.map((stock, index) => (
            <StockCard
              key={stock.symbol}
              stock={stock}
              onPress={handleStockPress}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

// Stock Detail Screen
const StockDetailScreen = ({ route, navigation }) => {
  const { stock } = route.params;

  return (
    <View style={styles.container}>
      <View style={styles.detailHeader}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.detailTitle}>{stock.name}</Text>
      </View>

      <ScrollView style={styles.detailContent}>
        <View style={styles.priceSection}>
          <Text style={styles.detailPrice}>{stock.price?.toFixed(2) || '--'} EGP</Text>
          <Text style={[styles.detailChange, { color: stock.change >= 0 ? '#4CAF50' : '#F44336' }]}>
            {stock.change >= 0 ? '+' : ''}{stock.change?.toFixed(2) || '--'} 
            ({stock.change >= 0 ? '+' : ''}{stock.changePercent?.toFixed(2) || '--'}%)
          </Text>
        </View>

        <View style={styles.detailGrid}>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Open</Text>
            <Text style={styles.detailValue}>{stock.open?.toFixed(2) || '--'} EGP</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>High</Text>
            <Text style={styles.detailValue}>{stock.high?.toFixed(2) || '--'} EGP</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Low</Text>
            <Text style={styles.detailValue}>{stock.low?.toFixed(2) || '--'} EGP</Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Volume</Text>
            <Text style={styles.detailValue}>{stock.volume?.toLocaleString() || '--'}</Text>
          </View>
        </View>

        <TouchableOpacity style={styles.alertButton} onPress={() => createAlert(stock)}>
          <Icon name="notifications" size={20} color="#FFFFFF" />
          <Text style={styles.alertButtonText}>Create Price Alert</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

// Portfolio Screen
const PortfolioScreen = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.comingSoon}>Portfolio Tracking</Text>
      <Text style={styles.comingSoonSubtext}>Coming Soon!</Text>
    </View>
  );
};

// Alerts Screen
const AlertsScreen = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.comingSoon}>Price Alerts</Text>
      <Text style={styles.comingSoonSubtext}>Coming Soon!</Text>
    </View>
  );
};

// Helper functions
const getSectorColor = (sector) => {
  const colors = {
    'index': '#2196F3',
    'bank': '#FF9800',
    'industrial': '#4CAF50',
    'fintech': '#9C27B0',
    'real_estate': '#FF5722',
    'construction': '#607D8B',
    'pharma': '#E91E63',
    'financial': '#009688'
  };
  return colors[sector] || '#757575';
};

const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

const createAlert = (stock) => {
  Alert.alert(
    'Create Alert',
    `Set a price alert for ${stock.name}?`,
    [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Create', onPress: () => console.log('Alert created') }
    ]
  );
};

// Main App Component
const App = () => {
  return (
    <NavigationContainer>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused, color, size }) => {
            let iconName;
            if (route.name === 'Home') iconName = 'home';
            else if (route.name === 'Portfolio') iconName = 'account-balance-wallet';
            else if (route.name === 'Alerts') iconName = 'notifications';
            return <Icon name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: '#2196F3',
          tabBarInactiveTintColor: 'gray',
          headerShown: false,
        })}
      >
        <Tab.Screen name="Home" component={HomeStackNavigator} />
        <Tab.Screen name="Portfolio" component={PortfolioScreen} />
        <Tab.Screen name="Alerts" component={AlertsScreen} />
      </Tab.Navigator>
    </NavigationContainer>
  );
};

// Stack Navigator for Home
const HomeStackNavigator = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="HomeMain" component={HomeScreen} />
      <Stack.Screen name="StockDetail" component={StockDetailScreen} />
    </Stack.Navigator>
  );
};

export default App;
