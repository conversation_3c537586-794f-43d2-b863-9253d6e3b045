/**
 * Advanced Stock Screener for EGX
 * Filter and analyze Egyptian stocks based on multiple criteria
 */

class StockScreener {
  constructor(tradingViewClient) {
    this.client = tradingViewClient;
    this.stocks = new Map();
    this.screeningResults = new Map();
  }

  // Add stock data for screening
  addStockData(symbol, data) {
    this.stocks.set(symbol, {
      ...data,
      lastUpdated: new Date()
    });
  }

  // Screen stocks based on criteria
  async screenStocks(criteria) {
    const results = [];
    
    for (const [symbol, stock] of this.stocks) {
      try {
        const score = await this.evaluateStock(stock, criteria);
        if (score.passes) {
          results.push({
            symbol,
            name: stock.name,
            score: score.totalScore,
            matches: score.matches,
            data: stock
          });
        }
      } catch (error) {
        console.error(`Error screening ${symbol}:`, error);
      }
    }

    // Sort by score (highest first)
    results.sort((a, b) => b.score - a.score);
    
    this.screeningResults.set(criteria.id || 'default', {
      criteria,
      results,
      timestamp: new Date()
    });

    return results;
  }

  async evaluateStock(stock, criteria) {
    let totalScore = 0;
    let maxScore = 0;
    const matches = [];

    // Price criteria
    if (criteria.price) {
      maxScore += criteria.price.weight || 10;
      if (this.checkPriceRange(stock.price, criteria.price)) {
        totalScore += criteria.price.weight || 10;
        matches.push('Price range');
      }
    }

    // Volume criteria
    if (criteria.volume) {
      maxScore += criteria.volume.weight || 10;
      if (this.checkVolumeRange(stock.volume, criteria.volume)) {
        totalScore += criteria.volume.weight || 10;
        matches.push('Volume range');
      }
    }

    // Market cap criteria
    if (criteria.marketCap) {
      maxScore += criteria.marketCap.weight || 15;
      const marketCap = this.calculateMarketCap(stock);
      if (this.checkRange(marketCap, criteria.marketCap)) {
        totalScore += criteria.marketCap.weight || 15;
        matches.push('Market cap');
      }
    }

    // Daily change criteria
    if (criteria.dailyChange) {
      maxScore += criteria.dailyChange.weight || 15;
      if (this.checkRange(stock.changePercent, criteria.dailyChange)) {
        totalScore += criteria.dailyChange.weight || 15;
        matches.push('Daily change');
      }
    }

    // Technical indicators
    if (criteria.technical) {
      const technicalScore = await this.evaluateTechnicalCriteria(stock, criteria.technical);
      totalScore += technicalScore.score;
      maxScore += technicalScore.maxScore;
      matches.push(...technicalScore.matches);
    }

    // Sector criteria
    if (criteria.sector) {
      maxScore += criteria.sector.weight || 5;
      if (criteria.sector.include.includes(stock.type)) {
        totalScore += criteria.sector.weight || 5;
        matches.push('Sector match');
      }
    }

    const finalScore = maxScore > 0 ? (totalScore / maxScore) * 100 : 0;
    const passes = finalScore >= (criteria.minScore || 60);

    return {
      totalScore: finalScore,
      passes,
      matches
    };
  }

  async evaluateTechnicalCriteria(stock, technical) {
    let score = 0;
    let maxScore = 0;
    const matches = [];

    try {
      // Get technical analysis data
      const markets = await this.client.searchMarketV3(stock.symbol);
      if (markets.length === 0) return { score: 0, maxScore: 0, matches: [] };

      const ta = await markets[0].getTA();

      // RSI criteria
      if (technical.rsi) {
        maxScore += technical.rsi.weight || 10;
        if (ta.indicators.RSI && this.checkRange(ta.indicators.RSI, technical.rsi)) {
          score += technical.rsi.weight || 10;
          matches.push('RSI range');
        }
      }

      // MACD criteria
      if (technical.macd) {
        maxScore += technical.macd.weight || 10;
        if (ta.indicators.MACD && this.checkRange(ta.indicators.MACD, technical.macd)) {
          score += technical.macd.weight || 10;
          matches.push('MACD range');
        }
      }

      // Overall recommendation
      if (technical.recommendation) {
        maxScore += technical.recommendation.weight || 20;
        if (technical.recommendation.values.includes(ta.summary.RECOMMENDATION)) {
          score += technical.recommendation.weight || 20;
          matches.push('Technical recommendation');
        }
      }

      // Moving averages
      if (technical.movingAverages) {
        maxScore += technical.movingAverages.weight || 15;
        if (technical.movingAverages.values.includes(ta.summary.MA.RECOMMENDATION)) {
          score += technical.movingAverages.weight || 15;
          matches.push('Moving averages');
        }
      }

    } catch (error) {
      console.error(`Error getting technical data for ${stock.symbol}:`, error);
    }

    return { score, maxScore, matches };
  }

  checkPriceRange(price, criteria) {
    return this.checkRange(price, criteria);
  }

  checkVolumeRange(volume, criteria) {
    return this.checkRange(volume, criteria);
  }

  checkRange(value, criteria) {
    if (criteria.min !== undefined && value < criteria.min) return false;
    if (criteria.max !== undefined && value > criteria.max) return false;
    return true;
  }

  calculateMarketCap(stock) {
    // Simplified market cap calculation
    // In real implementation, you'd need shares outstanding data
    return stock.price * 1000000; // Placeholder
  }

  // Predefined screening strategies
  getPresetScreens() {
    return {
      'value_stocks': {
        id: 'value_stocks',
        name: 'Value Stocks',
        description: 'Undervalued stocks with strong fundamentals',
        criteria: {
          price: { min: 5, max: 200, weight: 10 },
          dailyChange: { min: -5, max: 15, weight: 15 },
          technical: {
            rsi: { min: 20, max: 50, weight: 20 },
            recommendation: { values: ['BUY', 'STRONG_BUY'], weight: 25 }
          },
          minScore: 70
        }
      },
      'growth_stocks': {
        id: 'growth_stocks',
        name: 'Growth Stocks',
        description: 'High-growth potential stocks',
        criteria: {
          dailyChange: { min: 2, weight: 20 },
          volume: { min: 100000, weight: 15 },
          technical: {
            rsi: { min: 50, max: 80, weight: 15 },
            movingAverages: { values: ['BUY', 'STRONG_BUY'], weight: 20 }
          },
          sector: { include: ['fintech', 'pharma', 'industrial'], weight: 10 },
          minScore: 65
        }
      },
      'momentum_stocks': {
        id: 'momentum_stocks',
        name: 'Momentum Stocks',
        description: 'Stocks with strong upward momentum',
        criteria: {
          dailyChange: { min: 3, weight: 25 },
          volume: { min: 50000, weight: 20 },
          technical: {
            rsi: { min: 60, max: 85, weight: 20 },
            recommendation: { values: ['BUY', 'STRONG_BUY'], weight: 25 }
          },
          minScore: 75
        }
      },
      'dividend_stocks': {
        id: 'dividend_stocks',
        name: 'Dividend Stocks',
        description: 'Stable dividend-paying stocks',
        criteria: {
          price: { min: 10, weight: 10 },
          dailyChange: { min: -2, max: 5, weight: 15 },
          sector: { include: ['bank', 'financial'], weight: 20 },
          technical: {
            rsi: { min: 30, max: 70, weight: 15 }
          },
          minScore: 60
        }
      },
      'oversold_stocks': {
        id: 'oversold_stocks',
        name: 'Oversold Stocks',
        description: 'Potentially oversold stocks for reversal',
        criteria: {
          dailyChange: { max: -3, weight: 20 },
          technical: {
            rsi: { max: 30, weight: 30 },
            recommendation: { values: ['BUY', 'STRONG_BUY', 'NEUTRAL'], weight: 20 }
          },
          volume: { min: 10000, weight: 10 },
          minScore: 70
        }
      }
    };
  }

  // Get screening results
  getScreeningResults(screenId = 'default') {
    return this.screeningResults.get(screenId);
  }

  // Generate screening report
  generateScreeningReport(screenId) {
    const results = this.getScreeningResults(screenId);
    if (!results) return null;

    const topPicks = results.results.slice(0, 10);
    const sectorDistribution = this.analyzeSectorDistribution(results.results);
    const scoreDistribution = this.analyzeScoreDistribution(results.results);

    return {
      screenName: results.criteria.name || 'Custom Screen',
      timestamp: results.timestamp,
      totalStocks: this.stocks.size,
      matchingStocks: results.results.length,
      successRate: (results.results.length / this.stocks.size) * 100,
      topPicks,
      analytics: {
        sectorDistribution,
        scoreDistribution,
        averageScore: results.results.reduce((sum, r) => sum + r.score, 0) / results.results.length
      },
      recommendations: this.generateScreeningRecommendations(results.results)
    };
  }

  analyzeSectorDistribution(results) {
    const distribution = {};
    results.forEach(result => {
      const sector = result.data.type;
      distribution[sector] = (distribution[sector] || 0) + 1;
    });
    return distribution;
  }

  analyzeScoreDistribution(results) {
    const ranges = {
      '90-100': 0,
      '80-89': 0,
      '70-79': 0,
      '60-69': 0,
      'Below 60': 0
    };

    results.forEach(result => {
      const score = result.score;
      if (score >= 90) ranges['90-100']++;
      else if (score >= 80) ranges['80-89']++;
      else if (score >= 70) ranges['70-79']++;
      else if (score >= 60) ranges['60-69']++;
      else ranges['Below 60']++;
    });

    return ranges;
  }

  generateScreeningRecommendations(results) {
    const recommendations = [];

    if (results.length === 0) {
      recommendations.push({
        type: 'warning',
        message: 'No stocks matched your criteria. Consider relaxing some filters.'
      });
    } else if (results.length > 50) {
      recommendations.push({
        type: 'suggestion',
        message: 'Many stocks matched. Consider adding more specific criteria to narrow down results.'
      });
    }

    // Analyze top performers
    const topScores = results.slice(0, 5);
    if (topScores.length > 0) {
      recommendations.push({
        type: 'insight',
        message: `Top performer: ${topScores[0].name} with ${topScores[0].score.toFixed(1)}% match score.`
      });
    }

    return recommendations;
  }
}

module.exports = StockScreener;
