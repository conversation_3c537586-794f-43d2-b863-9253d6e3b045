const TradingView = require('../main');

/**
 * EGX (Egyptian Exchange) Stock Monitor
 * Real-time monitoring of Egyptian stocks with technical analysis
 */

console.log('🇪🇬 EGX Stock Monitor Starting...\n');

const client = new TradingView.Client();

// Top EGX stocks to monitor
const egxStocks = [
  { symbol: 'EGX:EGX30', name: 'EGX 30 Index', type: 'index' },
  { symbol: 'EGX:COMI', name: 'Commercial International Bank', type: 'bank' },
  { symbol: 'EGX:SWDY', name: 'El Sewedy Electric', type: 'industrial' },
  { symbol: 'EGX:FWRY', name: 'Fawry Banking Technology', type: 'fintech' },
  { symbol: 'EGX:PHDC', name: 'Palm Hills Development', type: 'real_estate' },
  { symbol: 'EGX:ORAS', name: 'Orascom Construction', type: 'construction' },
  { symbol: 'EGX:ISPH', name: '<PERSON> Pharma', type: 'pharma' },
  { symbol: 'EGX:HRHO', name: 'EFG Holding', type: 'financial' }
];

const charts = [];
const stockData = {};

// Create charts for each EGX stock
egxStocks.forEach((stock, index) => {
  const chart = new client.Session.Chart();
  charts.push(chart);
  
  chart.setMarket(stock.symbol, {
    timeframe: '1D', // Daily timeframe for stocks
  });

  chart.onSymbolLoaded(() => {
    console.log(`✅ ${stock.name} (${stock.symbol}) connected`);
  });

  chart.onUpdate(() => {
    if (!chart.periods[0]) return;
    
    const currentPrice = chart.periods[0].close;
    const openPrice = chart.periods[0].open;
    const highPrice = chart.periods[0].high;
    const lowPrice = chart.periods[0].low;
    const volume = chart.periods[0].volume || 0;
    
    const previousPrice = stockData[stock.symbol]?.price;
    
    // Calculate daily change
    const dailyChange = currentPrice - openPrice;
    const dailyChangePercent = ((dailyChange / openPrice) * 100);
    
    // Store current data
    stockData[stock.symbol] = {
      price: currentPrice,
      open: openPrice,
      high: highPrice,
      low: lowPrice,
      volume: volume,
      change: dailyChange,
      changePercent: dailyChangePercent,
      type: stock.type,
      name: stock.name,
      lastUpdate: new Date().toLocaleTimeString()
    };
    
    // Determine trend indicator
    let trendIndicator = '📊';
    if (previousPrice) {
      if (currentPrice > previousPrice) trendIndicator = '📈';
      else if (currentPrice < previousPrice) trendIndicator = '📉';
    }
    
    // Color coding for change
    const changeColor = dailyChange >= 0 ? '🟢' : '🔴';
    const changeSign = dailyChange >= 0 ? '+' : '';
    
    console.log(`${trendIndicator} ${stock.name}: ${currentPrice.toFixed(2)} EGP ${changeColor} ${changeSign}${dailyChange.toFixed(2)} (${changeSign}${dailyChangePercent.toFixed(2)}%)`);
  });

  chart.onError((...err) => {
    console.error(`❌ Error with ${stock.name}:`, ...err);
  });
});

// Display detailed summary every 60 seconds
setInterval(() => {
  console.log('\n' + '='.repeat(80));
  console.log('🇪🇬 EGX MARKET SUMMARY');
  console.log('='.repeat(80));
  
  // Group by sector
  const sectors = {};
  egxStocks.forEach(stock => {
    const data = stockData[stock.symbol];
    if (data) {
      if (!sectors[data.type]) sectors[data.type] = [];
      sectors[data.type].push({ symbol: stock.symbol, ...data });
    }
  });
  
  // Display by sector
  Object.keys(sectors).forEach(sectorType => {
    console.log(`\n📊 ${sectorType.toUpperCase().replace('_', ' ')} SECTOR:`);
    console.log('-'.repeat(60));
    
    sectors[sectorType].forEach(stock => {
      const changeColor = stock.change >= 0 ? '🟢' : '🔴';
      const changeSign = stock.change >= 0 ? '+' : '';
      console.log(`${stock.name.padEnd(30)}: ${stock.price.toFixed(2)} EGP ${changeColor} ${changeSign}${stock.change.toFixed(2)} (${changeSign}${stock.changePercent.toFixed(2)}%)`);
      console.log(`${''.padEnd(32)}Vol: ${stock.volume.toLocaleString()} | H: ${stock.high.toFixed(2)} | L: ${stock.low.toFixed(2)}`);
    });
  });
  
  console.log('='.repeat(80));
  console.log(`Last Updated: ${new Date().toLocaleString()}`);
  console.log('='.repeat(80) + '\n');
}, 60000);

// Get technical analysis for top stocks
async function getEGXTechnicalAnalysis() {
  console.log('\n🔍 Getting Technical Analysis for Top EGX Stocks...\n');
  
  const topStocks = ['EGX:COMI', 'EGX:SWDY', 'EGX:FWRY'];
  
  for (const symbol of topStocks) {
    try {
      const markets = await TradingView.searchMarketV3(symbol);
      if (markets.length > 0) {
        const market = markets[0];
        const ta = await market.getTA();
        
        console.log(`📈 ${market.description} (${symbol})`);
        console.log(`   Recommendation: ${ta.summary.RECOMMENDATION}`);
        console.log(`   Buy: ${ta.summary.BUY} | Sell: ${ta.summary.SELL} | Neutral: ${ta.summary.NEUTRAL}`);
        if (ta.indicators.RSI) console.log(`   RSI: ${ta.indicators.RSI.toFixed(2)}`);
        console.log('');
      }
    } catch (error) {
      console.error(`❌ Error analyzing ${symbol}:`, error.message);
    }
    
    // Wait between requests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
}

// Run technical analysis after 10 seconds
setTimeout(getEGXTechnicalAnalysis, 10000);

// Cleanup after 5 minutes
setTimeout(() => {
  console.log('\n🛑 Stopping EGX monitor...');
  client.end();
}, 300000);

console.log('⏳ Connecting to EGX markets...\n');
