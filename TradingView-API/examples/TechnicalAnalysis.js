const TradingView = require('../main');

/**
 * Technical Analysis Example
 * This example demonstrates how to get technical analysis data
 */

console.log('📈 Technical Analysis Example\n');

// Function to get technical analysis for a symbol
async function getTechnicalAnalysis(symbol) {
  try {
    console.log(`🔍 Analyzing ${symbol}...`);
    
    // Search for the market first
    const markets = await TradingView.searchMarketV3(symbol);
    if (markets.length === 0) {
      console.log(`❌ Market ${symbol} not found`);
      return;
    }
    
    const market = markets[0];
    console.log(`✅ Found: ${market.description} (${market.id})`);
    
    // Get technical analysis
    const ta = await market.getTA();
    
    console.log('\n📊 Technical Analysis Results:');
    console.log('================================');
    console.log(`Overall Recommendation: ${ta.summary.RECOMMENDATION}`);
    console.log(`Buy Signals: ${ta.summary.BUY}`);
    console.log(`Sell Signals: ${ta.summary.SELL}`);
    console.log(`Neutral Signals: ${ta.summary.NEUTRAL}`);
    
    console.log('\n📈 Moving Averages:');
    console.log(`MA Recommendation: ${ta.summary.MA.RECOMMENDATION}`);
    console.log(`MA Buy: ${ta.summary.MA.BUY} | Sell: ${ta.summary.MA.SELL} | Neutral: ${ta.summary.MA.NEUTRAL}`);
    
    console.log('\n🔄 Oscillators:');
    console.log(`Oscillator Recommendation: ${ta.summary.OSCILLATORS.RECOMMENDATION}`);
    console.log(`OSC Buy: ${ta.summary.OSCILLATORS.BUY} | Sell: ${ta.summary.OSCILLATORS.SELL} | Neutral: ${ta.summary.OSCILLATORS.NEUTRAL}`);
    
    // Show some specific indicators
    console.log('\n🎯 Key Indicators:');
    if (ta.indicators.RSI) console.log(`RSI (14): ${ta.indicators.RSI.toFixed(2)}`);
    if (ta.indicators.MACD) console.log(`MACD: ${ta.indicators.MACD.toFixed(4)}`);
    if (ta.indicators.Stoch) console.log(`Stochastic: ${ta.indicators.Stoch.toFixed(2)}`);
    if (ta.indicators.CCI) console.log(`CCI (20): ${ta.indicators.CCI.toFixed(2)}`);
    
    console.log('\n' + '='.repeat(50) + '\n');
    
  } catch (error) {
    console.error(`❌ Error analyzing ${symbol}:`, error.message);
  }
}

// Analyze multiple symbols
async function analyzeMultipleMarkets() {
  const symbols = [
    'BINANCE:BTCUSDT',
    'BINANCE:ETHUSDT', 
    'NASDAQ:AAPL',
    'NYSE:TSLA'
  ];
  
  for (const symbol of symbols) {
    await getTechnicalAnalysis(symbol);
    // Wait 2 seconds between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('✅ Technical analysis completed for all symbols!');
}

// Run the analysis
analyzeMultipleMarkets().catch(console.error);
