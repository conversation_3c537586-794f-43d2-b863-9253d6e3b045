const TradingView = require('../main');

/**
 * Multi-Market Price Monitor
 * This example monitors multiple cryptocurrency pairs simultaneously
 */

console.log('🌐 Starting Multi-Market Price Monitor...\n');

const client = new TradingView.Client();

// Markets to monitor
const markets = [
  { symbol: 'BINANCE:BTCUSDT', name: 'Bitcoin' },
  { symbol: 'BINANCE:ETHUSDT', name: 'Ethereum' },
  { symbol: 'BINANCE:SOLUSDT', name: '<PERSON><PERSON>' },
  { symbol: 'BINANCE:ADAUSDT', name: '<PERSON><PERSON>' },
  { symbol: 'BINANCE:DOTUSDT', name: '<PERSON><PERSON><PERSON>' }
];

const charts = [];
const prices = {};

// Create charts for each market
markets.forEach((market, index) => {
  const chart = new client.Session.Chart();
  charts.push(chart);
  
  chart.setMarket(market.symbol, {
    timeframe: '1', // 1 minute for real-time updates
  });

  chart.onSymbolLoaded(() => {
    console.log(`✅ ${market.name} (${market.symbol}) connected`);
  });

  chart.onUpdate(() => {
    if (!chart.periods[0]) return;
    
    const currentPrice = chart.periods[0].close;
    const previousPrice = prices[market.symbol];
    
    // Store current price
    prices[market.symbol] = currentPrice;
    
    // Calculate change
    let changeIndicator = '📊';
    if (previousPrice) {
      if (currentPrice > previousPrice) changeIndicator = '📈';
      else if (currentPrice < previousPrice) changeIndicator = '📉';
    }
    
    console.log(`${changeIndicator} ${market.name}: $${currentPrice.toFixed(2)}`);
  });

  chart.onError((...err) => {
    console.error(`❌ Error with ${market.name}:`, ...err);
  });
});

// Display summary every 30 seconds
setInterval(() => {
  console.log('\n' + '='.repeat(50));
  console.log('📊 MARKET SUMMARY');
  console.log('='.repeat(50));
  
  markets.forEach(market => {
    const price = prices[market.symbol];
    if (price) {
      console.log(`${market.name.padEnd(12)}: $${price.toFixed(2)}`);
    }
  });
  console.log('='.repeat(50) + '\n');
}, 30000);

// Cleanup after 2 minutes
setTimeout(() => {
  console.log('\n🛑 Stopping market monitor...');
  client.end();
}, 120000);

console.log('⏳ Connecting to markets...\n');
