const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const TradingView = require('../main');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// Add CORS middleware to allow Financial Advisor app to connect
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Add JSON parsing middleware
app.use(express.json());

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// EGX Stock configuration - These are monitored in real-time
const egxStocks = [
  { symbol: 'EGX:EGX30', name: 'EGX 30 Index', type: 'index', color: '#1f77b4' },
  { symbol: 'EGX:COMI', name: 'Commercial International Bank', type: 'bank', color: '#ff7f0e' },
  { symbol: 'EGX:SWDY', name: 'El Sewedy Electric', type: 'industrial', color: '#2ca02c' },
  { symbol: 'EGX:FWRY', name: 'Fawry Banking Technology', type: 'fintech', color: '#d62728' },
  { symbol: 'EGX:PHDC', name: 'Palm Hills Development', type: 'real_estate', color: '#9467bd' },
  { symbol: 'EGX:ORAS', name: 'Orascom Construction', type: 'construction', color: '#8c564b' },
  { symbol: 'EGX:ISPH', name: 'Ibn Sina Pharma', type: 'pharma', color: '#e377c2' },
  { symbol: 'EGX:HRHO', name: 'EFG Holding', type: 'financial', color: '#7f7f7f' },
  // Additional popular EGX stocks added for real-time monitoring
  { symbol: 'EGX:VALU', name: 'U Consumer Finance', type: 'financial', color: '#17becf' },
  { symbol: 'EGX:ETEL', name: 'Telecom Egypt', type: 'telecom', color: '#bcbd22' },
  { symbol: 'EGX:EMFD', name: 'EFG Finance', type: 'financial', color: '#e377c2' },
  { symbol: 'EGX:TALAAT', name: 'Talaat Moustafa Group', type: 'real_estate', color: '#ff7f0e' },
  { symbol: 'EGX:ABUK', name: 'Alexandria Bank', type: 'bank', color: '#2ca02c' },
  { symbol: 'EGX:MNHD', name: 'Madinet Nasr Housing', type: 'real_estate', color: '#d62728' }
];

// Extended EGX Stock symbols for broader search capability
const allEgxSymbols = [
  // Banks
  'COMI', 'NBKE', 'ABUK', 'ADIB', 'ALEX', 'ATQA', 'AHLI', 'ARIB', 'BCAI', 'BGAD',
  'BLAB', 'BMBE', 'BNKA', 'BPLS', 'BRCE', 'CALD', 'CAST', 'CCAP', 'EGBE', 'HRHO',
  'FABD', 'PIDG', 'QDIB', 'SAIB', 'SGFH', 'SKCB', 'UBKR',
  
  // Telecom & Technology
  'ETEL', 'ORTE', 'VTEL', 'FWRY', 'MNHD',
  
  // Real Estate
  'PHDC', 'TMGH', 'TALAAT', 'MNHD', 'EHCD', 'DSCW', 'EHPH', 'MHDB', 'ELRW',
  'EKPT', 'EHDB', 'MCDR', 'TMGH', 'THDR', 'AREB', 'ELTI', 'OSRM',
  
  // Industrial
  'SWDY', 'ORAS', 'ARHO', 'ARCE', 'BTEB', 'CTRA', 'ECHEM', 'EGAS', 'EGCH',
  'EMFA', 'ENAD', 'ETRS', 'EWDB', 'FIRE', 'GTHE', 'IRON', 'ISZP', 'JUFO',
  'LENS', 'MCAD', 'OTMT', 'PMED', 'POLY', 'SCTR', 'SKPC', 'SPED', 'SSTL',
  'STAL', 'SUAM', 'WEFR', 'WRFR',
  
  // Pharmaceuticals
  'ISPH', 'PHCI', 'AMOC', 'MIPH', 'MAAL', 'NIPH', 'EIPH', 'UEGC',
  
  // Food & Beverages
  'DOMT', 'EAST', 'EGGP', 'EGHE', 'MBFG', 'EMFD', 'HELI', 'IFCI', 'LHDE',
  'ORFN', 'PDIC', 'UASG', 'WFOD',
  
  // Textiles
  'LFHE', 'KAPS', 'EBAT', 'EGTS', 'ORWE', 'MGTS', 'NSGB', 'GODB', 'CZTX',
  
  // Basic Resources
  'EKHO', 'ASEC', 'ASDI', 'CLHO', 'SIDC', 'ELCR', 'ELIH', 'EZSC', 'EFID',
  'EHPD', 'ELRO', 'EZPT', 'NILE', 'UASP',
  
  // Utilities
  'EGAS', 'EGPC', 'EGFE', 'MPRA',
  
  // Tourism & Hotels
  'EGTS', 'SHEL', 'OTSH', 'JTHE', 'TRAN', 'HTND',
  
  // Consumer Goods
  'FANG', 'EGAA', 'HNBE', 'KAHR', 'LCSW', 'MOKD', 'PDCR', 'PICO', 'RAED'
];

// Function to format EGX symbols for TradingView
function formatEgxSymbol(symbol) {
  return symbol.startsWith('EGX:') ? symbol : `EGX:${symbol}`;
}

// Egyptian Bonds configuration - these would be real bond symbols from TradingView
const egxBonds = [
  { symbol: 'EGX:GOVT10Y', name: 'Egyptian Government 10Y Bond', type: 'government', maturity: '2034-12-31', coupon: 15.5, color: '#2E8B57' },
  { symbol: 'EGX:GOVT5Y', name: 'Egyptian Government 5Y Bond', type: 'government', maturity: '2029-12-31', coupon: 14.2, color: '#228B22' },
  { symbol: 'EGX:COMI_BOND', name: 'CIB Corporate Bond 2027', type: 'corporate', maturity: '2027-06-30', coupon: 12.8, color: '#8B4513' },
  { symbol: 'EGX:NBE_BOND', name: 'NBE Corporate Bond 2026', type: 'corporate', maturity: '2026-03-15', coupon: 11.2, color: '#CD853F' },
  { symbol: 'EGX:QNB_BOND', name: 'QNB ALAHLI Corporate Bond 2028', type: 'corporate', maturity: '2028-09-30', coupon: 13.1, color: '#A0522D' }
];

let stockData = {};
let bondData = {};
let charts = [];
let tvClient = null;

// Initialize TradingView connection
function initializeTradingView() {
  console.log('🇪🇬 Initializing EGX Stock Monitor Web Server...');
  
  tvClient = new TradingView.Client();
  
  // Initialize bonds data (simulated since bonds aren't typically real-time)
  initializeBondData();
  
  egxStocks.forEach((stock, index) => {
    const chart = new tvClient.Session.Chart();
    charts.push(chart);
    
    chart.setMarket(stock.symbol, {
      timeframe: '1D',
    });

    chart.onSymbolLoaded(() => {
      console.log(`✅ ${stock.name} connected`);
      
      // Send connection status to clients
      io.emit('stockConnected', {
        symbol: stock.symbol,
        name: stock.name,
        status: 'connected'
      });
    });

    chart.onUpdate(() => {
      if (!chart.periods[0]) return;
      
      const currentPrice = chart.periods[0].close;
      const openPrice = chart.periods[0].open;
      const highPrice = chart.periods[0].high;
      const lowPrice = chart.periods[0].low;
      const volume = chart.periods[0].volume || 0;
      
      const dailyChange = currentPrice - openPrice;
      const dailyChangePercent = ((dailyChange / openPrice) * 100);
      
      const stockInfo = {
        symbol: stock.symbol,
        name: stock.name,
        type: stock.type,
        color: stock.color,
        price: currentPrice,
        open: openPrice,
        high: highPrice,
        low: lowPrice,
        volume: volume,
        change: dailyChange,
        changePercent: dailyChangePercent,
        timestamp: new Date().toISOString()
      };
      
      stockData[stock.symbol] = stockInfo;
      
      // Broadcast to all connected clients
      io.emit('stockUpdate', stockInfo);
    });

    chart.onError((...err) => {
      console.error(`❌ Error with ${stock.name}:`, ...err);
      io.emit('stockError', {
        symbol: stock.symbol,
        name: stock.name,
        error: err.join(' ')
      });
    });
  });
}

// Initialize Bond Data with realistic Egyptian market data
function initializeBondData() {
  console.log('💰 Initializing Egyptian Bond Data...');
  
  egxBonds.forEach((bond, index) => {
    // Calculate realistic bond pricing based on current Egyptian market conditions
    const basePrice = 100000; // Face value basis
    const marketRate = 16.5; // Current Egyptian market interest rate
    const timeToMaturity = new Date(bond.maturity).getTime() - Date.now();
    const yearsToMaturity = timeToMaturity / (1000 * 60 * 60 * 24 * 365);
    
    // Calculate bond price using present value formula
    const couponPayment = (bond.coupon / 100) * basePrice;
    const discountRate = marketRate / 100;
    
    let currentPrice = basePrice;
    if (yearsToMaturity > 0) {
      // Present value of coupon payments + present value of principal
      const pvCoupons = couponPayment * ((1 - Math.pow(1 + discountRate, -yearsToMaturity)) / discountRate);
      const pvPrincipal = basePrice / Math.pow(1 + discountRate, yearsToMaturity);
      currentPrice = pvCoupons + pvPrincipal;
    }
    
    // Add some market volatility (±2%)
    const volatility = (Math.random() - 0.5) * 0.04;
    currentPrice = currentPrice * (1 + volatility);
    
    // Calculate yield to maturity
    const yieldToMaturity = bond.coupon + ((basePrice - currentPrice) / yearsToMaturity) / ((basePrice + currentPrice) / 2);
    
    bondData[bond.symbol] = {
      symbol: bond.symbol,
      name: bond.name,
      issuer: bond.name.includes('Government') ? 'Ministry of Finance' : bond.name.split(' ')[0],
      bond_type: bond.type,
      face_value: basePrice,
      current_price: Math.round(currentPrice),
      coupon_rate: bond.coupon,
      yield_to_maturity: Math.round(yieldToMaturity * 100) / 100,
      maturity_date: bond.maturity,
      rating: bond.type === 'government' ? 'B+' : 'BB-',
      price_change: Math.round((Math.random() - 0.5) * 1000),
      price_change_percent: Math.round((Math.random() - 0.5) * 2 * 100) / 100,
      last_updated: new Date().toISOString(),
      color: bond.color
    };
    
    console.log(`💰 ${bond.name} initialized - Price: ${Math.round(currentPrice)} EGP`);
  });
  
  // Update bond data every 5 minutes (bonds don't change as frequently as stocks)
  setInterval(() => {
    updateBondData();
  }, 5 * 60 * 1000);
}

// Update bond data with small market movements
function updateBondData() {
  console.log('💰 Updating bond prices...');
  
  Object.keys(bondData).forEach(symbol => {
    const bond = bondData[symbol];
    
    // Small price movements (±0.5%)
    const priceChange = (Math.random() - 0.5) * 0.01 * bond.current_price;
    const newPrice = Math.max(bond.current_price + priceChange, bond.face_value * 0.8); // Minimum 80% of face value
    
    const changeAmount = newPrice - bond.current_price;
    const changePercent = (changeAmount / bond.current_price) * 100;
    
    bondData[symbol] = {
      ...bond,
      current_price: Math.round(newPrice),
      price_change: Math.round(changeAmount),
      price_change_percent: Math.round(changePercent * 100) / 100,
      last_updated: new Date().toISOString()
    };
  });
  
  // Emit bond updates to connected clients
  io.emit('bondUpdate', Object.values(bondData));
}

// API Routes
app.get('/api/stocks', (req, res) => {
  res.json(Object.values(stockData));
});

app.get('/api/stocks/:symbol', (req, res) => {
  const symbol = req.params.symbol;
  const stock = stockData[symbol];
  
  if (stock) {
    res.json(stock);
  } else {
    res.status(404).json({ error: 'Stock not found' });
  }
});

// Technical Analysis endpoint
app.get('/api/technical-analysis/:symbol', async (req, res) => {
  try {
    const symbol = req.params.symbol;
    const markets = await TradingView.searchMarketV3(symbol);
    
    if (markets.length === 0) {
      return res.status(404).json({ error: 'Market not found' });
    }
    
    const market = markets[0];
    const ta = await market.getTA();
    
    res.json({
      symbol: symbol,
      description: market.description,
      recommendation: ta.summary.RECOMMENDATION,
      buy: ta.summary.BUY,
      sell: ta.summary.SELL,
      neutral: ta.summary.NEUTRAL,
      indicators: {
        rsi: ta.indicators.RSI,
        macd: ta.indicators.MACD,
        stoch: ta.indicators.Stoch,
        cci: ta.indicators.CCI
      },
      movingAverages: ta.summary.MA,
      oscillators: ta.summary.OSCILLATORS
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Enhanced EGX Stock Search - Prioritize real data, show all available stocks
app.get('/search', async (req, res) => {
  try {
    const query = req.query.q;
    if (!query) {
      return res.status(400).json({ error: 'Search query is required' });
    }

    console.log(`🔍 Searching EGX stocks for: "${query}"`);
    
    const results = [];
    const searchTerm = query.toUpperCase();
    
    // STEP 1: Check our real-time monitored stocks first
    console.log(`📊 Checking real-time monitored stocks...`);
    Object.values(stockData).forEach(stock => {
      const symbol = stock.symbol.replace('EGX:', '');
      if (symbol.includes(searchTerm) || stock.name.toUpperCase().includes(searchTerm)) {
        console.log(`✅ Found monitored stock: ${symbol} at ${stock.price} EGP`);
        results.push({
          symbol: symbol,
          name: stock.name,
          price: stock.price,
          change: stock.change,
          changePercent: stock.changePercent,
          volume: stock.volume,
          high: stock.high,
          low: stock.low,
          open: stock.open,
          source: 'real_time_monitored',
          timestamp: stock.timestamp,
          priority: 1 // Highest priority
        });
      }
    });
    
    // STEP 2: Search TradingView for additional EGX stocks
    try {
      console.log(`📈 Searching TradingView for EGX:${searchTerm}...`);
      const searchResults = await TradingView.searchMarketV3(`EGX:${searchTerm}`);
      
      console.log(`📊 TradingView found ${searchResults.length} results`);
      
      for (const market of searchResults.slice(0, 15)) { // Limit results
        const symbol = market.symbol.replace('EGX:', '');
        
        // Skip if we already have this from monitored stocks
        if (results.some(r => r.symbol === symbol)) {
          console.log(`⏭️ Skipping ${symbol} - already have real-time data`);
          continue;
        }
        
        console.log(`📊 Found TradingView symbol: ${symbol} (${market.description})`);
        
        // Try to get live quote data for this symbol using a temporary chart
        try {
          console.log(`📈 Fetching live quote for ${symbol}...`);
          
          // Create a temporary chart session to get quote
          const tempChart = new tvClient.Session.Chart();
          
          // Create a promise to handle the async nature of TradingView
          const quotePromise = new Promise((resolve, reject) => {
            let hasData = false;
            
            tempChart.setMarket(market.symbol, {
              timeframe: '1D',
            });
            
            tempChart.onSymbolLoaded(() => {
              console.log(`📊 Symbol ${symbol} loaded for quote`);
            });
            
            tempChart.onUpdate(() => {
              if (!hasData && tempChart.periods[0]) {
                hasData = true;
                const data = tempChart.periods[0];
                const currentPrice = data.close;
                const openPrice = data.open;
                const highPrice = data.high;
                const lowPrice = data.low;
                const volume = data.volume || 0;
                
                const dailyChange = currentPrice - openPrice;
                const dailyChangePercent = openPrice > 0 ? ((dailyChange / openPrice) * 100) : 0;
                
                console.log(`✅ Got live quote for ${symbol}: ${currentPrice} EGP`);
                
                resolve({
                  symbol: symbol,
                  name: market.description || getStockNameFromSymbol(symbol),
                  price: Math.round(currentPrice * 100) / 100,
                  change: Math.round(dailyChange * 100) / 100,
                  changePercent: Math.round(dailyChangePercent * 100) / 100,
                  volume: volume,
                  high: Math.round(highPrice * 100) / 100,
                  low: Math.round(lowPrice * 100) / 100,
                  open: Math.round(openPrice * 100) / 100,
                  source: 'trading_view_live',
                  timestamp: new Date().toISOString(),
                  priority: 2
                });
                
                // Clean up the temporary chart
                tempChart.delete();
              }
            });
            
            tempChart.onError((...err) => {
              console.warn(`⚠️ Error getting quote for ${symbol}:`, err);
              tempChart.delete();
              resolve(null);
            });
            
            // Timeout after 5 seconds
            setTimeout(() => {
              if (!hasData) {
                console.warn(`⏰ Timeout getting quote for ${symbol}`);
                tempChart.delete();
                resolve(null);
              }
            }, 5000);
          });
          
          const quoteResult = await quotePromise;
          
          if (quoteResult) {
            results.push(quoteResult);
          } else {
            console.warn(`⚠️ No quote data available for ${symbol}`);
            results.push({
              symbol: symbol,
              name: market.description || getStockNameFromSymbol(symbol),
              price: 0,
              change: 0,
              changePercent: 0,
              volume: 0,
              high: 0,
              low: 0,
              open: 0,
              source: 'trading_view_available',
              note: 'Available on TradingView - can be added for live monitoring',
              priority: 2
            });
          }
        } catch (quoteError) {
          console.warn(`⚠️ Error fetching quote for ${symbol}:`, quoteError.message);
          results.push({
            symbol: symbol,
            name: market.description || getStockNameFromSymbol(symbol),
            price: 0,
            change: 0,
            changePercent: 0,
            volume: 0,
            high: 0,
            low: 0,
            open: 0,
            source: 'trading_view_quote_error',
            note: 'Error fetching live quote from TradingView',
            priority: 2
          });
        }
      }
      
    } catch (tvError) {
      console.warn(`⚠️ TradingView search error:`, tvError.message);
    }
    
    // STEP 3: Search our comprehensive EGX symbol list as final fallback
    if (results.length < 5) {
      console.log(`📋 Searching comprehensive EGX symbol list...`);
      const matchingSymbols = allEgxSymbols.filter(symbol => 
        symbol.includes(searchTerm) || 
        getStockNameFromSymbol(symbol).toUpperCase().includes(searchTerm)
      ).slice(0, 10);
      
      for (const symbol of matchingSymbols) {
        // Skip if we already have this symbol
        if (results.some(r => r.symbol === symbol)) {
          continue;
        }
        
        console.log(`📋 Found in EGX list: ${symbol}`);
        results.push({
          symbol: symbol,
          name: getStockNameFromSymbol(symbol),
          price: 0,
          change: 0,
          changePercent: 0,
          volume: 0,
          high: 0,
          low: 0,
          open: 0,
          source: 'egx_symbol_list',
          note: 'EGX listed stock - may be available on TradingView',
          priority: 3 // Lowest priority
        });
      }
    }
    
    // Sort results by priority (real-time data first)
    results.sort((a, b) => a.priority - b.priority);
    
    console.log(`✅ Returning ${results.length} stocks for "${query}" (${results.filter(r => r.price > 0).length} with live data)`);
    res.json(results);
    
  } catch (error) {
    console.error('❌ Search error:', error);
    res.status(500).json({ error: 'Search failed', details: error.message });
  }
});

// Broader EGX search endpoint - searches all EGX stocks by partial match
app.get('/search-all', async (req, res) => {
  try {
    const query = req.query.q;
    if (!query) {
      return res.status(400).json({ error: 'Search query is required' });
    }

    console.log(`🔍 Searching ALL EGX stocks for: "${query}"`);
    
    // Search TradingView for EGX stocks with partial matches
    const searchResults = await TradingView.searchMarketV3(`EGX`);
    
    console.log(`📊 Found ${searchResults.length} total EGX stocks on TradingView`);
    
    // Filter by query
    const matchingResults = searchResults.filter(market => {
      const symbol = market.symbol.replace('EGX:', '');
      const description = market.description || '';
      const searchTerm = query.toUpperCase();
      
      return symbol.includes(searchTerm) || 
             description.toUpperCase().includes(searchTerm) ||
             searchTerm.includes(symbol);
    }).slice(0, 50); // Limit to 50 results
    
    console.log(`🎯 Found ${matchingResults.length} matching EGX stocks`);
    
    const results = [];
    
    for (const market of matchingResults) {
      try {
        const symbol = market.symbol.replace('EGX:', '');
        const egxSymbol = market.symbol;
        
        // Check cached data first
        if (stockData[egxSymbol]) {
          results.push({
            symbol: symbol,
            name: stockData[egxSymbol].name,
            price: stockData[egxSymbol].price,
            change: stockData[egxSymbol].change,
            changePercent: stockData[egxSymbol].changePercent,
            volume: stockData[egxSymbol].volume,
            high: stockData[egxSymbol].high,
            low: stockData[egxSymbol].low,
            open: stockData[egxSymbol].open,
            source: 'real_time_cached',
            timestamp: stockData[egxSymbol].timestamp
          });
          continue;
        }
        
        // Try to get quote data
        try {
          const quote = await market.getQuote();
          
          if (quote && quote.price) {
            results.push({
              symbol: symbol,
              name: market.description || symbol,
              price: Math.round(quote.price * 100) / 100,
              change: Math.round((quote.price - (quote.prev_close || quote.price)) * 100) / 100,
              changePercent: Math.round(((quote.price - (quote.prev_close || quote.price)) / (quote.prev_close || quote.price)) * 10000) / 100,
              volume: quote.volume || 0,
              high: Math.round((quote.high || quote.price) * 100) / 100,
              low: Math.round((quote.low || quote.price) * 100) / 100,
              open: Math.round((quote.open || quote.price) * 100) / 100,
              source: 'trading_view_quote',
              timestamp: new Date().toISOString()
            });
          } else {
            // Add symbol info even without quote
            results.push({
              symbol: symbol,
              name: market.description || symbol,
              price: 0,
              change: 0,
              changePercent: 0,
              volume: 0,
              high: 0,
              low: 0,
              open: 0,
              source: 'trading_view_no_quote',
              note: 'Available on TradingView but no current quote'
            });
          }
        } catch (quoteError) {
          // Add symbol info even with quote error
          results.push({
            symbol: symbol,
            name: market.description || symbol,
            price: 0,
            change: 0,
            changePercent: 0,
            volume: 0,
            high: 0,
            low: 0,
            open: 0,
            source: 'trading_view_quote_error',
            note: 'Available on TradingView - quote temporarily unavailable'
          });
        }
        
      } catch (error) {
        console.warn(`Error processing ${market.symbol}:`, error.message);
      }
    }
    
    console.log(`✅ Returning ${results.length} EGX stocks for "${query}"`);
    res.json(results);
    
  } catch (error) {
    console.error('❌ Search-all error:', error);
    res.status(500).json({ error: 'Search failed', details: error.message });
  }
});

// Dynamic stock management endpoint - Add new stocks to real-time monitoring
app.post('/add-stock', async (req, res) => {
  try {
    const { symbol, name, type = 'stock', color = '#999999' } = req.body;
    
    if (!symbol || !name) {
      return res.status(400).json({ error: 'Symbol and name are required' });
    }
    
    const fullSymbol = symbol.startsWith('EGX:') ? symbol : `EGX:${symbol}`;
    const cleanSymbol = symbol.replace('EGX:', '');
    
    // Check if already monitored
    if (egxStocks.find(s => s.symbol === fullSymbol)) {
      return res.json({ 
        message: `${cleanSymbol} is already being monitored`,
        status: 'already_exists'
      });
    }
    
    console.log(`➕ Adding ${cleanSymbol} to real-time monitoring...`);
    
    // Add to stocks array
    egxStocks.push({ symbol: fullSymbol, name, type, color });
    
    // Create new chart for this stock
    const chart = new tvClient.Session.Chart();
    charts.push(chart);
    
    chart.setMarket(fullSymbol, {
      timeframe: '1D',
    });

    chart.onSymbolLoaded(() => {
      console.log(`✅ ${name} connected and added to monitoring`);
      
      io.emit('stockConnected', {
        symbol: fullSymbol,
        name: name,
        status: 'connected'
      });
      
      res.json({
        message: `${cleanSymbol} successfully added to real-time monitoring`,
        status: 'success',
        symbol: cleanSymbol,
        name: name
      });
    });

    chart.onUpdate(() => {
      if (!chart.periods[0]) return;
      
      const currentPrice = chart.periods[0].close;
      const openPrice = chart.periods[0].open;
      const highPrice = chart.periods[0].high;
      const lowPrice = chart.periods[0].low;
      const volume = chart.periods[0].volume || 0;
      
      const dailyChange = currentPrice - openPrice;
      const dailyChangePercent = ((dailyChange / openPrice) * 100);
      
      const stockInfo = {
        symbol: fullSymbol,
        name: name,
        type: type,
        color: color,
        price: currentPrice,
        open: openPrice,
        high: highPrice,
        low: lowPrice,
        volume: volume,
        change: dailyChange,
        changePercent: dailyChangePercent,
        timestamp: new Date().toISOString()
      };
      
      stockData[fullSymbol] = stockInfo;
      io.emit('stockUpdate', stockInfo);
    });

    chart.onError((...err) => {
      console.error(`❌ Error adding ${name}:`, ...err);
      res.status(500).json({
        error: `Failed to add ${cleanSymbol} to monitoring`,
        details: err.join(' '),
        status: 'error'
      });
    });
    
  } catch (error) {
    console.error('❌ Add stock error:', error);
    res.status(500).json({ error: 'Failed to add stock', details: error.message });
  }
});

// Get list of currently monitored stocks
app.get('/monitored-stocks', (req, res) => {
  const monitoredList = egxStocks.map(stock => ({
    symbol: stock.symbol.replace('EGX:', ''),
    name: stock.name,
    type: stock.type,
    hasRealTimeData: !!stockData[stock.symbol],
    currentPrice: stockData[stock.symbol]?.price || null,
    lastUpdate: stockData[stock.symbol]?.timestamp || null
  }));
  
  res.json({
    total: monitoredList.length,
    stocks: monitoredList
  });
});

// Helper function to get stock name from symbol
function getStockNameFromSymbol(symbol) {
  const stockNames = {
    // Banks
    'COMI': 'Commercial International Bank',
    'NBKE': 'National Bank of Kuwait Egypt',
    'ABUK': 'Abu Dhabi Commercial Bank Egypt',
    'ADIB': 'Abu Dhabi Islamic Bank Egypt',
    'ALEX': 'Bank of Alexandria',
    'ATQA': 'Attijariwafa Bank Egypt',
    'AHLI': 'Ahli United Bank Egypt',
    'ARIB': 'Arab Investment Bank',
    'BCAI': 'Bank of Cairo',
    'BGAD': 'Banque du Caire',
    'BLAB': 'Blom Bank Egypt',
    'BMBE': 'Banque Misr',
    'BNKA': 'Banque Misr Liban',
    'BPLS': 'BPE Partners',
    'BRCE': 'Banque de Credit Egyptien',
    'CALD': 'Credit Agricole Egypt',
    'CAST': 'Cairo Amman Bank',
    'CCAP': 'Capital Bank',
    'EGBE': 'Egyptian Gulf Bank',
    'HRHO': 'EFG Holding',
    'FABD': 'Faisal Islamic Bank',
    'PIDG': 'Piraeus Bank Egypt',
    'QDIB': 'QNB Al Ahli Bank',
    'SAIB': 'Société Arabe Internationale de Banque',
    'SGFH': 'Solidarity Group Holding',
    'SKCB': 'Suez Canal Bank',
    'UBKR': 'United Bank',
    
    // Telecom & Technology
    'ETEL': 'Egypt Telecom',
    'ORTE': 'Orascom Telecom',
    'VTEL': 'Vodafone Egypt',
    'FWRY': 'Fawry Banking Technology',
    'MNHD': 'Madinet Nasr Housing',
    
    // Real Estate
    'PHDC': 'Palm Hills Development',
    'TMGH': 'TMG Holding',
    'TALAAT': 'Talaat Moustafa Group',
    'EHCD': 'Egypt Housing & Construction',
    'DSCW': 'Dscoop Capital Holding',
    'EHPH': 'Egypt Poultry Production',
    'MHDB': 'Misr Housing & Development Bank',
    'ELRW': 'El Rowad Housing & Construction',
    'EKPT': 'Egypt Kuwait Holding',
    'EHDB': 'Egypt Housing & Development Bank',
    'MCDR': 'Madinet Nasr Housing & Development',
    'THDR': 'Tharwa Capital Holding',
    'AREB': 'Arab Real Estate Bank',
    'ELTI': 'Egypt for Information Dissemination',
    'OSRM': 'Orascom Development Egypt',
    
    // Industrial
    'SWDY': 'El Sewedy Electric',
    'ORAS': 'Orascom Construction',
    'ARHO': 'Arab Cotton Ginning',
    'ARCE': 'Arab Ceramic',
    'BTEB': 'Beltone Financial Holding',
    'CTRA': 'Citadel Capital',
    'ECHEM': 'Egypt Chemical',
    'EGAS': 'Egyptian Gas',
    'EGCH': 'Egypt Chemical Industries',
    'EMFA': 'Egypt Medical Care',
    'ENAD': 'El Nasr Automotive',
    'ETRS': 'Egypt Trust',
    'EWDB': 'Egypt Wood',
    'FIRE': 'Egyptian Financial Group',
    'GTHE': 'Green Egypt',
    'IRON': 'Iron & Steel',
    'ISZP': 'Ismailia Pharmaceutical',
    'JUFO': 'Juhayna Food Industries',
    'LENS': 'Egyptian Lens',
    'MCAD': 'Misr Chemical',
    'OTMT': 'Oriental Textiles',
    'PMED': 'Pharaonic Medical',
    'POLY': 'Egyptian Polystyrene',
    'SCTR': 'Sector Financial',
    'SKPC': 'Sidi Kerir Petrochemicals',
    'SPED': 'Spiderman Industries',
    'SSTL': 'Suez Steel',
    'STAL': 'Steel Alexandria',
    'SUAM': 'Suez Automotive',
    'WEFR': 'Western Fertilizers',
    'WRFR': 'Warrak Fertilizers',
    
    // Pharmaceuticals
    'ISPH': 'Ibn Sina Pharma',
    'PHCI': 'Pharaonic Chemical Industries',
    'AMOC': 'Arab Medical Center',
    'MIPH': 'Misr International Pharmaceuticals',
    'MAAL': 'Misr Aluminum',
    'NIPH': 'Nile Pharmaceuticals',
    'EIPH': 'Egypt International Pharmaceuticals',
    'UEGC': 'United Egyptian Chemical',
  };
  
  return stockNames[symbol] || `${symbol} Company`;
}

// Get quotes for multiple symbols
app.get('/quotes', async (req, res) => {
  try {
    const symbols = req.query.symbols;
    if (!symbols) {
      return res.status(400).json({ error: 'Symbols parameter is required' });
    }
    
    const symbolArray = symbols.split(',').map(s => s.trim().toUpperCase());
    console.log(`📊 Getting quotes for: ${symbolArray.join(', ')}`);
    
    const results = [];
    
    for (const symbol of symbolArray) {
      try {
        const egxSymbol = formatEgxSymbol(symbol);
        
        // Check if we have real-time data for this symbol
        if (stockData[egxSymbol]) {
          results.push({
            symbol: symbol,
            ...stockData[egxSymbol],
            source: 'real_time'
          });
        } else {
          // Try to get data from TradingView search
          const markets = await TradingView.searchMarketV3(egxSymbol);
          if (markets.length > 0) {
            results.push({
              symbol: symbol,
              name: markets[0].description || `${symbol} Stock`,
              price: 0,
              change: 0,
              changePercent: 0,
              volume: 0,
              source: 'tradingview_search',
              note: 'Real-time data not available'
            });
          }
        }
      } catch (error) {
        console.warn(`Error getting quote for ${symbol}:`, error.message);
      }
    }
    
    res.json(results);
  } catch (error) {
    console.error('Quotes error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get individual stock quote - enhanced version
app.get('/quote/:symbol', async (req, res) => {
  try {
    const symbol = req.params.symbol.toUpperCase();
    const egxSymbol = formatEgxSymbol(symbol);
    
    console.log(`📈 Getting quote for: ${symbol}`);
    
    // Check if we have real-time data
    if (stockData[egxSymbol]) {
      return res.json({
        symbol: symbol,
        ...stockData[egxSymbol],
        source: 'real_time'
      });
    }
    
    // Try to get data from TradingView
    const markets = await TradingView.searchMarketV3(egxSymbol);
    if (markets.length === 0) {
      return res.status(404).json({ error: 'Stock not found on EGX' });
    }
    
    const market = markets[0];
    
    // Return basic info (live data would require setting up a chart)
    res.json({
      symbol: symbol,
      name: market.description || `${symbol} Stock`,
      price: 0,
      change: 0,
      changePercent: 0,
      volume: 0,
      high: 0,
      low: 0,
      open: 0,
      source: 'tradingview_search',
      note: 'Real-time data not available - add to monitored stocks for live updates'
    });
    
  } catch (error) {
    console.error(`Quote error for ${req.params.symbol}:`, error);
    res.status(500).json({ error: error.message });
  }
});

// Health check endpoint  
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'TradingView API Server is running',
    timestamp: new Date().toISOString(),
    stocks: Object.keys(stockData).length,
    bonds: Object.keys(bondData).length
  });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('👤 Client connected:', socket.id);
  
  // Send current stock data to new client
  socket.emit('initialData', Object.values(stockData));
  
  // Handle client requests for technical analysis
  socket.on('requestTechnicalAnalysis', async (symbol) => {
    try {
      const markets = await TradingView.searchMarketV3(symbol);
      if (markets.length > 0) {
        const market = markets[0];
        const ta = await market.getTA();
        
        socket.emit('technicalAnalysis', {
          symbol: symbol,
          data: ta
        });
      }
    } catch (error) {
      socket.emit('error', { message: error.message });
    }
  });
  
  socket.on('disconnect', () => {
    console.log('👤 Client disconnected:', socket.id);
  });
});

// Start server
const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  console.log(`🌐 EGX Stock Monitor Web Server running on http://localhost:${PORT}`);
  console.log('📊 Dashboard available at: http://localhost:3000');
  
  // Initialize TradingView
  initializeTradingView();
});
