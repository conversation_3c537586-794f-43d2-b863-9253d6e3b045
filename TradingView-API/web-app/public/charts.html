<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EGX Charts - Interactive Trading Charts</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1e1e1e;
            color: #ffffff;
        }

        .header {
            background: #2d2d2d;
            padding: 1rem 2rem;
            border-bottom: 1px solid #404040;
        }

        .header h1 {
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-tabs {
            background: #2d2d2d;
            padding: 0 2rem;
            display: flex;
            gap: 1rem;
            border-bottom: 1px solid #404040;
        }

        .nav-tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            color: #cccccc;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .nav-tab:hover {
            color: #ffffff;
            background: #404040;
        }

        .nav-tab.active {
            color: #4CAF50;
            border-bottom-color: #4CAF50;
        }

        .container {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 2rem;
            height: calc(100vh - 200px);
        }

        .main-chart {
            background: #2d2d2d;
            border-radius: 10px;
            overflow: hidden;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .widget-container {
            background: #2d2d2d;
            border-radius: 10px;
            padding: 1rem;
            height: fit-content;
        }

        .stock-selector {
            margin-bottom: 1rem;
        }

        .stock-selector select {
            width: 100%;
            padding: 0.5rem;
            background: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 5px;
        }

        .technical-summary {
            margin-top: 1rem;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #404040;
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .recommendation {
            padding: 1rem;
            border-radius: 5px;
            margin: 0.5rem 0;
            text-align: center;
            font-weight: bold;
        }

        .recommendation.buy {
            background: #4CAF50;
            color: white;
        }

        .recommendation.sell {
            background: #f44336;
            color: white;
        }

        .recommendation.neutral {
            background: #ff9800;
            color: white;
        }

        .market-overview {
            background: #2d2d2d;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .overview-card {
            background: #404040;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .overview-card h3 {
            color: #cccccc;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .overview-card .value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #4CAF50;
        }

        @media (max-width: 768px) {
            .chart-grid {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📈 EGX Interactive Charts</h1>
    </div>

    <div class="nav-tabs">
        <button class="nav-tab active" onclick="switchTab('charts')">Charts</button>
        <button class="nav-tab" onclick="switchTab('screener')">Screener</button>
        <button class="nav-tab" onclick="switchTab('heatmap')">Market Heatmap</button>
        <button class="nav-tab" onclick="switchTab('news')">Market News</button>
    </div>

    <div class="container">
        <div class="market-overview">
            <h2>🇪🇬 EGX Market Overview</h2>
            <div class="overview-grid">
                <div class="overview-card">
                    <h3>EGX 30 Index</h3>
                    <div class="value" id="egx30Value">Loading...</div>
                </div>
                <div class="overview-card">
                    <h3>Market Cap</h3>
                    <div class="value" id="marketCap">Loading...</div>
                </div>
                <div class="overview-card">
                    <h3>Volume</h3>
                    <div class="value" id="totalVolume">Loading...</div>
                </div>
                <div class="overview-card">
                    <h3>Active Stocks</h3>
                    <div class="value" id="activeStocks">Loading...</div>
                </div>
            </div>
        </div>

        <div class="chart-grid">
            <div class="main-chart">
                <!-- TradingView Advanced Chart Widget -->
                <div id="tradingview_chart"></div>
            </div>

            <div class="sidebar">
                <div class="widget-container">
                    <h3>Stock Selection</h3>
                    <div class="stock-selector">
                        <select id="stockSelect" onchange="changeStock()">
                            <option value="EGX:EGX30">EGX 30 Index</option>
                            <option value="EGX:COMI">Commercial International Bank</option>
                            <option value="EGX:SWDY">El Sewedy Electric</option>
                            <option value="EGX:FWRY">Fawry Banking Technology</option>
                            <option value="EGX:PHDC">Palm Hills Development</option>
                            <option value="EGX:ORAS">Orascom Construction</option>
                            <option value="EGX:ISPH">Ibn Sina Pharma</option>
                            <option value="EGX:HRHO">EFG Holding</option>
                        </select>
                    </div>
                </div>

                <div class="widget-container">
                    <h3>Technical Analysis</h3>
                    <div id="technicalSummary">
                        <div class="recommendation neutral" id="recommendation">
                            Loading...
                        </div>
                        <div class="technical-summary">
                            <div class="summary-item">
                                <span>RSI (14):</span>
                                <span id="rsi">--</span>
                            </div>
                            <div class="summary-item">
                                <span>MACD:</span>
                                <span id="macd">--</span>
                            </div>
                            <div class="summary-item">
                                <span>Moving Avg:</span>
                                <span id="movingAvg">--</span>
                            </div>
                            <div class="summary-item">
                                <span>Oscillators:</span>
                                <span id="oscillators">--</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="widget-container">
                    <!-- TradingView Mini Symbol Overview Widget -->
                    <div id="tradingview_overview"></div>
                </div>

                <div class="widget-container">
                    <!-- TradingView Economic Calendar Widget -->
                    <div id="tradingview_calendar"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- TradingView Widgets -->
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <script>
        let currentSymbol = 'EGX:EGX30';
        let widget = null;

        // Initialize TradingView Advanced Chart
        function initChart(symbol = 'EGX:EGX30') {
            if (widget) {
                widget.remove();
            }

            widget = new TradingView.widget({
                "width": "100%",
                "height": "100%",
                "symbol": symbol,
                "interval": "D",
                "timezone": "Africa/Cairo",
                "theme": "dark",
                "style": "1",
                "locale": "en",
                "toolbar_bg": "#f1f3f6",
                "enable_publishing": false,
                "hide_top_toolbar": false,
                "hide_legend": false,
                "save_image": true,
                "container_id": "tradingview_chart",
                "studies": [
                    "RSI@tv-basicstudies",
                    "MACD@tv-basicstudies",
                    "Volume@tv-basicstudies"
                ]
            });
        }

        // Initialize Symbol Overview Widget
        function initOverviewWidget(symbol = 'EGX:EGX30') {
            document.getElementById('tradingview_overview').innerHTML = '';
            
            new TradingView.MiniSymbolOverviewWidget({
                "symbol": symbol,
                "width": "100%",
                "height": 220,
                "locale": "en",
                "dateRange": "12M",
                "colorTheme": "dark",
                "trendLineColor": "rgba(76, 175, 80, 1)",
                "underLineColor": "rgba(76, 175, 80, 0.3)",
                "underLineBottomColor": "rgba(76, 175, 80, 0)",
                "isTransparent": false,
                "autosize": false,
                "largeChartUrl": "",
                "container_id": "tradingview_overview"
            });
        }

        // Initialize Economic Calendar Widget
        function initCalendarWidget() {
            new TradingView.EconomicCalendarWidget({
                "colorTheme": "dark",
                "isTransparent": false,
                "width": "100%",
                "height": 300,
                "locale": "en",
                "importanceFilter": "-1,0,1",
                "container_id": "tradingview_calendar"
            });
        }

        // Change stock symbol
        function changeStock() {
            const select = document.getElementById('stockSelect');
            currentSymbol = select.value;
            
            initChart(currentSymbol);
            initOverviewWidget(currentSymbol);
            loadTechnicalAnalysis(currentSymbol);
        }

        // Load technical analysis
        async function loadTechnicalAnalysis(symbol) {
            try {
                const response = await fetch(`/api/technical-analysis/${symbol}`);
                const data = await response.json();
                
                updateTechnicalDisplay(data);
            } catch (error) {
                console.error('Error loading technical analysis:', error);
            }
        }

        // Update technical analysis display
        function updateTechnicalDisplay(data) {
            const recommendation = document.getElementById('recommendation');
            const rsi = document.getElementById('rsi');
            const macd = document.getElementById('macd');
            const movingAvg = document.getElementById('movingAvg');
            const oscillators = document.getElementById('oscillators');

            if (data.recommendation) {
                recommendation.textContent = data.recommendation;
                recommendation.className = `recommendation ${data.recommendation.toLowerCase()}`;
            }

            if (data.indicators) {
                rsi.textContent = data.indicators.rsi ? data.indicators.rsi.toFixed(2) : '--';
                macd.textContent = data.indicators.macd ? data.indicators.macd.toFixed(4) : '--';
            }

            if (data.movingAverages) {
                movingAvg.textContent = data.movingAverages.RECOMMENDATION || '--';
            }

            if (data.oscillators) {
                oscillators.textContent = data.oscillators.RECOMMENDATION || '--';
            }
        }

        // Switch tabs
        function switchTab(tabName) {
            // Remove active class from all tabs
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Add active class to clicked tab
            event.target.classList.add('active');
            
            // Handle tab content switching
            switch(tabName) {
                case 'charts':
                    // Already showing charts
                    break;
                case 'screener':
                    // Implement screener functionality
                    alert('Screener functionality coming soon!');
                    break;
                case 'heatmap':
                    // Implement heatmap functionality
                    alert('Market heatmap coming soon!');
                    break;
                case 'news':
                    // Implement news functionality
                    alert('Market news coming soon!');
                    break;
            }
        }

        // Initialize widgets when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            initOverviewWidget();
            initCalendarWidget();
            loadTechnicalAnalysis(currentSymbol);
        });
    </script>
</body>
</html>
