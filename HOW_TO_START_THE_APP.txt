Financial Advisor - How to Start the Application
===========================================

To start the Financial Advisor application, use one of the following methods:

1. Simplified Start (Recommended):
   Run the following command:
   python run-simplified-app.py

2. Alternative Start Methods:
   If the simplified start doesn't work, try one of these alternatives:
   - python start_financial_advisor.py
   - python start_app_with_persistence.py
   - python run_app.py

3. Desktop Application:
   To start the desktop version of the application:
   python desktop_app.py
   or
   python ai_advisor_desktop.py

Note: The application has been cleaned up and all non-essential files have been moved to a backup directory.
The loan date issues have been fixed, and the application should now work correctly.
