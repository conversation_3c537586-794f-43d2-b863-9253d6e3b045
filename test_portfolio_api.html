<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .stock-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stock-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>Portfolio API Test Suite</h1>
    
    <div class="section">
        <h2>API Server Status</h2>
        <button onclick="testServerStatus()">Test Server Connection</button>
        <div id="serverStatus"></div>
    </div>

    <div class="section">
        <h2>Stock Search Test</h2>
        <button onclick="testStockSearch()">Test Stock Search</button>
        <div id="searchResults"></div>
    </div>

    <div class="section">
        <h2>Portfolio Calculation Test</h2>
        <button onclick="testPortfolioCalculation()">Test Portfolio Calculations</button>
        <div id="calculationResults"></div>
    </div>

    <div class="section">
        <h2>Real-time Data Test</h2>
        <button onclick="testRealTimeData()">Test Real-time Stock Prices</button>
        <div id="realTimeResults"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';

        async function testServerStatus() {
            const statusDiv = document.getElementById('serverStatus');
            statusDiv.innerHTML = '<div class="info">Testing server connection...</div>';

            try {
                const response = await fetch(`${API_BASE}/monitored-stocks`);
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.innerHTML = `
                        <div class="success">✓ Server is running and responding</div>
                        <div class="info">Monitored stocks: ${data.length}</div>
                        <pre>${JSON.stringify(data.slice(0, 3), null, 2)}</pre>
                    `;
                } else {
                    statusDiv.innerHTML = `<div class="error">✗ Server responded with status: ${response.status}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">✗ Connection failed: ${error.message}</div>`;
            }
        }

        async function testStockSearch() {
            const resultsDiv = document.getElementById('searchResults');
            resultsDiv.innerHTML = '<div class="info">Testing stock search...</div>';

            const testQueries = ['COMI', 'VALU', 'CIB', 'ABUK'];
            let results = [];

            for (const query of testQueries) {
                try {
                    const response = await fetch(`${API_BASE}/search?q=${query}`);
                    if (response.ok) {
                        const data = await response.json();
                        results.push({
                            query,
                            success: true,
                            count: data.length,
                            data: data.slice(0, 2)
                        });
                    } else {
                        results.push({
                            query,
                            success: false,
                            error: `HTTP ${response.status}`
                        });
                    }
                } catch (error) {
                    results.push({
                        query,
                        success: false,
                        error: error.message
                    });
                }
            }

            let html = '';
            results.forEach(result => {
                if (result.success) {
                    html += `
                        <div class="success">✓ Search for "${result.query}": ${result.count} results</div>
                        <pre>${JSON.stringify(result.data, null, 2)}</pre>
                    `;
                } else {
                    html += `<div class="error">✗ Search for "${result.query}" failed: ${result.error}</div>`;
                }
            });

            resultsDiv.innerHTML = html;
        }

        async function testPortfolioCalculation() {
            const resultsDiv = document.getElementById('calculationResults');
            resultsDiv.innerHTML = '<div class="info">Testing portfolio calculations...</div>';

            // Test portfolio with real EGX stocks
            const testPortfolio = {
                id: 'test-portfolio',
                name: 'Test Portfolio',
                description: 'Portfolio for testing real calculations',
                holdings: [
                    { symbol: 'COMI', quantity: 100, averagePrice: 75.50 },
                    { symbol: 'VALU', quantity: 200, averagePrice: 8.20 },
                    { symbol: 'CIB', quantity: 50, averagePrice: 90.00 },
                    { symbol: 'ABUK', quantity: 150, averagePrice: 15.75 }
                ]
            };

            try {
                // Get current prices for all holdings
                let totalCurrentValue = 0;
                let totalCostBasis = 0;
                let results = [];

                for (const holding of testPortfolio.holdings) {
                    const response = await fetch(`${API_BASE}/search?q=${holding.symbol}`);
                    if (response.ok) {
                        const searchResults = await response.json();
                        const stock = searchResults.find(s => s.symbol === holding.symbol);
                        
                        if (stock && stock.price > 0) {
                            const currentValue = stock.price * holding.quantity;
                            const costBasis = holding.averagePrice * holding.quantity;
                            const gainLoss = currentValue - costBasis;
                            const gainLossPercent = (gainLoss / costBasis) * 100;

                            totalCurrentValue += currentValue;
                            totalCostBasis += costBasis;

                            results.push({
                                symbol: holding.symbol,
                                quantity: holding.quantity,
                                averagePrice: holding.averagePrice,
                                currentPrice: stock.price,
                                costBasis: costBasis,
                                currentValue: currentValue,
                                gainLoss: gainLoss,
                                gainLossPercent: gainLossPercent
                            });
                        } else {
                            results.push({
                                symbol: holding.symbol,
                                error: 'Price not available'
                            });
                        }
                    }
                }

                const totalGainLoss = totalCurrentValue - totalCostBasis;
                const totalGainLossPercent = totalCostBasis > 0 ? (totalGainLoss / totalCostBasis) * 100 : 0;

                let html = `
                    <div class="success">✓ Portfolio calculation completed</div>
                    <div class="info">
                        <strong>Portfolio Summary:</strong><br>
                        Total Cost Basis: ${totalCostBasis.toFixed(2)} EGP<br>
                        Total Current Value: ${totalCurrentValue.toFixed(2)} EGP<br>
                        Total Gain/Loss: ${totalGainLoss.toFixed(2)} EGP (${totalGainLossPercent.toFixed(2)}%)
                    </div>
                    <div class="stock-grid">
                `;

                results.forEach(result => {
                    if (!result.error) {
                        const gainLossClass = result.gainLoss >= 0 ? 'success' : 'error';
                        html += `
                            <div class="stock-card">
                                <strong>${result.symbol}</strong><br>
                                Qty: ${result.quantity}<br>
                                Avg Price: ${result.averagePrice.toFixed(2)} EGP<br>
                                Current: ${result.currentPrice.toFixed(2)} EGP<br>
                                Value: ${result.currentValue.toFixed(2)} EGP<br>
                                <span class="${gainLossClass}">
                                    P&L: ${result.gainLoss.toFixed(2)} EGP (${result.gainLossPercent.toFixed(2)}%)
                                </span>
                            </div>
                        `;
                    } else {
                        html += `
                            <div class="stock-card">
                                <strong>${result.symbol}</strong><br>
                                <span class="error">${result.error}</span>
                            </div>
                        `;
                    }
                });

                html += '</div>';
                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">✗ Portfolio calculation failed: ${error.message}</div>`;
            }
        }

        async function testRealTimeData() {
            const resultsDiv = document.getElementById('realTimeResults');
            resultsDiv.innerHTML = '<div class="info">Testing real-time data quality...</div>';

            try {
                const response = await fetch(`${API_BASE}/monitored-stocks`);
                const stocks = await response.json();

                const realTimeStocks = stocks.filter(stock => stock.price > 0);
                const zeroOrInvalidPrices = stocks.filter(stock => !stock.price || stock.price <= 0);

                let html = `
                    <div class="success">✓ Real-time data test completed</div>
                    <div class="info">
                        Total monitored stocks: ${stocks.length}<br>
                        Stocks with valid prices: ${realTimeStocks.length}<br>
                        Stocks with invalid/zero prices: ${zeroOrInvalidPrices.length}
                    </div>
                `;

                if (realTimeStocks.length > 0) {
                    html += '<h4>Stocks with Valid Real-time Prices:</h4><div class="stock-grid">';
                    realTimeStocks.slice(0, 6).forEach(stock => {
                        html += `
                            <div class="stock-card">
                                <strong>${stock.symbol}</strong><br>
                                ${stock.name}<br>
                                Price: ${stock.price.toFixed(2)} EGP<br>
                                Change: ${stock.change ? stock.change.toFixed(2) : 'N/A'} EGP<br>
                                Volume: ${stock.volume ? stock.volume.toLocaleString() : 'N/A'}
                            </div>
                        `;
                    });
                    html += '</div>';
                }

                if (zeroOrInvalidPrices.length > 0) {
                    html += '<h4>Stocks with Invalid Prices:</h4>';
                    zeroOrInvalidPrices.forEach(stock => {
                        html += `<div class="error">✗ ${stock.symbol} (${stock.name}) - Price: ${stock.price || 'undefined'}</div>`;
                    });
                }

                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">✗ Real-time data test failed: ${error.message}</div>`;
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            console.log('Portfolio API Test Suite loaded');
        });
    </script>
</body>
</html>
