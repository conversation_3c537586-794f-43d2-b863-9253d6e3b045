import webview
import os
import sys
import subprocess
import time
import signal
import socket
from http.client import HTTPConnection
from data_storage import DataStorage

"""
Financial Advisor Desktop Application
------------------------------------
This application converts the Financial Advisor web app into a desktop application
with persistent data storage.
"""

# App configuration
APP_NAME = "Financial Advisor Pro"
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
DEFAULT_PORT = 5173
HOST = "localhost"
MAX_STARTUP_TIME = 60
DEBUG_MODE = False
FRAMELESS = False

# Global variables
server_process = None

def is_port_in_use(port):
    """Check if a port is already in use"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex((HOST, port)) == 0

def find_available_port():
    """Find an available port starting from the default port"""
    port = DEFAULT_PORT
    while is_port_in_use(port):
        port += 1
        if port > DEFAULT_PORT + 100:  # Avoid endless loop
            raise Exception(f"No available ports found in range {DEFAULT_PORT}-{DEFAULT_PORT+100}")
    return port

def wait_for_server(host, port, timeout=MAX_STARTUP_TIME):
    """Wait for the development server to become available"""
    print(f"Waiting for server to start on {host}:{port}...")
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            conn = HTTPConnection(host, port, timeout=1)
            conn.request("HEAD", "/")
            response = conn.getresponse()
            print(f"Server started successfully on {host}:{port}")
            return True
        except Exception as e:
            if DEBUG_MODE:
                print(f"Still waiting for server: {e}")
            time.sleep(0.5)
    return False

def run_dev_server():
    """Run the web development server in a separate process"""
    global server_process

    try:
        # Find an available port
        port = find_available_port()
        print(f"Using port: {port}")

        # Prepare the command with port specification
        command = "npm run dev"
        if "--port" not in command:
            command = f"{command} -- --port {port}"

        # Set environment variables for the dev server
        env = os.environ.copy()
        env["PORT"] = str(port)

        # Start the dev server
        if sys.platform == 'win32':
            server_process = subprocess.Popen(
                command,
                cwd=os.path.dirname(os.path.abspath(__file__)),
                env=env,
                shell=True,
                stdout=subprocess.PIPE if not DEBUG_MODE else None,
                stderr=subprocess.PIPE if not DEBUG_MODE else None,
                text=True
            )
        else:
            server_process = subprocess.Popen(
                command.split(),
                cwd=os.path.dirname(os.path.abspath(__file__)),
                env=env,
                stdout=subprocess.PIPE if not DEBUG_MODE else None,
                stderr=subprocess.PIPE if not DEBUG_MODE else None,
                text=True
            )

        # Wait for server to start
        if wait_for_server(HOST, port):
            return f"http://{HOST}:{port}"
        else:
            raise Exception(f"Development server failed to start within {MAX_STARTUP_TIME} seconds")

    except Exception as e:
        print(f"Error starting development server: {e}")
        sys.exit(1)

def cleanup():
    """Clean up resources when app exits"""
    global server_process

    if server_process:
        print("Shutting down development server...")
        try:
            if sys.platform == 'win32':
                subprocess.call(['taskkill', '/F', '/T', '/PID', str(server_process.pid)])
            else:
                os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
            print("Server process terminated")
        except Exception as e:
            print(f"Error shutting down server: {e}")

def launch_app():
    """Launch the desktop application with data storage functionality"""
    print(f"Starting {APP_NAME} desktop application...")

    try:
        # Initialize the data storage
        storage = DataStorage()

        # Start the dev server
        url = run_dev_server()

        # Create window options
        window_options = {
            'width': WINDOW_WIDTH,
            'height': WINDOW_HEIGHT,
            'min_size': (800, 600),
            'text_select': True,
            'frameless': FRAMELESS,
            'js_api': storage  # This exposes our Python storage methods to JavaScript
        }

        # Create the window
        window = webview.create_window(title=APP_NAME, url=url, **window_options)

        # Start the window - this will block until window is closed
        webview.start(debug=DEBUG_MODE)

    except Exception as e:
        print(f"Error in desktop app: {e}")

    finally:
        # Always cleanup when done
        cleanup()

if __name__ == "__main__":
    launch_app()
