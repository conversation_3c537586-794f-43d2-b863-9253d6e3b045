#!/usr/bin/env python3

"""
Phase 1 Completion Verification
===============================
Verify that Phase 1 critical fixes have been applied successfully.
"""

import sys
sys.path.append('.')
from data_storage import DataStorage
import json

def verify_phase1_fixes():
    """Verify that Phase 1 critical fixes have been applied"""
    
    print("✅ PHASE 1 VERIFICATION")
    print("=" * 50)
    
    storage = DataStorage()
    
    # Test 1: Loan Dates Fix
    print("\n1️⃣ LOAN DATES VERIFICATION")
    print("-" * 30)
    
    try:
        loans = storage.get_collection('loans')
        print(f"Found {len(loans)} loans")
        
        from datetime import datetime
        current_date = datetime.now()
        
        all_dates_good = True
        for loan in loans:
            if isinstance(loan, dict):
                name = loan.get('name', 'Unknown')
                next_payment = loan.get('next_payment_date')
                
                if next_payment:
                    try:
                        next_dt = datetime.fromisoformat(next_payment.replace('Z', '+00:00')).replace(tzinfo=None)
                        if next_dt >= current_date:
                            print(f"  ✅ {name}: Next payment {next_payment} (future date)")
                        else:
                            print(f"  ❌ {name}: Next payment {next_payment} (past date)")
                            all_dates_good = False
                    except:
                        print(f"  ⚠️ {name}: Invalid date format")
                        all_dates_good = False
                else:
                    print(f"  ℹ️ {name}: No next payment (loan may have ended)")
        
        if all_dates_good:
            print("  🎉 All loan dates are current/future!")
        else:
            print("  ⚠️ Some loan dates still need fixing")
            
    except Exception as e:
        print(f"  ❌ Error checking loans: {e}")
    
    # Test 2: Assets Data Integrity
    print("\n2️⃣ ASSETS DATA INTEGRITY VERIFICATION") 
    print("-" * 30)
    
    try:
        # Get all financial data
        bank_accounts = storage.get_collection('bank_accounts')
        cds = storage.get_collection('cds')
        assets = storage.get_collection('assets')
        
        # Calculate totals
        total_bank_balance = sum(float(acc.get('balance', 0)) for acc in bank_accounts)
        total_cd_value = sum(float(cd.get('principal', 0)) for cd in cds)
        total_liquid_assets = total_bank_balance + total_cd_value
        
        total_assets_value = sum(float(asset.get('value', 0)) for asset in assets)
        
        print(f"  💵 Liquid Assets: ${total_liquid_assets:,.2f}")
        print(f"  📊 Assets Table: ${total_assets_value:,.2f}")
        
        if total_assets_value >= total_liquid_assets:
            print("  ✅ Assets ≥ Liquid Assets (data integrity good)")
            integrity_good = True
        else:
            discrepancy = total_liquid_assets - total_assets_value
            print(f"  ❌ Assets < Liquid Assets by ${discrepancy:,.2f}")
            integrity_good = False
            
    except Exception as e:
        print(f"  ❌ Error checking assets: {e}")
        integrity_good = False
    
    # Test 3: Financial Calculations
    print("\n3️⃣ FINANCIAL CALCULATIONS VERIFICATION")
    print("-" * 30)
    
    try:
        # Test Dashboard calculations
        total_loan_balance = sum(float(loan.get('remaining_balance', 0)) for loan in loans)
        liabilities = storage.get_collection('liabilities')
        total_liabilities = sum(float(liability.get('amount', 0)) for liability in liabilities)
        
        net_worth = total_assets_value - total_liabilities
        
        print(f"  💰 Total Loan Balance: ${total_loan_balance:,.2f}")
        print(f"  💸 Total Liabilities: ${total_liabilities:,.2f}")
        print(f"  💎 Net Worth: ${net_worth:,.2f}")
        
        # Basic sanity checks
        calculations_good = True
        if total_loan_balance < 0:
            print("  ⚠️ Negative loan balance detected")
            calculations_good = False
        if net_worth < 0 and total_assets_value > 0:
            print("  ⚠️ Negative net worth with positive assets")
            calculations_good = False
            
        if calculations_good:
            print("  ✅ Financial calculations appear valid")
        else:
            print("  ⚠️ Some calculation issues detected")
            
    except Exception as e:
        print(f"  ❌ Error checking calculations: {e}")
        calculations_good = False
    
    # Overall Phase 1 Status
    print("\n🎯 PHASE 1 COMPLETION STATUS")
    print("-" * 30)
    
    if all_dates_good and integrity_good and calculations_good:
        print("🎉 PHASE 1 COMPLETE - All critical issues fixed!")
        print("✅ Ready to proceed with Phase 2 (Performance Optimization)")
        return True
    else:
        print("⚠️ PHASE 1 INCOMPLETE - Some issues remain:")
        if not all_dates_good:
            print("  • Loan dates still need fixing")
        if not integrity_good:
            print("  • Assets data integrity issue persists")
        if not calculations_good:
            print("  • Financial calculation issues")
        return False

def main():
    """Main function"""
    print("🔍 PHASE 1 VERIFICATION SCRIPT")
    print("=" * 50)
    print("Checking if critical data fixes have been applied successfully")
    print()
    
    try:
        success = verify_phase1_fixes()
        
        if success:
            print(f"\n🚀 READY FOR PHASE 2!")
            print("Next steps: Performance Optimization")
        else:
            print(f"\n🔧 PHASE 1 FIXES NEEDED")
            print("Review and address remaining issues before Phase 2")
            
    except Exception as e:
        print(f"\n❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
