#!/usr/bin/env python3
"""
Fix ENBD3 Remaining Balance

This script fixes the remaining balance for the ENBD3 loan in the Loan Coverage Fund.
The issue is that the remaining balance is showing the same as the principal amount,
suggesting that no payments have been made on this loan yet.
"""

import os
import sys
import json
import shutil
from datetime import datetime

# Configuration
DATA_DIR = os.path.join(os.path.expanduser("~"), ".financial_advisor")
LOANS_DIR = os.path.join(DATA_DIR, "loans")
BACKUP_DIR = os.path.join(DATA_DIR, "backups")

def make_backup():
    """Create a backup of the current loan data"""
    try:
        # Create backup directory if it doesn't exist
        os.makedirs(BACKUP_DIR, exist_ok=True)

        # Create a timestamp for the backup
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"loans_backup_before_enbd3_fix_{timestamp}"
        backup_path = os.path.join(BACKUP_DIR, backup_name)

        # Create backup directory
        os.makedirs(backup_path, exist_ok=True)

        # Copy all loan files
        if os.path.exists(LOANS_DIR):
            loan_files = [f for f in os.listdir(LOANS_DIR) if f.endswith('.json')]
            for loan_file in loan_files:
                src_path = os.path.join(LOANS_DIR, loan_file)
                dst_path = os.path.join(backup_path, loan_file)
                shutil.copy2(src_path, dst_path)

            print(f"Created backup at: {backup_path}")
            return True
        else:
            print(f"Loans directory not found at: {LOANS_DIR}")
            return False
    except Exception as e:
        print(f"Error creating backup: {e}")
        return False

def find_enbd3_loan():
    """Find the ENBD3 loan file"""
    try:
        # Check if the loans directory exists
        if not os.path.exists(LOANS_DIR):
            print(f"Loans directory not found at: {LOANS_DIR}")
            return None

        # Get all loan files
        loan_files = [f for f in os.listdir(LOANS_DIR) if f.endswith('.json')]

        # Find the ENBD3 loan
        enbd3_loan_file = None
        enbd3_loan_data = None

        for loan_file in loan_files:
            file_path = os.path.join(LOANS_DIR, loan_file)

            try:
                with open(file_path, 'r') as f:
                    loan_data = json.load(f)

                if isinstance(loan_data, dict) and loan_data.get('name') == 'ENBD3':
                    enbd3_loan_file = file_path
                    enbd3_loan_data = loan_data
                    break
            except json.JSONDecodeError:
                print(f"Error decoding JSON in file: {file_path}")
                continue

        if not enbd3_loan_file:
            print("ENBD3 loan not found in the loans directory.")
            return None

        return (enbd3_loan_file, enbd3_loan_data)
    except Exception as e:
        print(f"Error finding ENBD3 loan: {e}")
        return None

def fix_enbd3_remaining_balance():
    """Fix the remaining balance for the ENBD3 loan"""
    try:
        # Find the ENBD3 loan
        result = find_enbd3_loan()

        if not result:
            return False

        enbd3_loan_file, enbd3_loan = result

        # Print the current state of the ENBD3 loan
        print("\nCurrent state of ENBD3 loan:")
        print(f"  File: {os.path.basename(enbd3_loan_file)}")
        print(f"  Principal: {enbd3_loan.get('principal')}")
        print(f"  Remaining Balance: {enbd3_loan.get('remaining_balance')}")
        print(f"  Interest Rate: {enbd3_loan.get('interest_rate')}%")
        print(f"  Term Months: {enbd3_loan.get('term_months')}")
        print(f"  Next Payment Date: {enbd3_loan.get('next_payment_date')}")
        print(f"  End Date: {enbd3_loan.get('end_date', 'Not set')}")

        # Calculate a more realistic remaining balance
        principal = float(enbd3_loan.get('principal', 0))

        # Ask the user for the percentage of principal to use as remaining balance
        print("\nPlease enter the percentage of principal to use as remaining balance.")
        print("For example, if the loan is new, enter 100. If some payments have been made, enter a lower value.")
        percentage = input("Percentage (1-100): ")

        try:
            percentage = float(percentage)
            if percentage < 1 or percentage > 100:
                print("Invalid percentage. Using default of 95%.")
                percentage = 95
        except ValueError:
            print("Invalid input. Using default of 95%.")
            percentage = 95

        new_remaining_balance = principal * (percentage / 100)

        # Update the remaining balance
        enbd3_loan['remaining_balance'] = new_remaining_balance

        # Write the updated loan back to the file
        with open(enbd3_loan_file, 'w') as f:
            json.dump(enbd3_loan, f, indent=2)

        print(f"\nUpdated ENBD3 loan remaining balance to: {new_remaining_balance}")
        print(f"This is {percentage}% of the principal amount.")

        return True
    except Exception as e:
        print(f"Error fixing ENBD3 remaining balance: {e}")
        return False

def main():
    print("\nFinancial Advisor - Fix ENBD3 Remaining Balance")
    print("=" * 60)
    print("This script fixes the remaining balance for the ENBD3 loan in the Loan Coverage Fund.")

    # Create a backup
    if not make_backup():
        print("Failed to create backup. Aborting.")
        return

    # Fix the ENBD3 remaining balance
    if fix_enbd3_remaining_balance():
        print("\nSuccessfully fixed the ENBD3 remaining balance.")
        print("Please restart the application to see the changes.")
    else:
        print("\nFailed to fix the ENBD3 remaining balance.")

if __name__ == "__main__":
    main()
