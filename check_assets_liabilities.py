#!/usr/bin/env python3
"""
Check Assets and Liabilities Data
================================
Compare the data sources used by Dashboard vs Net Worth page.
"""

import sys
sys.path.append('.')
from data_storage import DataStorage

def check_assets_and_liabilities():
    """Check what's in assets and liabilities vs bank accounts and CDs"""
    try:
        storage = DataStorage()
        
        # Get all the different data sources
        assets = storage.get_collection('assets')
        liabilities = storage.get_collection('liabilities') 
        bank_accounts = storage.get_collection('bank_accounts')
        cds = storage.get_collection('cds')
        loans = storage.get_collection('loans')
        
        print('DATA SOURCE COMPARISON')
        print('=' * 60)
        
        # Dashboard calculation
        print('\n🖥️  DASHBOARD CALCULATION:')
        print('-' * 30)
        total_bank_balance = sum(acc.get('balance', 0) for acc in bank_accounts)
        total_cd_value = sum(cd.get('principal', 0) for cd in cds)
        total_loan_balance = sum(loan.get('remaining_balance', 0) for loan in loans)
        
        dashboard_assets = total_bank_balance + total_cd_value
        dashboard_net_worth = dashboard_assets - total_loan_balance
        
        print(f'Bank Accounts Total: {total_bank_balance:,.2f}')
        print(f'CDs Total: {total_cd_value:,.2f}')
        print(f'Total Assets: {dashboard_assets:,.2f}')
        print(f'Loan Balance: {total_loan_balance:,.2f}')
        print(f'NET WORTH: {dashboard_net_worth:,.2f}')
        
        # Net Worth page calculation
        print('\n📊 NET WORTH PAGE CALCULATION:')
        print('-' * 30)
        
        if assets:
            print('Assets table:')
            total_assets_value = 0
            for asset in assets:
                value = asset.get('value', 0)
                total_assets_value += value
                print(f'  - {asset.get("name", "Unknown")}: {value:,.2f}')
            print(f'Total Assets: {total_assets_value:,.2f}')
        else:
            print('No assets found in assets table')
            total_assets_value = 0
            
        if liabilities:
            print('Liabilities table:')
            total_liabilities_value = 0
            for liability in liabilities:
                amount = liability.get('amount', 0)
                total_liabilities_value += amount
                print(f'  - {liability.get("name", "Unknown")}: {amount:,.2f}')
            print(f'Total Liabilities: {total_liabilities_value:,.2f}')
        else:
            print('No liabilities found in liabilities table')
            total_liabilities_value = 0
            
        networth_page_net_worth = total_assets_value - total_liabilities_value
        print(f'NET WORTH: {networth_page_net_worth:,.2f}')
        
        # Comparison
        print('\n🔍 COMPARISON:')
        print('-' * 30)
        print(f'Dashboard Net Worth:  {dashboard_net_worth:,.2f}')
        print(f'Net Worth Page:       {networth_page_net_worth:,.2f}')
        print(f'Difference:           {dashboard_net_worth - networth_page_net_worth:,.2f}')
        
        # Detailed breakdown
        print('\n📋 DETAILED DATA:')
        print('-' * 30)
        
        print(f'\nBank Accounts ({len(bank_accounts)}):')
        for acc in bank_accounts:
            print(f'  - {acc.get("name", "Unknown")}: {acc.get("balance", 0):,.2f}')
            
        print(f'\nCDs ({len(cds)}):')
        for cd in cds:
            print(f'  - {cd.get("institution", "Unknown")}: {cd.get("principal", 0):,.2f}')
            
        print(f'\nLoans ({len(loans)}):')
        for loan in loans:
            print(f'  - {loan.get("name", "Unknown")}: {loan.get("remaining_balance", 0):,.2f}')
        
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_assets_and_liabilities()
