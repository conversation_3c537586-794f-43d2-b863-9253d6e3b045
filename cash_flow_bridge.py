"""
Cash Flow Analysis Bridge Module
Integrates the advanced cash flow analysis from the Loans app into the main Financial Advisor app
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
from pathlib import Path

class CashFlowAnalyzer:
    """
    Cash Flow Analysis engine adapted from the Loans app
    Provides advanced cash flow projections, risk analysis, and recommendations
    """
    
    def __init__(self, data_dir: str = None):
        if data_dir is None:
            data_dir = os.path.join(os.path.expanduser("~"), ".financial_advisor")
        self.data_dir = data_dir
        self.currency = "EGP"
        
    def get_current_financial_situation(self) -> Dict[str, Any]:
        """Get current financial situation summary"""
        try:
            # Load data from the existing storage system
            loans = self._load_collection('loans') or []
            cds = self._load_collection('cds') or []
            bank_accounts = self._load_collection('bank_accounts') or []
            
            # Calculate loan payments
            total_loan_payments = sum(loan.get('monthly_payment', 0) for loan in loans)
            
            # Calculate CD income
            total_cd_income = 0
            cib_income = 0
            other_cd_income = 0
            
            for cd in cds:
                monthly_income = cd.get('principal', 0) * (cd.get('interest_rate', 0) / 100) / 12
                total_cd_income += monthly_income
                
                if 'cib' in cd.get('institution', '').lower():
                    cib_income += monthly_income
                else:
                    other_cd_income += monthly_income
            
            # Calculate funding gap
            funding_gap = max(0, total_loan_payments - other_cd_income)
            cd_coverage_percentage = (other_cd_income / total_loan_payments * 100) if total_loan_payments > 0 else 0
            
            # Get ENBD Current Plus balance
            enbd_balance = 0
            for account in bank_accounts:
                if (account.get('bank_name', '').lower() == 'enbd' and 
                    'current' in account.get('account_type', '').lower()):
                    enbd_balance = account.get('balance', 0)
                    break
            
            return {
                "total_cd_income": total_cd_income,
                "cib_available_income": cib_income,
                "cd_income_for_loans": other_cd_income,
                "total_loan_payments": total_loan_payments,
                "funding_gap": funding_gap,
                "estimated_living_expenses": 55000,  # Default value
                "monthly_shortfall": funding_gap,
                "cd_coverage_percentage": cd_coverage_percentage,
                "current_balance": enbd_balance,
                "currency": self.currency
            }
            
        except Exception as e:
            print(f"Error calculating financial situation: {e}")
            return self._get_default_situation()
    
    def generate_cash_flow_projection(self, 
                                    months_ahead: int = 60,
                                    starting_balance: float = None,
                                    custom_living_expenses: float = None) -> List[Dict[str, Any]]:
        """Generate dynamic cash flow projection"""
        try:
            situation = self.get_current_financial_situation()
            loans = self._load_collection('loans') or []
            
            if starting_balance is None:
                starting_balance = situation['current_balance']
            
            if custom_living_expenses is None:
                custom_living_expenses = situation['estimated_living_expenses']
            
            # Create loan payment schedule
            loan_schedule = self._create_loan_schedule(loans, months_ahead)
            
            projections = []
            current_balance = starting_balance
            start_date = datetime.now()
            
            for month in range(months_ahead):
                projection_date = start_date + timedelta(days=month * 30)
                
                # Get loan payments for this month
                month_loan_payments = loan_schedule.get(month, situation['total_loan_payments'])
                
                # Calculate shortfall
                loan_shortfall = max(0, month_loan_payments - situation['cd_income_for_loans'])
                
                # Monthly change calculation
                monthly_income = situation['cib_available_income']
                total_outflow = loan_shortfall
                net_change = monthly_income - total_outflow
                
                starting_balance_for_period = current_balance
                current_balance += net_change
                
                projections.append({
                    "period": month + 1,
                    "date": projection_date.strftime("%Y-%m-%d"),
                    "starting_balance": starting_balance_for_period,
                    "monthly_income": monthly_income,
                    "living_expenses": custom_living_expenses,
                    "loan_payments": month_loan_payments,
                    "cd_income_for_loans": situation['cd_income_for_loans'],
                    "loan_shortfall": loan_shortfall,
                    "total_outflow": total_outflow,
                    "net_change": net_change,
                    "ending_balance": current_balance,
                    "active_loans": self._count_active_loans(loans, month),
                    "status": "Positive" if current_balance >= 0 else "Negative"
                })
            
            return projections
            
        except Exception as e:
            print(f"Error generating cash flow projection: {e}")
            return []
    
    def analyze_critical_dates(self, projections: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze critical dates from projection data"""
        try:
            critical_analysis = {
                "first_negative": None,
                "very_negative": None,
                "last_positive": None,
                "total_negative_months": 0,
                "cash_runway_months": float('inf'),
                "risk_level": "LOW"
            }
            
            # Find critical points
            negative_projections = [p for p in projections if p['status'] == 'Negative']
            
            if negative_projections:
                critical_analysis["first_negative"] = negative_projections[0]
                critical_analysis["total_negative_months"] = len(negative_projections)
                critical_analysis["cash_runway_months"] = negative_projections[0]["period"]
                
                # Determine risk level
                months_to_negative = negative_projections[0]["period"]
                if months_to_negative <= 3:
                    critical_analysis["risk_level"] = "CRITICAL"
                elif months_to_negative <= 12:
                    critical_analysis["risk_level"] = "HIGH"
                elif months_to_negative <= 24:
                    critical_analysis["risk_level"] = "MEDIUM"
                else:
                    critical_analysis["risk_level"] = "LOW"
            
            # Find very negative (below -500,000)
            very_negative = [p for p in projections if p['ending_balance'] < -500000]
            if very_negative:
                critical_analysis["very_negative"] = very_negative[0]
            
            # Find last positive
            positive_projections = [p for p in projections if p['status'] == 'Positive']
            if positive_projections:
                critical_analysis["last_positive"] = positive_projections[-1]
            
            return critical_analysis
            
        except Exception as e:
            print(f"Error analyzing critical dates: {e}")
            return {"risk_level": "UNKNOWN", "first_negative": None}
    
    def get_optimization_recommendations(self, situation: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate optimization recommendations"""
        recommendations = []
        
        try:
            # Expense reduction scenarios
            current_expenses = situation['estimated_living_expenses']
            
            for reduction_pct in [10, 20, 30]:
                monthly_savings = current_expenses * (reduction_pct / 100)
                recommendations.append({
                    "type": "expense_reduction",
                    "title": f"Reduce Living Expenses by {reduction_pct}%",
                    "monthly_impact": monthly_savings,
                    "annual_impact": monthly_savings * 12,
                    "description": f"Cut monthly expenses by {self.format_currency(monthly_savings)}",
                    "feasibility": "High" if reduction_pct <= 15 else "Medium" if reduction_pct <= 25 else "Low"
                })
            
            # Income generation scenarios
            for income_boost in [25000, 50000, 75000, 100000]:
                recommendations.append({
                    "type": "income_generation",
                    "title": f"Generate Additional Income +{self.format_currency(income_boost)}/month",
                    "monthly_impact": income_boost,
                    "annual_impact": income_boost * 12,
                    "description": f"Add {self.format_currency(income_boost)} monthly income through investments or side business",
                    "feasibility": "High" if income_boost <= 50000 else "Medium" if income_boost <= 75000 else "Low"
                })
            
            # Cash injection scenarios
            funding_gap = situation['funding_gap']
            if funding_gap > 0:
                # Calculate minimum injection needed
                runway_months = 6  # Target 6 months runway
                min_injection = funding_gap * runway_months
                
                for injection_amount in [min_injection, min_injection * 1.5, min_injection * 2]:
                    recommendations.append({
                        "type": "cash_injection",
                        "title": f"Cash Injection: {self.format_currency(injection_amount)}",
                        "monthly_impact": 0,
                        "annual_impact": -injection_amount,  # One-time cost
                        "description": f"Inject {self.format_currency(injection_amount)} to extend cash runway",
                        "feasibility": "High" if injection_amount <= min_injection * 1.2 else "Medium"
                    })
            
            return sorted(recommendations, key=lambda x: x['monthly_impact'], reverse=True)
            
        except Exception as e:
            print(f"Error generating recommendations: {e}")
            return []
    
    def format_currency(self, amount: float) -> str:
        """Format amount as currency"""
        return f"EGP {amount:,.0f}"
    
    def _load_collection(self, collection_name: str) -> List[Dict[str, Any]]:
        """Load data from the storage system"""
        try:
            # Try to load from the existing data storage
            file_path = os.path.join(self.data_dir, f"{collection_name}.json")
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
            # Try to load individual items
            index_file = os.path.join(self.data_dir, f"{collection_name}_index.json")
            if os.path.exists(index_file):
                with open(index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                    items = []
                    for item_id in index_data.get('items', []):
                        item_file = os.path.join(self.data_dir, collection_name, f"{item_id}.json")
                        if os.path.exists(item_file):
                            with open(item_file, 'r', encoding='utf-8') as item_f:
                                items.append(json.load(item_f))
                    return items
            
            return []
            
        except Exception as e:
            print(f"Error loading collection {collection_name}: {e}")
            return []
    
    def _create_loan_schedule(self, loans: List[Dict[str, Any]], months_ahead: int) -> Dict[int, float]:
        """Create monthly loan payment schedule considering end dates"""
        schedule = {}
        
        try:
            for month in range(months_ahead):
                total_payment = 0
                current_date = datetime.now() + timedelta(days=month * 30)
                
                for loan in loans:
                    # Check if loan is still active
                    end_date_str = loan.get('next_payment_date', '')
                    if end_date_str:
                        try:
                            # Add estimated term to next payment date
                            next_payment = datetime.strptime(end_date_str, '%Y-%m-%d')
                            term_months = loan.get('term_months', 12)
                            loan_end_date = next_payment + timedelta(days=term_months * 30)
                            
                            if current_date < loan_end_date:
                                total_payment += loan.get('monthly_payment', 0)
                        except:
                            # If date parsing fails, assume loan is active
                            total_payment += loan.get('monthly_payment', 0)
                    else:
                        total_payment += loan.get('monthly_payment', 0)
                
                schedule[month] = total_payment
            
            return schedule
            
        except Exception as e:
            print(f"Error creating loan schedule: {e}")
            # Return default schedule
            total_payment = sum(loan.get('monthly_payment', 0) for loan in loans)
            return {month: total_payment for month in range(months_ahead)}
    
    def _count_active_loans(self, loans: List[Dict[str, Any]], month: int) -> int:
        """Count active loans for a given month"""
        try:
            active_count = 0
            current_date = datetime.now() + timedelta(days=month * 30)
            
            for loan in loans:
                end_date_str = loan.get('next_payment_date', '')
                if end_date_str:
                    try:
                        next_payment = datetime.strptime(end_date_str, '%Y-%m-%d')
                        term_months = loan.get('term_months', 12)
                        loan_end_date = next_payment + timedelta(days=term_months * 30)
                        
                        if current_date < loan_end_date:
                            active_count += 1
                    except:
                        active_count += 1
                else:
                    active_count += 1
            
            return active_count
            
        except Exception as e:
            print(f"Error counting active loans: {e}")
            return len(loans)
    
    def _get_default_situation(self) -> Dict[str, Any]:
        """Return default financial situation if data loading fails"""
        return {
            "total_cd_income": 0,
            "cib_available_income": 0,
            "cd_income_for_loans": 0,
            "total_loan_payments": 0,
            "funding_gap": 0,
            "estimated_living_expenses": 55000,
            "monthly_shortfall": 0,
            "cd_coverage_percentage": 0,
            "current_balance": 0,
            "currency": self.currency
        }

# Export API for integration with the web app
def get_cash_flow_api():
    """Get the cash flow analysis API for web integration"""
    analyzer = CashFlowAnalyzer()
    
    return {
        "get_current_situation": analyzer.get_current_financial_situation,
        "generate_projection": analyzer.generate_cash_flow_projection,
        "analyze_critical_dates": analyzer.analyze_critical_dates,
        "get_recommendations": analyzer.get_optimization_recommendations,
        "format_currency": analyzer.format_currency
    }

# Integration test function
def test_integration():
    """Test the integration functionality"""
    try:
        analyzer = CashFlowAnalyzer()
        
        print("🧪 Testing Cash Flow Analysis Integration...")
        
        # Test current situation
        situation = analyzer.get_current_financial_situation()
        print(f"✅ Current situation loaded: {analyzer.format_currency(situation['total_cd_income'])} CD income")
        
        # Test projection
        projections = analyzer.generate_cash_flow_projection(months_ahead=12)
        print(f"✅ Generated {len(projections)} months of projections")
        
        # Test critical analysis
        if projections:
            analysis = analyzer.analyze_critical_dates(projections)
            print(f"✅ Risk analysis complete: {analysis['risk_level']} risk level")
        
        # Test recommendations
        recommendations = analyzer.get_optimization_recommendations(situation)
        print(f"✅ Generated {len(recommendations)} recommendations")
        
        print("🎉 Integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

if __name__ == "__main__":
    test_integration()
