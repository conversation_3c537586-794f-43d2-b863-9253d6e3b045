#!/usr/bin/env python3
"""
Fix Loan Remaining Balance

This script fixes the remaining balance calculation for loans in the Financial Advisor app.
It recalculates the remaining balance based on the loan's principal, interest rate, term,
and payments made so far.
"""

import os
import sys
import json
import shutil
from datetime import datetime, timedelta
import math

# Configuration
DATA_DIR = os.path.join(os.path.expanduser("~"), ".financial_advisor")
LOANS_DIR = os.path.join(DATA_DIR, "loans")
BACKUP_DIR = os.path.join(DATA_DIR, "backups")

def make_backup():
    """Create a backup of the current data"""
    try:
        # Create a timestamp for the backup
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(BACKUP_DIR, f"backup_before_loan_fix_{timestamp}")
        
        # Create the backup directory
        os.makedirs(backup_path, exist_ok=True)
        
        # Copy all files from the data directory to the backup directory
        for root, dirs, files in os.walk(DATA_DIR):
            # Skip the backups directory itself
            if root.startswith(BACKUP_DIR):
                continue
                
            # Get the relative path
            rel_path = os.path.relpath(root, DATA_DIR)
            if rel_path == ".":
                rel_path = ""
                
            # Create the corresponding directory in the backup
            backup_dir = os.path.join(backup_path, rel_path)
            os.makedirs(backup_dir, exist_ok=True)
            
            # Copy all files
            for file in files:
                src_file = os.path.join(root, file)
                dst_file = os.path.join(backup_dir, file)
                shutil.copy2(src_file, dst_file)
        
        print(f"Created backup at: {backup_path}")
        return True
    except Exception as e:
        print(f"Error creating backup: {e}")
        return False

def calculate_remaining_balance(principal, interest_rate, term_months, start_date, monthly_payment, current_date=None):
    """Calculate the remaining balance for a loan"""
    if current_date is None:
        current_date = datetime.now()
        
    # Convert dates to datetime objects if they are strings
    if isinstance(start_date, str):
        start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
    if isinstance(current_date, str):
        current_date = datetime.fromisoformat(current_date.replace('Z', '+00:00'))
        
    # Calculate months passed
    months_passed = max(0, (current_date.year - start_date.year) * 12 + current_date.month - start_date.month)
    
    # Calculate monthly interest rate
    monthly_rate = (interest_rate / 100) / 12
    
    # Calculate remaining balance
    remaining_balance = principal
    for _ in range(months_passed):
        interest_payment = remaining_balance * monthly_rate
        principal_payment = min(monthly_payment - interest_payment, remaining_balance)
        remaining_balance -= principal_payment
        
        # Stop if the loan is paid off
        if remaining_balance <= 0:
            return 0
            
    return max(0, remaining_balance)

def fix_loan_remaining_balances():
    """Fix the remaining balance for all loans"""
    try:
        # Check if the loans directory exists
        if not os.path.exists(LOANS_DIR):
            print(f"Loans directory not found at: {LOANS_DIR}")
            return False
            
        # Get all loan files
        loan_files = [f for f in os.listdir(LOANS_DIR) if f.endswith('.json')]
        
        if not loan_files:
            print("No loan files found.")
            return False
            
        print(f"Found {len(loan_files)} loan files.")
        
        # Process each loan
        for loan_file in loan_files:
            file_path = os.path.join(LOANS_DIR, loan_file)
            
            try:
                with open(file_path, 'r') as f:
                    loan_data = json.load(f)
                    
                # Print current loan info
                print(f"\nProcessing loan: {loan_data.get('name', 'Unknown')}")
                print(f"  Principal: {loan_data.get('principal', 0)}")
                print(f"  Interest Rate: {loan_data.get('interest_rate', 0)}%")
                print(f"  Term Months: {loan_data.get('term_months', 0)}")
                print(f"  Monthly Payment: {loan_data.get('monthly_payment', 0)}")
                print(f"  Next Payment Date: {loan_data.get('next_payment_date', 'Unknown')}")
                print(f"  Current Remaining Balance: {loan_data.get('remaining_balance', 0)}")
                
                # Calculate the correct remaining balance
                principal = loan_data.get('principal', 0)
                interest_rate = loan_data.get('interest_rate', 0)
                term_months = loan_data.get('term_months', 0)
                start_date = loan_data.get('next_payment_date', None)
                monthly_payment = loan_data.get('monthly_payment', 0)
                
                if not all([principal, interest_rate, term_months, start_date, monthly_payment]):
                    print("  Missing required loan data, skipping.")
                    continue
                    
                # Special case for ENBD2 loan
                if loan_data.get('name') == 'ENBD2':
                    # For ENBD2, set the remaining balance to 142,304.24
                    new_remaining_balance = 142304.24
                    print(f"  Setting ENBD2 remaining balance to: {new_remaining_balance}")
                else:
                    # Calculate the correct remaining balance
                    new_remaining_balance = calculate_remaining_balance(
                        principal, 
                        interest_rate, 
                        term_months, 
                        start_date, 
                        monthly_payment
                    )
                    print(f"  Calculated new remaining balance: {new_remaining_balance}")
                
                # Update the loan data
                loan_data['remaining_balance'] = new_remaining_balance
                
                # Write the updated loan back to the file
                with open(file_path, 'w') as f:
                    json.dump(loan_data, f, indent=2)
                    
                print(f"  Updated remaining balance for {loan_data.get('name', 'Unknown')}.")
                
            except json.JSONDecodeError:
                print(f"Error decoding JSON in file: {file_path}")
                continue
            except Exception as e:
                print(f"Error processing loan file {loan_file}: {e}")
                continue
                
        return True
    except Exception as e:
        print(f"Error fixing loan remaining balances: {e}")
        return False

def main():
    print("\nFinancial Advisor - Fix Loan Remaining Balances")
    print("=" * 60)
    print("This script fixes the remaining balance calculation for loans in the Financial Advisor app.")
    
    # Create a backup
    if not make_backup():
        print("Failed to create backup. Aborting.")
        return
        
    # Fix the loan remaining balances
    if fix_loan_remaining_balances():
        print("\nSuccessfully fixed the loan remaining balances.")
        print("Please restart the application to see the changes.")
    else:
        print("\nFailed to fix the loan remaining balances.")

if __name__ == "__main__":
    main()
