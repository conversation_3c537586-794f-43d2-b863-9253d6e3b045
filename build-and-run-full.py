import os
import sys
import subprocess
import time
import webview  # type: ignore
import traceback
import shutil
from data_storage import DataStorage

# Constants
APP_NAME = "Financial Advisor"
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
DEBUG_MODE = True

def build_app():
    """Build the React app"""
    print("Building React app...")

    # Set environment variables for the build
    os.environ["VITE_USE_LOCAL_STORE"] = "true"
    os.environ["REACT_APP_USE_LOCAL_STORE"] = "true"
    os.environ["USE_LOCAL_STORE"] = "true"
    os.environ["VITE_API_URL"] = "/"
    os.environ["VITE_BASE_URL"] = "/"
    os.environ["VITE_PUBLIC_URL"] = "/"
    os.environ["PUBLIC_URL"] = "/"

    # Copy the storage-bridge.js file to the src/utils directory
    src_dir = os.path.dirname(os.path.abspath(__file__))
    utils_dir = os.path.join(src_dir, "src", "utils")

    # Create the utils directory if it doesn't exist
    os.makedirs(utils_dir, exist_ok=True)

    # Copy the storage-bridge.js file
    storage_bridge_src = os.path.join(src_dir, "storage-bridge.js")
    storage_bridge_dst = os.path.join(utils_dir, "storage-bridge.js")

    if os.path.exists(storage_bridge_src):
        shutil.copy2(storage_bridge_src, storage_bridge_dst)
        print(f"Copied storage-bridge.js to {storage_bridge_dst}")
    else:
        print(f"Warning: storage-bridge.js not found at {storage_bridge_src}")

    # Run the build command
    try:
        subprocess.run(
            "npm run build",
            shell=True,
            check=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        print("Build completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Build failed: {e}")
        return False

def on_loaded(window):
    """Called when the webpage has loaded"""
    print("WebView loaded - page is ready")

    # First, ensure the PyWebView API is properly exposed to the frontend
    try:
        if hasattr(window, 'evaluate_js'):
            # This is critical - we need to make sure the API is properly exposed
            window.evaluate_js("""
                // Create a proper API bridge
                if (!window.pywebview) {
                    window.pywebview = {};
                }

                if (!window.pywebview.api) {
                    window.pywebview.api = {};
                }

                // CRITICAL: Create a pywebview_channel if it doesn't exist
                // This is the key to making the app work
                window.pywebview_channel = {
                    call: function(method, ...args) {
                        console.log('Calling method via pywebview_channel:', method, args);
                        if (window.pywebview && window.pywebview.api && typeof window.pywebview.api[method] === 'function') {
                            try {
                                const result = window.pywebview.api[method](...args);
                                console.log('Result from ' + method + ':', result);
                                return result;
                            } catch (error) {
                                console.error('Error calling ' + method + ':', error);
                                return null;
                            }
                        } else {
                            console.error('Method not available:', method);
                            return null;
                        }
                    }
                };
                console.log('Created pywebview_channel wrapper - FORCED MODE');

                // Define the API methods
                window.pywebview.api.get_item = function(key) {
                    console.log('Calling get_item with key:', key);
                    try {
                        if (window.pywebview_channel) {
                            return window.pywebview_channel.call('get_item', key);
                        } else {
                            console.error('pywebview_channel not available');
                            return null;
                        }
                    } catch (error) {
                        console.error('Error in get_item:', error);
                        return null;
                    }
                };

                window.pywebview.api.set_item = function(key, value) {
                    console.log('Calling set_item with key:', key);
                    try {
                        if (window.pywebview_channel) {
                            return window.pywebview_channel.call('set_item', key, value);
                        } else {
                            console.error('pywebview_channel not available');
                            return false;
                        }
                    } catch (error) {
                        console.error('Error in set_item:', error);
                        return false;
                    }
                };

                window.pywebview.api.remove_item = function(key) {
                    console.log('Calling remove_item with key:', key);
                    try {
                        if (window.pywebview_channel) {
                            return window.pywebview_channel.call('remove_item', key);
                        } else {
                            console.error('pywebview_channel not available');
                            return false;
                        }
                    } catch (error) {
                        console.error('Error in remove_item:', error);
                        return false;
                    }
                };

                window.pywebview.api.get_all_keys = function() {
                    console.log('Calling get_all_keys');
                    try {
                        if (window.pywebview_channel) {
                            return window.pywebview_channel.call('get_all_keys');
                        } else {
                            console.error('pywebview_channel not available');
                            return '[]';
                        }
                    } catch (error) {
                        console.error('Error in get_all_keys:', error);
                        return '[]';
                    }
                };

                window.pywebview.api.dump_storage = function() {
                    console.log('Calling dump_storage');
                    try {
                        if (window.pywebview_channel) {
                            return window.pywebview_channel.call('dump_storage');
                        } else {
                            console.error('pywebview_channel not available');
                            return '{}';
                        }
                    } catch (error) {
                        console.error('Error in dump_storage:', error);
                        return '{}';
                    }
                };

                window.pywebview.api.clean_indexes = function() {
                    console.log('Calling clean_indexes');
                    try {
                        if (window.pywebview_channel) {
                            return window.pywebview_channel.call('clean_indexes');
                        } else {
                            console.error('pywebview_channel not available');
                            return false;
                        }
                    } catch (error) {
                        console.error('Error in clean_indexes:', error);
                        return false;
                    }
                };

                window.pywebview.api.repair_storage = function() {
                    console.log('Calling repair_storage');
                    try {
                        if (window.pywebview_channel) {
                            return window.pywebview_channel.call('repair_storage');
                        } else {
                            console.error('pywebview_channel not available');
                            return false;
                        }
                    } catch (error) {
                        console.error('Error in repair_storage:', error);
                        return false;
                    }
                };

                window.pywebview.api.get_storage_stats = function() {
                    console.log('Calling get_storage_stats');
                    try {
                        if (window.pywebview_channel) {
                            return window.pywebview_channel.call('get_storage_stats');
                        } else {
                            console.error('pywebview_channel not available');
                            return '{"items":0,"size":0}';
                        }
                    } catch (error) {
                        console.error('Error in get_storage_stats:', error);
                        return '{"items":0,"size":0}';
                    }
                };

                window.pywebview.api.get_collection = function(collection) {
                    console.log('Calling get_collection with collection:', collection);
                    try {
                        if (window.pywebview_channel) {
                            return window.pywebview_channel.call('get_collection', collection);
                        } else {
                            console.error('pywebview_channel not available');
                            return '[]';
                        }
                    } catch (error) {
                        console.error('Error in get_collection:', error);
                        return '[]';
                    }
                };



                // Set the environment variable properly
                window.USE_LOCAL_STORE = true;

                // Set a flag to indicate that PyWebView is ready
                window.pywebview_ready = true;

                // Dispatch a custom event to notify the app that PyWebView is ready
                window.dispatchEvent(new Event('pywebview-api-ready'));
                window.dispatchEvent(new Event('pywebview-ready'));

                // Force the pywebview_channel to be available
                if (!window.pywebview_channel) {
                    window.pywebview_channel = {
                        call: function(method, ...args) {
                            console.log('Calling method via forced channel:', method, args);
                            if (window.pywebview && window.pywebview.api && typeof window.pywebview.api[method] === 'function') {
                                try {
                                    const result = window.pywebview.api[method](...args);
                                    console.log('Result from ' + method + ':', result);
                                    return result;
                                } catch (error) {
                                    console.error('Error calling ' + method + ':', error);
                                    return null;
                                }
                            } else {
                                console.error('Method not available:', method);
                                return null;
                            }
                        }
                    };
                }

                console.log('PyWebView API bridge created and configured');
                console.log('USE_LOCAL_STORE set to:', window.USE_LOCAL_STORE);
                console.log('pywebview_channel available:', !!window.pywebview_channel);
                console.log('pywebview_ready set to:', window.pywebview_ready);
                console.log('API methods:', Object.keys(window.pywebview.api));
            """)
            print("PyWebView API bridge created")

            # Now run the storage maintenance
            window.evaluate_js("""
                console.log('Running storage maintenance...');

                // Helper function to safely call API methods
                function safeApiCall(methodName, ...args) {
                    return new Promise((resolve, reject) => {
                        try {
                            if (window.pywebview && window.pywebview.api && typeof window.pywebview.api[methodName] === 'function') {
                                resolve(window.pywebview.api[methodName](...args));
                            } else {
                                console.error(`API method ${methodName} not available`);
                                reject(new Error(`API method ${methodName} not available`));
                            }
                        } catch (error) {
                            console.error(`Error calling ${methodName}:`, error);
                            reject(error);
                        }
                    });
                }

                // Run clean_indexes
                safeApiCall('clean_indexes')
                    .then(result => {
                        console.log('Storage index cleanup result:', result);
                    })
                    .catch(err => {
                        console.error('Error cleaning indexes:', err);
                    });

                // Run repair_storage
                safeApiCall('repair_storage')
                    .then(result => {
                        console.log('Storage repair result:', result);
                    })
                    .catch(err => {
                        console.error('Error repairing storage:', err);
                    });

                // Test the storage API
                safeApiCall('set_item', 'test-key', JSON.stringify({test: 'data', timestamp: Date.now()}))
                    .then(result => {
                        console.log('Test set_item result:', result);
                        return safeApiCall('get_item', 'test-key');
                    })
                    .then(result => {
                        console.log('Test get_item result:', result);
                    })
                    .catch(err => {
                        console.error('Error testing storage API:', err);
                    });
            """)
            print("Storage maintenance scheduled")
        else:
            print("Window does not support evaluate_js, skipping API setup")
    except Exception as e:
        print(f"Error setting up PyWebView API: {e}")
        traceback.print_exc()

def launch_app():
    """Launch the desktop application with the built React app"""
    print(f"Starting {APP_NAME}...")
    print(f"Storage location: {os.path.join(os.path.expanduser('~'), '.financial_advisor')}")

    try:
        # Initialize the data storage
        storage = DataStorage()

        # Set environment variables
        os.environ["VITE_USE_LOCAL_STORE"] = "true"
        os.environ["REACT_APP_USE_LOCAL_STORE"] = "true"
        os.environ["USE_LOCAL_STORE"] = "true"
        os.environ["VITE_API_URL"] = "/"
        os.environ["VITE_BASE_URL"] = "/"
        os.environ["VITE_PUBLIC_URL"] = "/"
        os.environ["PUBLIC_URL"] = "/"

        print("Environment variables set:")
        print(f"VITE_USE_LOCAL_STORE = {os.environ.get('VITE_USE_LOCAL_STORE')}")
        print(f"REACT_APP_USE_LOCAL_STORE = {os.environ.get('REACT_APP_USE_LOCAL_STORE')}")
        print(f"USE_LOCAL_STORE = {os.environ.get('USE_LOCAL_STORE')}")

        # Get the path to the built app
        dist_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'dist')
        index_path = os.path.join(dist_path, 'index.html')

        if not os.path.exists(index_path):
            print(f"Error: Built app not found at {index_path}")
            print("Please run 'npm run build' first")
            return

        # Create the URL for the local file
        url = f"file://{index_path}"
        print(f"Loading app from: {url}")

        # Create the window with storage API exposed
        window = webview.create_window(
            title=APP_NAME,
            url=url,
            width=WINDOW_WIDTH,
            height=WINDOW_HEIGHT,
            min_size=(800, 600),
            js_api=storage,
            text_select=True
        )

        # Register the loaded event handler
        window.events.loaded += on_loaded

        # Start the window
        webview.start(debug=DEBUG_MODE)

    except Exception as e:
        print(f"Error in desktop app: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    # Build the app first
    if build_app():
        # Then launch it
        launch_app()
    else:
        print("Failed to build the app. Exiting.")
        sys.exit(1)
