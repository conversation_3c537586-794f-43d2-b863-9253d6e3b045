import webview  # type: ignore
import os
import sys
import subprocess
import time
import signal
import socket
import traceback
from http.client import HTTPConnection
from data_storage import DataStorage

"""
Financial Advisor with Simplified Storage
----------------------------------------
This script runs the Financial Advisor app with a simpler, direct file storage system.
"""

# Configuration
APP_NAME = "Financial Advisor (Simplified Storage)"
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
DEFAULT_PORT = 5173
HOST = "localhost"
MAX_STARTUP_TIME = 60
DEBUG_MODE = False

# Global variables
server_process = None

def is_port_in_use(port):
    """Check if a port is already in use"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex((HOST, port)) == 0

def find_available_port():
    """Find an available port starting from the default port"""
    port = DEFAULT_PORT
    while is_port_in_use(port):
        port += 1
        if port > DEFAULT_PORT + 100:  # Avoid endless loop
            raise Exception(f"No available ports found in range {DEFAULT_PORT}-{DEFAULT_PORT+100}")
    return port

def wait_for_server(host, port, timeout=MAX_STARTUP_TIME):
    """Wait for the development server to become available"""
    print(f"Waiting for server to start on {host}:{port}...")
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            conn = HTTPConnection(host, port, timeout=1)
            conn.request("HEAD", "/")
            response = conn.getresponse()
            print(f"Server started successfully on {host}:{port}")
            return True
        except Exception as e:
            if DEBUG_MODE:
                print(f"Still waiting for server: {e}")
            time.sleep(0.5)
    return False

def run_dev_server():
    """Run the web development server in a separate process"""
    global server_process

    try:
        # Find an available port
        port = find_available_port()
        print(f"Using port: {port}")

        # Set environment variables to use local auth store
        env = os.environ.copy()
        env["PORT"] = str(port)
        env["VITE_USE_LOCAL_STORE"] = "true"
        env["VITE_BASE_URL"] = "/"  # Ensure base URL is set correctly

        # Make sure the environment variables are passed to the child process
        os.environ["VITE_USE_LOCAL_STORE"] = "true"
        os.environ["VITE_BASE_URL"] = "/"

        # Prepare the command - use npx to ensure we're using the local vite installation
        command = f"npx vite --port={port} --host={HOST}"

        # Start the dev server
        if sys.platform == 'win32':
            print(f"Starting dev server with command: {command}")
            server_process = subprocess.Popen(
                command,
                cwd=os.path.dirname(os.path.abspath(__file__)),
                env=env,
                shell=True,
                stdout=subprocess.PIPE if not DEBUG_MODE else None,
                stderr=subprocess.PIPE if not DEBUG_MODE else None,
                text=True
            )
        else:
            server_process = subprocess.Popen(
                command.split(),
                cwd=os.path.dirname(os.path.abspath(__file__)),
                env=env,
                stdout=subprocess.PIPE if not DEBUG_MODE else None,
                stderr=subprocess.PIPE if not DEBUG_MODE else None,
                text=True
            )

        # Wait for server to start
        if wait_for_server(HOST, port):
            return (f"http://{HOST}:{port}", port)
        else:
            raise Exception(f"Development server failed to start within {MAX_STARTUP_TIME} seconds")

    except Exception as e:
        print(f"Error starting development server: {e}")
        sys.exit(1)

def cleanup():
    """Clean up resources when app exits"""
    global server_process

    if server_process:
        print("Shutting down development server...")
        try:
            if sys.platform == 'win32':
                subprocess.call(['taskkill', '/F', '/T', '/PID', str(server_process.pid)])
            else:
                os.killpg(os.getpgid(server_process.pid), signal.SIGTERM)
            print("Server process terminated")
        except Exception as e:
            print(f"Error shutting down server: {e}")

def on_loaded(window):
    """Called when the webpage has loaded"""
    print("WebView loaded - page is ready")
    # The PyWebView API should be available directly via window.pywebview.api
    # No need for custom bridge creation or complex debug scripts here.
    # The frontend StorageBridge and localDataStore handle API interaction and events.

def launch_app():
    """Launch the desktop application with simplified storage"""
    print(f"Starting {APP_NAME}...")
    print(f"Storage location: {os.path.join(os.path.expanduser('~'), '.financial_advisor')}")

    try:
        # Initialize the data storage
        storage = DataStorage()

        # Set ALL possible environment variables that might be used
        os.environ["VITE_USE_LOCAL_STORE"] = "true"
        os.environ["REACT_APP_USE_LOCAL_STORE"] = "true"
        os.environ["USE_LOCAL_STORE"] = "true"
        os.environ["VITE_API_URL"] = "/"
        os.environ["VITE_BASE_URL"] = "/"
        os.environ["VITE_PUBLIC_URL"] = "/"
        os.environ["PUBLIC_URL"] = "/"

        print("Environment variables set:")
        print(f"VITE_USE_LOCAL_STORE = {os.environ.get('VITE_USE_LOCAL_STORE')}")
        print(f"REACT_APP_USE_LOCAL_STORE = {os.environ.get('REACT_APP_USE_LOCAL_STORE')}")
        print(f"USE_LOCAL_STORE = {os.environ.get('USE_LOCAL_STORE')}")

        # Start the dev server with the environment variables
        url, port = run_dev_server()

        # Create the window with storage API exposed
        # Use the actual port found by run_dev_server
        correct_url = f"http://{HOST}:{port}"
        print(f"Creating window with URL: {correct_url}")
        window = webview.create_window(
            title=APP_NAME,
            url=correct_url,
            width=WINDOW_WIDTH,
            height=WINDOW_HEIGHT,
            min_size=(800, 600),
            js_api=storage,
            text_select=True
        )

        # Register the loaded event handler
        window.events.loaded += on_loaded

        # Start the window
        webview.start(debug=DEBUG_MODE)

    except Exception as e:
        print(f"Error in desktop app: {e}")
        traceback.print_exc()  # Print the full stack trace

    finally:
        # Always cleanup when done
        cleanup()

if __name__ == "__main__":
    launch_app()
