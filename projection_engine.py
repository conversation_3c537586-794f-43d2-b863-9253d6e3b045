# projection_engine.py
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import datetime, date 
from dateutil.relativedelta import relativedelta
import math

# --- Dataclass Definitions ---
@dataclass
class Loan:
    loan_id: str
    loan_name: str
    original_principal: float
    current_principal_balance: float
    interest_rate: float # Annual, e.g., 5.75 for 5.75%
    loan_term_months: int 
    monthly_payment: float
    loan_start_date: str # YYYY-MM-DD string
    next_payment_date: str # YYYY-MM-DD string
    # Optional fields
    remaining_term_months: Optional[int] = None 
    lender_name: Optional[str] = None
    loan_type: Optional[str] = None

@dataclass
class CD:
    cd_id: str
    cd_name: str
    principal_amount: float
    current_value: float
    interest_rate: float
    term_months: int
    start_date: str # YYYY-MM-DD
    maturity_date: str # YYYY-MM-DD
    interest_payout_frequency: str 
    interest_treatment: str # "payout", "reinvest"
    last_value_update_date: str # YYYY-MM-DD
    # Optional fields
    payout_account_id: Optional[str] = None
    institution_name: Optional[str] = None

@dataclass
class Fund:
    fund_id: str
    fund_name: str
    expected_monthly_income: float
    last_income_update_date: Optional[str] = None

@dataclass
class ExpenseCategory:
    expense_category_id: str
    category_name: str
    budgeted_monthly_amount: float
    is_essential: Optional[bool] = False

@dataclass
class PlannerSettings:
    available_savings_buffer: float
    projection_period_months: int

@dataclass
class PlannerInputData:
    loans: List[Loan]
    cds: List[CD]
    funds: List[Fund]
    expense_categories: List[ExpenseCategory]
    settings: PlannerSettings

# --- Helper Functions ---
def _parse_date(date_str: str) -> Optional[date]:
    if not date_str: return None
    try: return datetime.strptime(date_str, "%Y-%m-%d").date()
    except ValueError: print(f"Warning: Could not parse date string: {date_str}"); return None

def _calculate_amortization_step(current_balance: float, annual_rate: float, standard_monthly_payment: float) -> Dict[str, float]:
    if current_balance <= 0: return {"interest_paid": 0.0, "principal_paid": 0.0, "new_balance": 0.0, "actual_payment_made": 0.0}
    monthly_interest_rate = (annual_rate / 100) / 12
    interest_for_month = round(current_balance * monthly_interest_rate, 2)
    principal_paid_from_standard_payment = round(standard_monthly_payment - interest_for_month, 2)
    if principal_paid_from_standard_payment < 0: principal_paid_from_standard_payment = 0.0
    if current_balance + interest_for_month <= standard_monthly_payment:
        actual_payment_made = round(current_balance + interest_for_month, 2)
        principal_paid = current_balance
        interest_paid = round(actual_payment_made - principal_paid, 2)
        new_balance = 0.0
    else:
        actual_payment_made = standard_monthly_payment
        principal_paid = principal_paid_from_standard_payment
        interest_paid = interest_for_month
        new_balance = round(current_balance - principal_paid, 2)
    if new_balance < 0: new_balance = 0.0
    return {"interest_paid": interest_paid, "principal_paid": principal_paid, "new_balance": new_balance, "actual_payment_made": actual_payment_made}

def _calculate_cd_monthly_income(
    cd_item: CD, 
    month_in_cd_lifecycle: int
) -> Dict[str, Any]:
    cash_flow_from_cd = 0.0
    new_cd_value = cd_item.current_value 
    interest_accrued_this_month = 0.0
    is_maturity_month = (month_in_cd_lifecycle == cd_item.term_months)
    base_for_interest_calc = cd_item.principal_amount
    if cd_item.interest_treatment == "reinvest" and cd_item.interest_payout_frequency != "at_maturity":
        base_for_interest_calc = cd_item.current_value
    monthly_simple_interest = round((base_for_interest_calc * (cd_item.interest_rate / 100)) / 12, 2)
    interest_accrued_this_month = monthly_simple_interest
    is_regular_event_month = False
    num_months_in_period = 1
    if cd_item.interest_payout_frequency == "monthly": is_regular_event_month = True
    elif cd_item.interest_payout_frequency == "quarterly" and month_in_cd_lifecycle > 0 and month_in_cd_lifecycle % 3 == 0: is_regular_event_month = True; num_months_in_period = 3
    elif cd_item.interest_payout_frequency == "semi-annually" and month_in_cd_lifecycle > 0 and month_in_cd_lifecycle % 6 == 0: is_regular_event_month = True; num_months_in_period = 6
    elif cd_item.interest_payout_frequency == "annually" and month_in_cd_lifecycle > 0 and month_in_cd_lifecycle % 12 == 0: is_regular_event_month = True; num_months_in_period = 12
    period_interest = round(monthly_simple_interest * num_months_in_period, 2) if is_regular_event_month else 0.0
    if is_regular_event_month and not is_maturity_month:
        if cd_item.interest_treatment == "payout": cash_flow_from_cd = period_interest
        elif cd_item.interest_treatment == "reinvest": new_cd_value = round(new_cd_value + period_interest, 2)
    if is_maturity_month:
        final_interest_to_add = 0.0
        if cd_item.interest_payout_frequency == "at_maturity":
            total_term_interest = round(cd_item.principal_amount * (cd_item.interest_rate / 100) * (cd_item.term_months / 12), 2)
            final_interest_to_add = total_term_interest
        elif not is_regular_event_month: final_interest_to_add = interest_accrued_this_month
        if cd_item.interest_treatment == "reinvest":
            if cd_item.interest_payout_frequency == "at_maturity": new_cd_value = cd_item.principal_amount + final_interest_to_add
            elif not is_regular_event_month: new_cd_value += final_interest_to_add
            cash_flow_from_cd = round(new_cd_value, 2); new_cd_value = 0.0
        elif cd_item.interest_treatment == "payout":
            cash_flow_from_cd += final_interest_to_add; cash_flow_from_cd += cd_item.principal_amount; new_cd_value = 0.0
    return {"cash_flow": round(cash_flow_from_cd, 2), "new_value": round(new_cd_value, 2), "interest_accrued_this_month": round(interest_accrued_this_month, 2)}

# --- Main Projection Function ---
def run_projection(data: PlannerInputData, 
                   projection_start_date_override: Optional[date] = None
                   ) -> Dict[str, Any]:
    print(f"Projection Engine: Starting projection for {data.settings.projection_period_months} months.")
    results = {"loan_coverage_months": 0.0, "all_loans_paid_off_month": None, "monthly_series": [], 
               "loan_balances_over_time": {loan.loan_id: [] for loan in data.loans}, 
               "cd_values_over_time": {cd.cd_id: [] for cd in data.cds}}
    current_savings_buffer = data.settings.available_savings_buffer
    active_loans_sim = [Loan(**loan.__dict__) for loan in data.loans]
    sim_cds_data = [CD(**cd.__dict__) for cd in data.cds]
    projection_current_actual_date = projection_start_date_override.replace(day=1) if projection_start_date_override else date.today().replace(day=1)
    print(f"Projection starting from (1st of month): {projection_current_actual_date.strftime('%Y-%m-%d')}")
    all_loans_paid_off_flag = False
    initial_date_approx_str = (projection_current_actual_date - relativedelta(months=1)).strftime("%Y-%m-%d")
    for loan_obj_init in active_loans_sim: results["loan_balances_over_time"][loan_obj_init.loan_id].append({"month": 0, "balance": loan_obj_init.current_principal_balance, "date_approx": initial_date_approx_str})
    for cd_obj_init in sim_cds_data: results["cd_values_over_time"][cd_obj_init.cd_id].append({"month": 0, "value": cd_obj_init.current_value, "cash_flow_generated_this_month": 0.0, "interest_accrued_this_month": 0.0, "date_approx": initial_date_approx_str})
    results["monthly_series"].append({"month": 0, "date_approx": initial_date_approx_str, "total_income": 0.0, "total_expenses": 0.0, "total_loan_payments": 0.0, "net_cash_flow": 0.0, "savings_buffer_start_of_month": current_savings_buffer, "savings_buffer_end_of_month": current_savings_buffer})

    for month_num in range(1, data.settings.projection_period_months + 1):
        current_month_date_str = projection_current_actual_date.strftime("%Y-%m-%d")
        monthly_total_cd_cash_flow = 0.0
        for cd_obj in sim_cds_data:
            if cd_obj.current_value <= 0:
                cd_log_exists = any(entry["month"] == month_num and entry["date_approx"] == current_month_date_str for entry in results["cd_values_over_time"].get(cd_obj.cd_id, []))
                if not cd_log_exists: results["cd_values_over_time"][cd_obj.cd_id].append({"month": month_num, "value": 0.0, "cash_flow_generated_this_month": 0.0, "interest_accrued_this_month": 0.0, "date_approx": current_month_date_str})
                continue
            cd_start_actual_date = _parse_date(cd_obj.start_date); cd_maturity_actual_date = _parse_date(cd_obj.maturity_date)
            if not cd_start_actual_date or not cd_maturity_actual_date:
                print(f"Warning: CD {cd_obj.cd_name} ({cd_obj.cd_id}) has invalid start/maturity dates. Skipping.")
                results["cd_values_over_time"][cd_obj.cd_id].append({"month": month_num, "value": cd_obj.current_value, "cash_flow_generated_this_month": 0.0, "interest_accrued_this_month": 0.0, "date_approx": current_month_date_str}); continue
            current_sim_month_start = projection_current_actual_date.replace(day=1); cd_start_month_start_for_cd_calc = cd_start_actual_date.replace(day=1)
            if current_sim_month_start < cd_start_month_start_for_cd_calc:
                 results["cd_values_over_time"][cd_obj.cd_id].append({"month": month_num, "value": cd_obj.current_value, "cash_flow_generated_this_month": 0.0, "interest_accrued_this_month": 0.0, "date_approx": current_month_date_str}); continue
            if current_sim_month_start > cd_maturity_actual_date.replace(day=1):
                 if cd_obj.current_value > 0: print(f"Info: CD {cd_obj.cd_name} ({cd_obj.cd_id}) is past maturity month. Value was {cd_obj.current_value}. Forcing to 0.")
                 cd_obj.current_value = 0.0; results["cd_values_over_time"][cd_obj.cd_id].append({"month": month_num, "value": 0.0, "cash_flow_generated_this_month": 0.0, "interest_accrued_this_month": 0.0, "date_approx": current_month_date_str}); continue
            delta = relativedelta(current_sim_month_start, cd_start_month_start_for_cd_calc); month_in_cd_lifecycle = delta.years * 12 + delta.months + 1
            if month_in_cd_lifecycle <= 0 or month_in_cd_lifecycle > cd_obj.term_months:
                 results["cd_values_over_time"][cd_obj.cd_id].append({"month": month_num, "value": cd_obj.current_value, "cash_flow_generated_this_month": 0.0, "interest_accrued_this_month": 0.0, "date_approx": current_month_date_str}); continue
            cd_income_info = _calculate_cd_monthly_income(cd_obj, month_in_cd_lifecycle)
            monthly_total_cd_cash_flow += cd_income_info["cash_flow"]; cd_obj.current_value = cd_income_info["new_value"]
            results["cd_values_over_time"][cd_obj.cd_id].append({"month": month_num, "value": cd_obj.current_value, "cash_flow_generated_this_month": cd_income_info["cash_flow"], "interest_accrued_this_month": cd_income_info["interest_accrued_this_month"], "date_approx": current_month_date_str})
        monthly_total_fund_income = sum(fund.expected_monthly_income for fund in data.funds); total_monthly_income = monthly_total_cd_cash_flow + monthly_total_fund_income
        total_monthly_expenses = sum(cat.budgeted_monthly_amount for cat in data.expense_categories); total_loan_payments_this_month = 0.0
        active_loans_this_month_count = sum(1 for loan in active_loans_sim if loan.current_principal_balance > 0)
        if active_loans_this_month_count == 0 and not all_loans_paid_off_flag:
             all_loans_paid_off_flag = True; results["all_loans_paid_off_month"] = max(0, month_num - 1) if not results["all_loans_paid_off_month"] else results["all_loans_paid_off_month"]
        for loan_obj in active_loans_sim:
            if loan_obj.current_principal_balance <= 0: results["loan_balances_over_time"][loan_obj.loan_id].append({"month": month_num, "balance": 0.0, "date_approx": current_month_date_str}); continue
            loan_next_payment_actual_date = _parse_date(loan_obj.next_payment_date); is_payment_due_this_sim_month = False
            if loan_next_payment_actual_date and loan_next_payment_actual_date.year == projection_current_actual_date.year and loan_next_payment_actual_date.month == projection_current_actual_date.month: is_payment_due_this_sim_month = True
            if is_payment_due_this_sim_month:
                payment_info = _calculate_amortization_step(loan_obj.current_principal_balance, loan_obj.interest_rate, loan_obj.monthly_payment)
                total_loan_payments_this_month += payment_info["actual_payment_made"]; loan_obj.current_principal_balance = payment_info["new_balance"]
                if loan_obj.current_principal_balance > 0 and loan_next_payment_actual_date: loan_obj.next_payment_date = (loan_next_payment_actual_date + relativedelta(months=1)).strftime("%Y-%m-%d")
            results["loan_balances_over_time"][loan_obj.loan_id].append({"month": month_num, "balance": round(loan_obj.current_principal_balance, 2), "date_approx": current_month_date_str})
        current_total_outstanding_loan_balance = sum(l.current_principal_balance for l in active_loans_sim if l.current_principal_balance > 0)
        if current_total_outstanding_loan_balance <= 0 and not all_loans_paid_off_flag:
            all_loans_paid_off_flag = True; results["all_loans_paid_off_month"] = month_num if not results["all_loans_paid_off_month"] else results["all_loans_paid_off_month"]
        net_cash_flow = total_monthly_income - total_monthly_expenses - total_loan_payments_this_month
        previous_buffer = current_savings_buffer; current_savings_buffer += net_cash_flow
        results["monthly_series"].append({"month": month_num, "date_approx": current_month_date_str, "total_income": round(total_monthly_income, 2), "total_expenses": round(total_monthly_expenses, 2), "total_loan_payments": round(total_loan_payments_this_month, 2), "net_cash_flow": round(net_cash_flow, 2), "savings_buffer_start_of_month": round(previous_buffer,2), "savings_buffer_end_of_month": round(current_savings_buffer, 2)})
        if not all_loans_paid_off_flag:
            if current_savings_buffer >= 0: results["loan_coverage_months"] = float(month_num)
            else: 
                deficit_for_month_fixed_costs = (total_monthly_expenses + total_loan_payments_this_month) - total_monthly_income
                results["loan_coverage_months"] = (month_num - 1) + (previous_buffer / deficit_for_month_fixed_costs if previous_buffer > 0 and deficit_for_month_fixed_costs > 0 else 0.0)
                results["loan_coverage_months"] = min(float(month_num), max(0.0, results["loan_coverage_months"])) 
                break 
        elif all_loans_paid_off_flag: results["loan_coverage_months"] = float(month_num)
        projection_current_actual_date += relativedelta(months=1)
    if month_num == data.settings.projection_period_months:
        if all_loans_paid_off_flag: results["loan_coverage_months"] = float(results["all_loans_paid_off_month"] if results["all_loans_paid_off_month"] is not None else data.settings.projection_period_months)
        elif current_savings_buffer >=0: results["loan_coverage_months"] = float(data.settings.projection_period_months)
    if results["all_loans_paid_off_month"] is None and results["loan_coverage_months"] > 0: results["loan_coverage_months"] = min(results["loan_coverage_months"], float(data.settings.projection_period_months))
    elif results["all_loans_paid_off_month"] is not None: results["loan_coverage_months"] = min(max(results["loan_coverage_months"], float(results["all_loans_paid_off_month"])), float(data.settings.projection_period_months))
    print(f"Projection Engine: Completed. Coverage: {results['loan_coverage_months']:.2f} months. All loans paid off month: {results['all_loans_paid_off_month']}")
    return results