// CommonJS module for Electron main process
const { app, BrowserWindow, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const url = require('url');
const { spawn } = require('child_process');

// Keep a global reference of key objects
let mainWindow;
let devServer;

// <PERSON>le creating/removing shortcuts on Windows when installing/uninstalling
if (require('electron-squirrel-startup')) {
  app.quit();
}

// Check if a file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (err) {
    return false;
  }
}

// Start the development server
function startDevServer() {
  return new Promise((resolve, reject) => {
    // Start the Vite dev server
    devServer = spawn('npm', ['run', 'dev'], {
      cwd: process.cwd(),
      shell: true,
      stdio: 'pipe'
    });
    
    let serverStarted = false;
    // Need to use stdout to determine when the server is ready
    devServer.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('Dev server:', output);
      
      // When Vite prints the "ready" message with the local URL
      if (output.includes('Local:') && !serverStarted) {
        serverStarted = true;
        // Extract the URL from the output
        const urlMatch = output.match(/Local:\s+(http:\/\/localhost:\d+)/);
        const serverUrl = urlMatch ? urlMatch[1] : 'http://localhost:5173';
        resolve(serverUrl);
      }
    });
    
    devServer.stderr.on('data', (data) => {
      console.error('Dev server error:', data.toString());
    });
    
    // If the server doesn't start within 30 seconds, reject
    setTimeout(() => {
      if (!serverStarted) {
        reject(new Error('Dev server failed to start in time'));
      }
    }, 30000);
  });
}

async function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1280,
    height: 800,
    title: 'Advanced Trading Advisor',
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      sandbox: true,
      preload: path.join(__dirname, 'preload.js')
    },
    show: false // Don't show window until content is ready
  });
  
  // Remove the default menu bar
  mainWindow.setMenu(null);
  
  try {
    let appUrl;
    
    // In production: use the bundled app
    if (app.isPackaged) {
      const indexPath = path.join(__dirname, 'dist', 'index.html');
      
      if (!fileExists(indexPath)) {
        throw new Error(`Could not find the built application at ${indexPath}`);
      }
      
      appUrl = url.format({
        pathname: indexPath,
        protocol: 'file:',
        slashes: true
      });
    } 
    // In development: start dev server and connect to it
    else {
      try {
        appUrl = await startDevServer();
      } catch (err) {
        throw new Error(`Failed to start development server: ${err.message}`);
      }
    }
    
    // Load the app
    await mainWindow.loadURL(appUrl);
    
    // Show window after content is loaded
    mainWindow.show();
    
  } catch (error) {
    console.error('Error loading application:', error);
    dialog.showErrorBox(
      'Application Error',
      `Failed to load the application: ${error.message}\n\nPlease contact support.`
    );
    app.quit();
  }
  
  // Open DevTools only in development and when explicitly enabled
  if (!app.isPackaged && process.env.OPEN_DEVTOOLS === 'true') {
    mainWindow.webContents.openDevTools();
  }
  
  // Handle window being closed
  mainWindow.on('closed', () => {
    mainWindow = null;
    
    // Kill the dev server when the window is closed
    if (devServer) {
      devServer.kill();
      devServer = null;
    }
  });
}

// This method will be called when Electron has finished initialization
app.on('ready', createWindow);

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS apps commonly stay open until explicitly quit
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On macOS recreate the window when dock icon is clicked
  if (mainWindow === null) {
    createWindow();
  }
});

// Cleanup when app is quitting
app.on('quit', () => {
  if (devServer) {
    devServer.kill();
    devServer = null;
  }
});