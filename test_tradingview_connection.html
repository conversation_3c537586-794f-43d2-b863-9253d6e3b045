<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
    </style>
</head>
<body>
    <h1>🚀 TradingView Connection Test</h1>
    
    <div id="status"></div>
    
    <button class="btn-primary" onclick="testConnection()">Test Server Connection</button>
    <button class="btn-success" onclick="fetchStocks()">Fetch Stock Data</button>
    
    <div id="results"></div>

    <script>
        const serverUrl = 'http://localhost:3000';
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function updateResults(content) {
            document.getElementById('results').innerHTML = content;
        }
        
        async function testConnection() {
            updateStatus('🔍 Testing TradingView server connection...', 'info');
            
            try {
                const response = await fetch(`${serverUrl}/api/stocks`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    updateStatus('✅ TradingView server is connected and responding!', 'success');
                    updateResults('<p><strong>Connection successful!</strong> Server is running at ' + serverUrl + '</p>');
                } else {
                    updateStatus(`❌ Server responded with error: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                updateStatus(`❌ Connection failed: ${error.message}`, 'error');
                updateResults('<p><strong>Connection failed!</strong><br>Make sure the TradingView server is running:<br><code>cd TradingView-API/web-app && node server.js</code></p>');
            }
        }
        
        async function fetchStocks() {
            updateStatus('📊 Fetching stock data...', 'info');
            
            try {
                const response = await fetch(`${serverUrl}/api/stocks`);
                
                if (response.ok) {
                    const stocks = await response.json();
                    updateStatus(`✅ Successfully fetched ${stocks.length} stocks!`, 'success');
                    
                    let resultsHtml = '<h3>📈 Live Stock Data:</h3>';
                    stocks.forEach(stock => {
                        const changeColor = stock.change >= 0 ? '#28a745' : '#dc3545';
                        const changeSign = stock.change >= 0 ? '+' : '';
                        
                        resultsHtml += `
                            <div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;">
                                <strong>${stock.name} (${stock.symbol})</strong><br>
                                Price: <strong>EGP ${stock.price.toFixed(2)}</strong><br>
                                Change: <span style="color: ${changeColor};">${changeSign}${stock.change.toFixed(2)} (${stock.changePercent.toFixed(2)}%)</span><br>
                                Volume: ${stock.volume.toLocaleString()}<br>
                                Type: ${stock.type}<br>
                                Last Updated: ${new Date(stock.timestamp).toLocaleString()}
                            </div>
                        `;
                    });
                    
                    updateResults(resultsHtml);
                } else {
                    updateStatus(`❌ Failed to fetch stocks: ${response.status}`, 'error');
                }
            } catch (error) {
                updateStatus(`❌ Error fetching stocks: ${error.message}`, 'error');
            }
        }
        
        // Auto-test on page load
        window.onload = () => {
            testConnection();
        };
    </script>
</body>
</html>
