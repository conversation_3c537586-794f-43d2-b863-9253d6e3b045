#!/usr/bin/env python3

"""
Fix Assets Data Integrity Issue
===============================
The audit revealed Assets ($4,868,514) < Liquid Assets ($11,049,000)
This means the Assets table is missing major holdings like CDs.
This script will analyze and fix the discrepancy.
"""

import sys
sys.path.append('.')
from data_storage import DataStorage
from datetime import datetime

def analyze_assets_discrepancy():
    """Analyze the assets vs liquid assets discrepancy"""
    
    print("🔍 ASSETS DATA INTEGRITY ANALYSIS")
    print("=" * 50)
    
    storage = DataStorage()
    
    # Get all relevant data
    bank_accounts = storage.get_collection('bank_accounts')
    cds = storage.get_collection('cds')
    assets = storage.get_collection('assets')
    loans = storage.get_collection('loans')
    
    print("\n📊 CURRENT DATA BREAKDOWN:")
    print("-" * 30)
    
    # Calculate totals
    total_bank_balance = sum(float(acc.get('balance', 0)) for acc in bank_accounts)
    total_cd_value = sum(float(cd.get('principal', 0)) for cd in cds)
    total_loan_balance = sum(float(loan.get('remaining_balance', 0)) for loan in loans)
    
    total_liquid_assets = total_bank_balance + total_cd_value
    
    print(f"🏦 Bank Accounts: ${total_bank_balance:,.2f}")
    print(f"💿 CDs: ${total_cd_value:,.2f}")
    print(f"💵 Total Liquid Assets: ${total_liquid_assets:,.2f}")
    print()
    
    # Assets table analysis
    print("📋 ASSETS TABLE BREAKDOWN:")
    total_assets_value = 0
    
    if assets:
        for asset in assets:
            name = asset.get('name', 'Unknown')
            value = float(asset.get('value', 0))
            category = asset.get('category', 'Unknown')
            total_assets_value += value
            print(f"  • {name} ({category}): ${value:,.2f}")
    else:
        print("  ❌ No assets found in Assets table!")
    
    print(f"\n📊 Assets Table Total: ${total_assets_value:,.2f}")
    print(f"💵 Liquid Assets Total: ${total_liquid_assets:,.2f}")
    print(f"💰 Loan Balances: ${total_loan_balance:,.2f}")
    
    # Identify the discrepancy
    discrepancy = total_liquid_assets - total_assets_value
    print(f"\n🚨 DISCREPANCY: ${discrepancy:,.2f}")
    
    if discrepancy > 0:
        print("❌ Assets table is MISSING liquid assets!")
        print("\n💡 RECOMMENDED FIXES:")
        
        # Check if CDs are missing from assets
        cd_in_assets = any(asset.get('category') == 'Investments' or 'CD' in asset.get('name', '') for asset in assets)
        if not cd_in_assets and total_cd_value > 0:
            print(f"  1. Add CDs to Assets table (${total_cd_value:,.2f})")
        
        # Check if bank accounts should be in assets
        cash_in_assets = any(asset.get('category') == 'Cash' or 'Cash' in asset.get('name', '') for asset in assets)
        if not cash_in_assets and total_bank_balance > 0:
            print(f"  2. Add Cash/Bank accounts to Assets table (${total_bank_balance:,.2f})")
        
        print(f"  3. Review Assets table for completeness")
        
        return True, discrepancy, total_cd_value, total_bank_balance
    else:
        print("✅ Assets table appears complete")
        return False, 0, 0, 0

def fix_assets_data():
    """Fix the assets data by adding missing liquid assets"""
    
    has_issue, discrepancy, cd_value, bank_value = analyze_assets_discrepancy()
    
    if not has_issue:
        print("\n✅ No fixes needed - Assets data is consistent")
        return
    
    print(f"\n🔧 APPLYING FIXES")
    print("-" * 30)
    
    storage = DataStorage()
    
    # Get current data
    cds = storage.get_collection('cds')
    bank_accounts = storage.get_collection('bank_accounts')
    assets = storage.get_collection('assets')
    
    fixes_applied = []
    
    # Check if we need to add CDs to assets
    cd_in_assets = any('CD' in asset.get('name', '') or asset.get('category') == 'Investments' for asset in assets)
    
    if not cd_in_assets and cd_value > 0:
        print(f"📀 Adding CDs to Assets table...")
        
        # Create a consolidated CD asset entry
        cd_asset = {
            'id': f"cd-consolidated-{datetime.now().strftime('%Y%m%d')}",
            'user_id': 'default-user',
            'name': 'Certificate of Deposits',
            'category': 'Investments',
            'value': cd_value,
            'acquisition_date': datetime.now().strftime('%Y-%m-%d'),
            'notes': 'Consolidated CD holdings from CD management page',
            'created_at': datetime.now().isoformat()
        }
        
        try:
            # Add to storage
            storage.set_item(f"assets/{cd_asset['id']}", cd_asset)
            fixes_applied.append(f"Added CDs to Assets: ${cd_value:,.2f}")
            print(f"  ✅ Added CD asset entry: ${cd_value:,.2f}")
        except Exception as e:
            print(f"  ❌ Failed to add CD asset: {e}")
    
    # Check if we need to add cash to assets  
    cash_in_assets = any('Cash' in asset.get('name', '') or asset.get('category') == 'Cash' for asset in assets)
    
    if not cash_in_assets and bank_value > 0:
        print(f"💰 Adding Cash/Bank accounts to Assets table...")
        
        # Create a consolidated cash asset entry
        cash_asset = {
            'id': f"cash-consolidated-{datetime.now().strftime('%Y%m%d')}",
            'user_id': 'default-user', 
            'name': 'Cash and Bank Accounts',
            'category': 'Cash',
            'value': bank_value,
            'acquisition_date': datetime.now().strftime('%Y-%m-%d'),
            'notes': 'Consolidated cash holdings from bank accounts',
            'created_at': datetime.now().isoformat()
        }
        
        try:
            # Add to storage
            storage.set_item(f"assets/{cash_asset['id']}", cash_asset)
            fixes_applied.append(f"Added Cash to Assets: ${bank_value:,.2f}")
            print(f"  ✅ Added Cash asset entry: ${bank_value:,.2f}")
        except Exception as e:
            print(f"  ❌ Failed to add Cash asset: {e}")
    
    if fixes_applied:
        print(f"\n🎉 FIXES APPLIED:")
        for fix in fixes_applied:
            print(f"  ✅ {fix}")
        
        print(f"\n🔄 Verifying fixes...")
        # Re-run analysis to verify
        analyze_assets_discrepancy()
    else:
        print(f"\n⚠️ No automatic fixes could be applied")
        print(f"   Please manually review and update the Assets table")

def main():
    """Main function"""
    print("🛠️ ASSETS DATA INTEGRITY FIX")
    print("=" * 50)
    print("This script will analyze and fix the Assets vs Liquid Assets discrepancy")
    print()
    
    try:
        fix_assets_data()
        print(f"\n✅ Assets data integrity fix completed!")
        
    except Exception as e:
        print(f"\n❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
