#!/usr/bin/env python3

import os

def list_all_pages():
    """List all pages that actually exist in the app"""
    
    print("📄 ALL EXISTING PAGES")
    print("=" * 40)
    
    pages_dir = 'src/pages'
    
    if not os.path.exists(pages_dir):
        print("❌ src/pages directory not found!")
        return
    
    pages = []
    for file in os.listdir(pages_dir):
        if file.endswith('.tsx'):
            page_name = file.replace('.tsx', '')
            file_path = os.path.join(pages_dir, file)
            file_size = os.path.getsize(file_path)
            
            pages.append({
                'name': page_name,
                'file': file,
                'path': file_path,
                'size': file_size
            })
    
    # Sort by name
    pages.sort(key=lambda x: x['name'])
    
    print(f"Found {len(pages)} pages:")
    print()
    
    # Categorize pages
    core_pages = []
    advanced_pages = []
    utility_pages = []
    
    for page in pages:
        name = page['name'].lower()
        size = page['size']
        
        # Categorize based on name and size
        if any(keyword in name for keyword in ['dashboard', 'loans', 'accounts', 'cds', 'networth']):
            category = "🏠 CORE"
            core_pages.append(page)
        elif any(keyword in name for keyword in ['reports', 'forecast', 'timeline', 'simulator', 'projections']):
            category = "🚀 ADVANCED"
            advanced_pages.append(page)
        else:
            category = "🔧 UTILITY"
            utility_pages.append(page)
        
        status = ""
        if size < 1000:
            status = " ⚠️ (Very small - likely stub)"
        elif size > 50000:
            status = " 📊 (Large - potential performance concern)"
        
        print(f"{category} {page['name']:<25} {size:>8,} bytes{status}")
    
    print(f"\n📊 SUMMARY:")
    print(f"🏠 Core Pages: {len(core_pages)}")
    print(f"🚀 Advanced Pages: {len(advanced_pages)}")
    print(f"🔧 Utility Pages: {len(utility_pages)}")
    
    # Check for potential issues
    stub_pages = [p for p in pages if p['size'] < 1000]
    large_pages = [p for p in pages if p['size'] > 50000]
    
    if stub_pages:
        print(f"\n⚠️  POTENTIAL STUB PAGES ({len(stub_pages)}):")
        for page in stub_pages:
            print(f"   • {page['name']} ({page['size']} bytes)")
    
    if large_pages:
        print(f"\n📊 LARGE PAGES - Performance Review Needed ({len(large_pages)}):")
        for page in large_pages:
            print(f"   • {page['name']} ({page['size']:,} bytes)")

if __name__ == '__main__':
    list_all_pages()
