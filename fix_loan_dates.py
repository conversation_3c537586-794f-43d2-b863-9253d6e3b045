#!/usr/bin/env python3

import sys
import json
sys.path.append('.')
from data_storage import DataStorage
from datetime import datetime, date
from dateutil.relativedelta import relativedelta

def fix_loan_dates():
    """Fix the loan dates based on proper calculation"""
    try:
        storage = DataStorage()
        loans = storage.get_collection('loans')
        
        print('Current Loan Data Issues:')
        print('=' * 50)
        
        # Set a reasonable start date for all loans
        # Assuming all loans started around the same time period
        # We'll calculate backward from current to reasonable dates
        
        current_date = date.today()
        print(f'Current date: {current_date}')
        print()
        
        loan_updates = []
        
        for loan in loans:
            print(f"Loan: {loan['name']}")
            print(f"  Current term: {loan['term_months']} months")
            print(f"  Current next_payment_date: {loan['next_payment_date']}")
            print(f"  Current end_date: {loan['end_date']}")
            
            # Calculate a reasonable start date and next payment
            # If next payment is in the past, calculate how many months have passed
            next_payment = datetime.strptime(loan['next_payment_date'], '%Y-%m-%d').date()
            months_past = (current_date.year - next_payment.year) * 12 + (current_date.month - next_payment.month)
            
            if months_past > 0:
                # Payment is in the past, calculate new next payment
                new_next_payment = current_date.replace(day=1) + relativedelta(months=1)
                # Calculate start date based on how many payments should have been made
                estimated_start = new_next_payment - relativedelta(months=months_past + 1)
            else:
                # Payment is in the future, use current logic
                new_next_payment = next_payment
                estimated_start = new_next_payment - relativedelta(months=1)
            
            # Calculate proper end date
            new_end_date = estimated_start + relativedelta(months=loan['term_months'])
            
            print(f"  Calculated start_date: {estimated_start}")
            print(f"  New next_payment_date: {new_next_payment}")
            print(f"  New end_date: {new_end_date}")
            print()
            
            # Store the updates
            loan_updates.append({
                'loan': loan,
                'updates': {
                    'start_date': estimated_start.strftime('%Y-%m-%d'),
                    'next_payment_date': new_next_payment.strftime('%Y-%m-%d'),
                    'end_date': new_end_date.strftime('%Y-%m-%d')
                }
            })
        
        # Ask for confirmation
        print("Do you want to apply these updates? (y/N): ", end='')
        response = input().strip().lower()
        
        if response == 'y' or response == 'yes':
            print("\nApplying updates...")
            for update_info in loan_updates:
                loan = update_info['loan']
                updates = update_info['updates']
                
                # Update the loan data
                updated_loan = {**loan, **updates}
                
                # Save the updated loan using set_item
                loan_key = f"loans/{loan['id']}"
                loan_json = json.dumps(updated_loan)
                storage.set_item(loan_key, loan_json)
                print(f"✓ Updated {loan['name']}")
            
            print("\nAll loan dates have been updated!")
        else:
            print("No changes applied.")
            
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    fix_loan_dates()
