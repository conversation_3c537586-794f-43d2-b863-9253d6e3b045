#!/usr/bin/env python3

import sys
sys.path.append('.')
from data_storage import DataStorage

def check_loan_dates():
    """Check current loan dates in storage"""
    try:
        storage = DataStorage()
        loans = storage.get_collection('loans')
        
        print('Current Loans Data:')
        print('=' * 50)
        print(f'Found {len(loans)} loans')
        
        for i, loan in enumerate(loans):
            print(f'Loan {i+1}: {loan["name"] if isinstance(loan, dict) and "name" in loan else loan}')
            if isinstance(loan, dict):
                print(f'  ID: {loan.get("id", "N/A")}')
                print(f'  Principal: {loan.get("principal", "N/A")}')
                print(f'  Term: {loan.get("term_months", "N/A")} months')
                print(f'  Start Date: {loan.get("start_date", "N/A")}')
                print(f'  Next Payment: {loan.get("next_payment_date", "N/A")}')
                print(f'  End Date: {loan.get("end_date", "N/A")}')
            else:
                print(f'  Type: {type(loan)}')
                print(f'  Data: {loan}')
            print('-' * 30)
            
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_loan_dates()
