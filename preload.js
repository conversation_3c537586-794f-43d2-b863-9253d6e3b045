// Preload script for Electron
const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose a limited API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  // System functions
  platform: process.platform,
  isElectron: true,
  
  // Data persistence
  saveData: (key, data) => {
    localStorage.setItem(key, JSON.stringify(data));
    return true;
  },
  
  loadData: (key) => {
    const data = localStorage.getItem(key);
    return data ? JSON.parse(data) : null;
  },
  
  // Window controls
  minimizeWindow: () => ipcRenderer.send('window-control', 'minimize'),
  maximizeWindow: () => ipcRenderer.send('window-control', 'maximize'),
  closeWindow: () => ipcRenderer.send('window-control', 'close'),
  
  // App messaging
  sendMessage: (channel, data) => {
    // Only allow specific channels for security
    const validChannels = ['app-event', 'user-action'];
    if (validChannels.includes(channel)) {
      ipcRenderer.send(channel, data);
    }
  },
  
  // Listen for messages from main process
  onMessage: (channel, callback) => {
    const validChannels = ['app-update', 'system-event'];
    if (validChannels.includes(channel)) {
      // Remove any old listeners to avoid memory leaks
      ipcRenderer.removeAllListeners(channel);
      
      // Add new listener
      ipcRenderer.on(channel, (_, ...args) => callback(...args));
      
      // Return a function to remove this listener
      return () => {
        ipcRenderer.removeAllListeners(channel);
      };
    }
  }
});