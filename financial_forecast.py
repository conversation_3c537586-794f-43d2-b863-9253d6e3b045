
def financial_risk_forecast(inputs=None):
    """
    Enhanced financial risk forecast with flexible inputs

    Args:
        inputs (dict): Dictionary containing forecast parameters:
            - monthly_income (float): Monthly income
            - monthly_loans (float): Default monthly loan payment
            - monthly_expenses (float): Monthly expenses
            - current_savings (float): Current savings amount
            - education_schedule (list): List of education payments with month, amount, description
            - loan_schedule (list): List of month-specific loan payments (optional)
            - forecast_months (list): List of months to forecast (default: ['Sep', 'Oct', 'Nov', 'Dec', 'Jan'])

    Returns:
        dict: Forecast results with risk assessment and advice
    """

    # Default inputs if none provided (backward compatibility)
    if inputs is None:
        inputs = {
            'monthly_income': 173000,
            'monthly_loans': 198048.25,  # Updated default
            'monthly_expenses': 30000,
            'current_savings': 612000,
            'education_schedule': [
                {'month': 'Sep', 'amount': 243253, 'description': 'University + School fees'},
                {'month': 'Nov', 'amount': 83724, 'description': 'Mid-semester payments'},
                {'month': 'Dec', 'amount': 105389, 'description': 'End of semester fees'},
                {'month': 'Jan', 'amount': 54890, 'description': 'New semester fees'}
            ],
            'loan_schedule': [
                {'month': 'Dec', 'amount': 126244},  # Reduced payment starting December
                {'month': 'Jan', 'amount': 126244}   # Continues in January
            ],
            'forecast_months': ['Sep', 'Oct', 'Nov', 'Dec', 'Jan']
        }

    # Extract inputs
    monthly_income = inputs.get('monthly_income', 173000)
    monthly_loans = inputs.get('monthly_loans', 198048.25)
    monthly_expenses = inputs.get('monthly_expenses', 30000)
    current_savings = inputs.get('current_savings', 612000)
    education_schedule = inputs.get('education_schedule', [])
    loan_schedule = inputs.get('loan_schedule', [])
    forecast_months = inputs.get('forecast_months', ['Sep', 'Oct', 'Nov', 'Dec', 'Jan'])

    # Convert education schedule to dictionary for easier lookup
    education_map = {}
    for payment in education_schedule:
        education_map[payment['month']] = payment['amount']

    # Convert loan schedule to dictionary for easier lookup
    loan_map = {}
    for payment in loan_schedule:
        loan_map[payment['month']] = payment['amount']

    # Initialize result tracking
    monthly_deficit_log = []
    total_deficit = 0
    education_reserved = 0

    # Calculate total education fees to reserve
    for payment in education_schedule:
        education_reserved += payment['amount']

    remaining_savings = current_savings - education_reserved
    initial_remaining_savings = remaining_savings

    # Forecast for specified months
    for month in forecast_months:
        # Education payment in this month
        edu_payment = education_map.get(month, 0)

        # Loan payment for this month (use month-specific if available, otherwise default)
        loan_payment = loan_map.get(month, monthly_loans)

        # Monthly cash flow = income - (loans + expenses + edu)
        total_outflow = loan_payment + monthly_expenses + edu_payment
        net_balance = monthly_income - total_outflow

        # Track deficit only when negative
        if net_balance < 0:
            total_deficit += abs(net_balance)

        # Update remaining savings: if net_balance is negative, it reduces savings
        remaining_savings += net_balance

        monthly_deficit_log.append({
            'month': month,
            'income': monthly_income,
            'loans': loan_payment,  # Use the month-specific loan payment
            'expenses': monthly_expenses,
            'education_payment': edu_payment,
            'net_cashflow': net_balance,
            'remaining_savings': max(0, remaining_savings)  # Don't show negative savings
        })

    # Assess risk level
    risk_level = "High" if remaining_savings < 0 else "Medium" if remaining_savings < 100000 else "Low"

    # Output analysis
    return {
        "education_reserved": education_reserved,
        "initial_remaining_savings": initial_remaining_savings,
        "monthly_forecast": monthly_deficit_log,
        "final_savings": remaining_savings,
        "risk_level": risk_level,
        "advice": generate_advice(remaining_savings, total_deficit, monthly_income, monthly_loans, monthly_expenses)
    }


def generate_advice(final_savings, total_deficit, monthly_income, monthly_loans, monthly_expenses):
    """
    Generate personalized financial advice based on the forecast results

    Args:
        final_savings (float): Final savings amount after forecast period
        total_deficit (float): Total deficit accumulated over forecast period
        monthly_income (float): Monthly income
        monthly_loans (float): Monthly loan payments
        monthly_expenses (float): Monthly expenses

    Returns:
        list: List of advice strings
    """
    advice = []

    # Calculate current monthly balance (without education payments)
    monthly_balance = monthly_income - monthly_loans - monthly_expenses

    # Risk assessment and primary advice
    if final_savings < 0:
        advice.append("⚠️ CRITICAL: Your savings will be fully depleted. Urgent action needed.")
        advice.append(f"💰 You need to reduce monthly outflow by {abs(monthly_balance):,.0f} EGP to break even.")
    elif final_savings < 100000:
        advice.append("⚠️ WARNING: Your financial buffer is low. Consider reducing expenses or restructuring loans.")
        if monthly_balance < 0:
            advice.append(f"� Current monthly deficit: {abs(monthly_balance):,.0f} EGP")
    else:
        advice.append("✅ Your financial position appears stable for the forecast period.")
        if monthly_balance > 0:
            advice.append(f"📈 Monthly surplus (excluding education): {monthly_balance:,.0f} EGP")

    # Specific actionable advice
    advice.append("�👉 Suggested Actions:")

    # Loan restructuring advice
    if monthly_balance < 0:
        target_loan_payment = monthly_income - monthly_expenses - 20000  # Leave 20k buffer
        reduction_needed = monthly_loans - target_loan_payment
        advice.append(f"- Negotiate with bank to reduce monthly loan payments by {reduction_needed:,.0f} EGP")
        advice.append(f"- Target loan payment: {target_loan_payment:,.0f} EGP (currently {monthly_loans:,.0f} EGP)")

    # Income enhancement advice
    if total_deficit > 0:
        monthly_increase_needed = total_deficit / 5  # Spread over 5 months
        advice.append(f"- Consider increasing monthly income by {monthly_increase_needed:,.0f} EGP")
        advice.append("- Explore part-time or freelance income options")

    # Expense management advice
    if monthly_expenses > 25000:
        advice.append("- Review and optimize monthly expenses")
        advice.append("- Delay non-essential expenses until financial situation improves")

    # Education-specific advice
    advice.append("- Consider consolidating high-interest loans")
    advice.append("- Explore education payment plans or deferrals if available")

    return advice
