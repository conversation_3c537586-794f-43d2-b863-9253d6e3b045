<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EGX Stock Search Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .search-box {
            margin-bottom: 20px;
        }
        input[type="text"] {
            width: 300px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            padding: 10px 20px;
            margin-left: 10px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .results {
            margin-top: 20px;
        }
        .stock-item {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: red;
            background-color: #ffe6e6;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇪🇬 EGX Stock Search Test</h1>
        <p>Test the comprehensive EGX stock search functionality. You can now search for ANY EGX stock, not just the 8 monitored ones!</p>
        
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="Enter stock symbol (e.g., NBE, ETEL, TALAAT)" />
            <button onclick="searchStocks()">Search</button>
            <button onclick="testMultipleSearches()">Test Multiple</button>
        </div>
        
        <div id="status"></div>
        <div id="results" class="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        
        async function searchStocks() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                alert('Please enter a search query');
                return;
            }
            
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            statusDiv.innerHTML = '<div class="loading">🔍 Searching for EGX stocks...</div>';
            resultsDiv.innerHTML = '';
            
            try {
                const response = await fetch(`${API_BASE}/search?q=${encodeURIComponent(query)}`);
                
                if (!response.ok) {
                    throw new Error(`API returned ${response.status}: ${response.statusText}`);
                }
                
                const stocks = await response.json();
                
                statusDiv.innerHTML = `<div class="success">✅ Found ${stocks.length} stocks matching "${query}"</div>`;
                
                if (stocks.length === 0) {
                    resultsDiv.innerHTML = '<div>No stocks found. Try a different search term.</div>';
                    return;
                }
                
                resultsDiv.innerHTML = stocks.map(stock => `
                    <div class="stock-item">
                        <strong>${stock.symbol}</strong> - ${stock.name}
                        <br/>
                        <span style="font-size: 18px; color: #2563eb; font-weight: bold;">
                            ${stock.price ? `${stock.price} EGP` : 'Price N/A'}
                        </span>
                        ${stock.change ? `
                            <span style="color: ${stock.change >= 0 ? '#16a34a' : '#dc2626'}; margin-left: 10px;">
                                ${stock.change >= 0 ? '+' : ''}${stock.change} (${stock.changePercent >= 0 ? '+' : ''}${stock.changePercent}%)
                            </span>
                        ` : ''}
                        <br/>
                        <small style="color: #666;">
                            Volume: ${stock.volume ? stock.volume.toLocaleString() : 'N/A'} | 
                            Source: ${stock.source} | 
                            ${stock.timestamp ? new Date(stock.timestamp).toLocaleTimeString() : stock.note}
                        </small>
                    </div>
                `).join('');
                
            } catch (error) {
                console.error('Search error:', error);
                statusDiv.innerHTML = `<div class="error">❌ Search failed: ${error.message}</div>`;
                resultsDiv.innerHTML = '';
            }
        }
        
        async function testMultipleSearches() {
            const testQueries = ['NBE', 'ETEL', 'TALAAT', 'BANK', 'PHARMA', 'CO'];
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            statusDiv.innerHTML = '<div class="loading">🧪 Running multiple search tests...</div>';
            resultsDiv.innerHTML = '';
            
            let allResults = [];
            
            for (const query of testQueries) {
                try {
                    const response = await fetch(`${API_BASE}/search?q=${encodeURIComponent(query)}`);
                    const stocks = await response.json();
                    
                    allResults.push({
                        query,
                        count: stocks.length,
                        stocks: stocks.slice(0, 3) // Show only first 3 results
                    });
                    
                } catch (error) {
                    allResults.push({
                        query,
                        error: error.message
                    });
                }
            }
            
            statusDiv.innerHTML = '<div class="success">✅ Multiple search tests completed</div>';
            
            resultsDiv.innerHTML = allResults.map(result => `
                <div class="stock-item">
                    <strong>Search: "${result.query}"</strong>
                    ${result.error ? 
                        `<br/><span style="color: red;">Error: ${result.error}</span>` :
                        `<br/>Found ${result.count} stocks` + (result.stocks.length > 0 ? 
                            `<br/><small>${result.stocks.map(s => `${s.symbol} (${s.price || 0} EGP)`).join(', ')}</small>` : '')
                    }
                </div>
            `).join('');
        }
        
        // Allow Enter key to trigger search
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchStocks();
            }
        });
        
        // Test server connection on page load
        window.addEventListener('load', async () => {
            try {
                const response = await fetch(`${API_BASE}/api/stocks`);
                if (response.ok) {
                    console.log('✅ TradingView server is running and accessible');
                } else {
                    console.warn('⚠️ TradingView server responded with error:', response.status);
                }
            } catch (error) {
                console.error('❌ TradingView server connection failed:', error);
                document.getElementById('status').innerHTML = 
                    '<div class="error">❌ TradingView server not accessible. Make sure it\'s running on port 3000.</div>';
            }
        });
    </script>
</body>
</html>
