<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Market Analysis Patterns</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            background: #f8fafc;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .stock-info {
            background: #ecfccb;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 3px solid #65a30d;
        }
        .pattern-info {
            background: #fef3c7;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 3px solid #d97706;
        }
        .code {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Market Analysis Patterns</h1>
        
        <div class="debug-section">
            <h3>Current Issue</h3>
            <p>Technical Patterns showing "No technical patterns detected for selected stocks" even with EMFD and COMI selected.</p>
        </div>

        <div class="debug-section">
            <h3>Stock Data Analysis</h3>
            <div id="stockData">Loading stock data...</div>
        </div>

        <div class="debug-section">
            <h3>Pattern Generation Logic</h3>
            <div id="patternLogic">Analyzing pattern generation...</div>
        </div>

        <div class="debug-section">
            <h3>Technical Indicators Explanation</h3>
            <div class="pattern-info">
                <h4>📊 Current Indicators Used:</h4>
                <ul>
                    <li><strong>Price Change Percentage:</strong> (current_change / previous_price) * 100</li>
                    <li><strong>AI Score:</strong> Composite score from TradingView API (0-10)</li>
                    <li><strong>Volatility:</strong> Absolute value of price change percentage</li>
                    <li><strong>Performance Classification:</strong> Based on change threshold</li>
                </ul>
            </div>
            
            <div class="pattern-info">
                <h4>🎯 Pattern Detection Rules:</h4>
                <ul>
                    <li><strong>Bullish Breakout:</strong> Change > 2% AND AI Score > 7</li>
                    <li><strong>Support Test:</strong> Change < 0 AND |Change| > 3%</li>
                    <li><strong>Bull Flag:</strong> Volatile (|Change| > 5%) AND performing well</li>
                    <li><strong>Bearish Divergence:</strong> Volatile (|Change| > 5%) AND declining</li>
                </ul>
            </div>

            <div class="pattern-info">
                <h4>⚠️ Current Issues:</h4>
                <ul>
                    <li>Thresholds may be too high for EGX market conditions</li>
                    <li>AI Score requirements might be excluding valid patterns</li>
                    <li>Need more diverse pattern types for different market conditions</li>
                </ul>
            </div>
        </div>

        <div class="debug-section">
            <h3>Recommended Improvements</h3>
            <div class="pattern-info">
                <h4>🔧 Enhanced Pattern Detection:</h4>
                <ul>
                    <li><strong>Lower Thresholds:</strong> Reduce change requirements to 1% for bullish, 1.5% for volatile</li>
                    <li><strong>More Pattern Types:</strong> Add RSI-based, Moving Average, Volume patterns</li>
                    <li><strong>Always Generate:</strong> Ensure at least one pattern per selected stock</li>
                    <li><strong>Real Technical Indicators:</strong> Use actual RSI, MACD, Bollinger Bands</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        async function debugStockData() {
            try {
                // Test the API connection
                const response = await fetch('http://localhost:3000/search-all');
                const data = await response.json();
                
                const stockDataDiv = document.getElementById('stockData');
                const patternLogicDiv = document.getElementById('patternLogic');
                
                if (data.stocks && data.stocks.length > 0) {
                    // Find EMFD and COMI specifically
                    const emfd = data.stocks.find(s => s.symbol === 'EMFD');
                    const comi = data.stocks.find(s => s.symbol === 'COMI');
                    
                    let html = '<h4>Selected Stocks Analysis:</h4>';
                    
                    [emfd, comi].forEach(stock => {
                        if (stock) {
                            const changePercent = (stock.change / (stock.price - stock.change)) * 100;
                            const isPerformingWell = changePercent > 2;
                            const isVolatile = Math.abs(changePercent) > 5;
                            
                            html += `
                                <div class="stock-info">
                                    <strong>${stock.symbol} - ${stock.name}</strong><br>
                                    Price: ${stock.price} EGP<br>
                                    Change: ${stock.change} EGP (${changePercent.toFixed(2)}%)<br>
                                    AI Score: ${stock.ai_score}/10<br>
                                    <br>
                                    <strong>Pattern Checks:</strong><br>
                                    • Is Performing Well (>2%): ${isPerformingWell ? '✅ YES' : '❌ NO'}<br>
                                    • AI Score > 7: ${stock.ai_score > 7 ? '✅ YES' : '❌ NO'}<br>
                                    • Is Declining: ${stock.change < 0 ? '✅ YES' : '❌ NO'}<br>
                                    • Is Volatile (>5%): ${isVolatile ? '✅ YES' : '❌ NO'}<br>
                                    <br>
                                    <strong>Would Generate Patterns:</strong><br>
                                    • Bullish Breakout: ${isPerformingWell && stock.ai_score > 7 ? '✅ YES' : '❌ NO'}<br>
                                    • Support Test: ${stock.change < 0 && Math.abs(changePercent) > 3 ? '✅ YES' : '❌ NO'}<br>
                                    • Volatility Pattern: ${isVolatile ? '✅ YES' : '❌ NO'}
                                </div>
                            `;
                        }
                    });
                    
                    stockDataDiv.innerHTML = html;
                    
                    // Pattern logic analysis
                    patternLogicDiv.innerHTML = `
                        <h4>Issue Found:</h4>
                        <p>The pattern generation thresholds are too restrictive for current market conditions. 
                        Many stocks don't meet the strict criteria (2% change + AI score > 7).</p>
                        <p><strong>Solution:</strong> Implement more flexible pattern detection with lower thresholds 
                        and additional pattern types.</p>
                    `;
                    
                } else {
                    stockDataDiv.innerHTML = '<p style="color: red;">❌ Could not fetch stock data. API might be down.</p>';
                }
                
            } catch (error) {
                document.getElementById('stockData').innerHTML = 
                    `<p style="color: red;">❌ Error: ${error.message}</p>`;
            }
        }
        
        // Run the debug analysis
        debugStockData();
    </script>
</body>
</html>
