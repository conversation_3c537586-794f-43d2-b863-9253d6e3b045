<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView API Test - Final Integration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .stock-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .stock-symbol { font-weight: bold; color: #007bff; font-size: 1.2em; }
        .stock-price { font-size: 1.1em; color: #28a745; }
        .stock-change { font-size: 0.9em; }
        .positive { color: #28a745; }
        .negative { color: #dc3545; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        #loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>📊 TradingView API - Final Integration Test</h1>
    
    <div class="container">
        <h2>🔗 Connection Status</h2>
        <div id="connection-status" class="status info">Checking connection...</div>
        <button onclick="testConnection()">Test Connection</button>
        <button onclick="testSearch()">Test Search</button>
        <button onclick="testMultipleSymbols()">Test Multiple Symbols</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div class="container">
        <h2>🔍 Search Test</h2>
        <input type="text" id="searchInput" placeholder="Enter symbol (e.g., COMI, ETEL)" value="COMI">
        <button onclick="searchSymbol()">Search</button>
        <div id="search-results"></div>
    </div>

    <div class="container">
        <h2>📈 Live Stock Data</h2>
        <div id="loading">Loading EGX stocks...</div>
        <div id="stock-data" class="grid"></div>
    </div>

    <div class="container">
        <h2>🔧 Raw API Response</h2>
        <pre id="raw-response">No data yet...</pre>
    </div>

    <div class="container">
        <h2>📋 Test Results Log</h2>
        <div id="test-log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';
        const EGX_SYMBOLS = ['COMI', 'ETEL', 'TALAAT', 'ORWE', 'HRHO', 'EKHO', 'CCAP', 'SWDY'];

        let testResults = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testResults.push({ timestamp, message, type });
            
            const logDiv = document.getElementById('test-log');
            const logElement = document.createElement('div');
            logElement.className = `status ${type}`;
            logElement.textContent = logEntry;
            logDiv.appendChild(logElement);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        async function testConnection() {
            try {
                log('Testing connection to TradingView API server...', 'info');
                const response = await fetch(`${API_BASE}/health`);
                
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('connection-status').className = 'status success';
                    document.getElementById('connection-status').textContent = 
                        `✅ Connected! Server: ${data.status} - ${data.message}`;
                    log('Connection test successful', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                document.getElementById('connection-status').className = 'status error';
                document.getElementById('connection-status').textContent = 
                    `❌ Connection failed: ${error.message}`;
                log(`Connection test failed: ${error.message}`, 'error');
            }
        }

        async function testSearch() {
            try {
                log('Testing search functionality...', 'info');
                const response = await fetch(`${API_BASE}/search?q=COMI`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`Search test successful: Found ${data.length} results`, 'success');
                    document.getElementById('raw-response').textContent = JSON.stringify(data, null, 2);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`Search test failed: ${error.message}`, 'error');
            }
        }

        async function testMultipleSymbols() {
            try {
                log('Testing multiple symbol quotes...', 'info');
                const symbols = ['COMI', 'ETEL', 'TALAAT'];
                const symbolsParam = symbols.join(',');
                const response = await fetch(`${API_BASE}/quotes?symbols=${symbolsParam}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`Multiple symbols test successful: ${data.length} quotes received`, 'success');
                    document.getElementById('raw-response').textContent = JSON.stringify(data, null, 2);
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`Multiple symbols test failed: ${error.message}`, 'error');
            }
        }

        async function searchSymbol() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                log('Please enter a search query', 'warning');
                return;
            }

            try {
                log(`Searching for: ${query}`, 'info');
                const response = await fetch(`${API_BASE}/search?q=${encodeURIComponent(query)}`);
                
                if (response.ok) {
                    const data = await response.json();
                    displaySearchResults(data);
                    log(`Search completed: ${data.length} results for "${query}"`, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`Search failed: ${error.message}`, 'error');
                document.getElementById('search-results').innerHTML = 
                    `<div class="status error">Search failed: ${error.message}</div>`;
            }
        }

        function displaySearchResults(results) {
            const container = document.getElementById('search-results');
            
            if (results.length === 0) {
                container.innerHTML = '<div class="status warning">No results found</div>';
                return;
            }

            container.innerHTML = results.map(stock => `
                <div class="stock-card">
                    <div class="stock-symbol">${stock.symbol}</div>
                    <div class="stock-name">${stock.name || 'Unknown'}</div>
                    <div class="stock-price">Price: $${stock.price || 'N/A'}</div>
                    <div class="stock-change ${(stock.change || 0) >= 0 ? 'positive' : 'negative'}">
                        Change: ${stock.change || 0} (${stock.changePercent || 0}%)
                    </div>
                    <small>Volume: ${stock.volume || 'N/A'}</small>
                </div>
            `).join('');
        }

        async function loadStockData() {
            try {
                log('Loading EGX stock data...', 'info');
                document.getElementById('loading').style.display = 'block';
                
                const promises = EGX_SYMBOLS.map(async symbol => {
                    try {
                        const response = await fetch(`${API_BASE}/quote/${symbol}`);
                        if (response.ok) {
                            const data = await response.json();
                            return { symbol, data, error: null };
                        } else {
                            return { symbol, data: null, error: `HTTP ${response.status}` };
                        }
                    } catch (error) {
                        return { symbol, data: null, error: error.message };
                    }
                });

                const results = await Promise.all(promises);
                displayStockData(results);
                
                const successful = results.filter(r => r.data !== null).length;
                log(`Stock data loaded: ${successful}/${EGX_SYMBOLS.length} symbols successful`, 'success');
                
            } catch (error) {
                log(`Failed to load stock data: ${error.message}`, 'error');
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        function displayStockData(results) {
            const container = document.getElementById('stock-data');
            
            container.innerHTML = results.map(result => {
                if (result.error) {
                    return `
                        <div class="stock-card">
                            <div class="stock-symbol">${result.symbol}</div>
                            <div class="status error">Error: ${result.error}</div>
                        </div>
                    `;
                }

                const stock = result.data;
                const change = stock.change || 0;
                const changePercent = stock.changePercent || 0;
                
                return `
                    <div class="stock-card">
                        <div class="stock-symbol">${stock.symbol || result.symbol}</div>
                        <div class="stock-name">${stock.name || 'Unknown'}</div>
                        <div class="stock-price">$${stock.price || 'N/A'}</div>
                        <div class="stock-change ${change >= 0 ? 'positive' : 'negative'}">
                            ${change >= 0 ? '+' : ''}${change} (${changePercent >= 0 ? '+' : ''}${changePercent}%)
                        </div>
                        <div><small>Volume: ${stock.volume || 'N/A'}</small></div>
                        <div><small>High: $${stock.high || 'N/A'} | Low: $${stock.low || 'N/A'}</small></div>
                    </div>
                `;
            }).join('');
        }

        function clearResults() {
            document.getElementById('search-results').innerHTML = '';
            document.getElementById('stock-data').innerHTML = '';
            document.getElementById('raw-response').textContent = 'No data yet...';
            document.getElementById('test-log').innerHTML = '';
            testResults = [];
            log('Results cleared', 'info');
        }

        // Initialize the page
        window.addEventListener('load', async () => {
            log('Page loaded, initializing tests...', 'info');
            await testConnection();
            await loadStockData();
        });

        // Auto-refresh stock data every 30 seconds
        setInterval(loadStockData, 30000);
    </script>
</body>
</html>
