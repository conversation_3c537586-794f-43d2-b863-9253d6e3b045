<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EGX Market Hours Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { margin: 5px; padding: 8px 16px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>EGX Market Hours & Price Test</h1>
    <p>Testing stock prices during market closure (EGX operates Sun-Thu, 10:00-14:30 Cairo time)</p>
    <p><strong>Current time:</strong> <span id="current-time"></span></p>

    <div id="test-1" class="test-section">
        <h3>Test 1: TradingView-API Connection</h3>
        <button onclick="testAPI()">Test API</button>
        <div id="api-result"></div>
    </div>

    <div id="test-2" class="test-section">
        <h3>Test 2: EGX Stock Prices (Your Holdings)</h3>
        <button onclick="testYourStocks()">Test Your Stocks (ETEL, COMI, SWDY)</button>
        <div id="stocks-result"></div>
    </div>

    <div id="test-3" class="test-section">
        <h3>Test 3: Market Hours Check</h3>
        <button onclick="checkMarketHours()">Check Market Status</button>
        <div id="market-result"></div>
    </div>

    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString();
        }
        updateTime();
        setInterval(updateTime, 1000);

        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            
            try {
                resultDiv.innerHTML = '<p>Testing TradingView-API connection...</p>';
                
                const response = await fetch('http://localhost:3000/monitored-stocks');
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ API Connected</h4>
                            <p>Monitored stocks: ${data.stocks.length}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ API Connection Failed</h4>
                        <p>Error: ${error.message}</p>
                        <p>Make sure TradingView-API server is running on port 3000</p>
                    </div>
                `;
            }
        }

        async function testYourStocks() {
            const resultDiv = document.getElementById('stocks-result');
            const testStocks = ['ETEL', 'COMI', 'SWDY']; // Your portfolio stocks
            
            try {
                resultDiv.innerHTML = '<p>Testing your portfolio stocks...</p>';
                
                const results = [];
                
                for (const symbol of testStocks) {
                    try {
                        const response = await fetch(`http://localhost:3000/search?query=${symbol}`);
                        if (response.ok) {
                            const data = await response.json();
                            if (data.stocks && data.stocks.length > 0) {
                                const stock = data.stocks[0];
                                results.push({
                                    symbol,
                                    price: stock.price,
                                    currency: stock.currency || 'EGP',
                                    found: true,
                                    data: stock
                                });
                            } else {
                                results.push({
                                    symbol,
                                    found: false,
                                    error: 'No data returned'
                                });
                            }
                        } else {
                            results.push({
                                symbol,
                                found: false,
                                error: `HTTP ${response.status}`
                            });
                        }
                    } catch (error) {
                        results.push({
                            symbol,
                            found: false,
                            error: error.message
                        });
                    }
                }
                
                let html = `
                    <div class="warning">
                        <h4>📊 Your Portfolio Stocks Test</h4>
                        <p><strong>Note:</strong> Prices may be 0 due to market closure</p>
                `;
                
                for (const result of results) {
                    const status = result.found ? (result.price > 0 ? 'success' : 'warning') : 'error';
                    html += `
                        <div style="margin: 10px 0; padding: 8px; border-radius: 3px; background: ${
                            status === 'success' ? '#d4edda' : 
                            status === 'warning' ? '#fff3cd' : '#f8d7da'
                        }">
                            <strong>${result.symbol}:</strong> 
                            ${result.found ? 
                                `${result.price} ${result.currency} ${result.price === 0 ? '(Market Closed)' : '(Live)'}` : 
                                `❌ ${result.error}`
                            }
                        </div>
                    `;
                }
                
                html += `
                        <h5>Detailed Data:</h5>
                        <pre>${JSON.stringify(results, null, 2)}</pre>
                    </div>
                `;
                
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Stock Test Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        function checkMarketHours() {
            const resultDiv = document.getElementById('market-result');
            
            try {
                const now = new Date();
                const cairoTime = new Date(now.toLocaleString("en-US", {timeZone: "Africa/Cairo"}));
                const dayOfWeek = cairoTime.getDay(); // 0=Sunday, 1=Monday, etc.
                const hour = cairoTime.getHours();
                const minute = cairoTime.getMinutes();
                
                // EGX operates Sunday (0) to Thursday (4), 10:00-14:30
                const isMarketDay = dayOfWeek >= 0 && dayOfWeek <= 4; // Sunday to Thursday
                const currentTime = hour * 100 + minute; // Convert to HHMM format
                const marketOpen = 1000; // 10:00
                const marketClose = 1430; // 14:30
                const isMarketHours = currentTime >= marketOpen && currentTime <= marketClose;
                
                const isMarketOpen = isMarketDay && isMarketHours;
                
                let statusColor = isMarketOpen ? 'success' : 'warning';
                let statusText = isMarketOpen ? 'OPEN' : 'CLOSED';
                let explanation = '';
                
                if (!isMarketDay) {
                    explanation = 'Market is closed - EGX operates Sunday to Thursday only';
                } else if (!isMarketHours) {
                    explanation = 'Market is closed - EGX trading hours are 10:00 AM to 2:30 PM Cairo time';
                } else {
                    explanation = 'Market is currently open for trading';
                }
                
                const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                
                resultDiv.innerHTML = `
                    <div class="${statusColor}">
                        <h4>${isMarketOpen ? '🟢' : '🔴'} EGX Market Status: ${statusText}</h4>
                        <p><strong>Current Cairo Time:</strong> ${cairoTime.toLocaleString()}</p>
                        <p><strong>Day:</strong> ${days[dayOfWeek]} ${isMarketDay ? '(Trading Day)' : '(Non-Trading Day)'}</p>
                        <p><strong>Time:</strong> ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')} ${isMarketHours ? '(Trading Hours)' : '(After Hours)'}</p>
                        <p><strong>Explanation:</strong> ${explanation}</p>
                        
                        <h5>EGX Trading Schedule:</h5>
                        <ul>
                            <li><strong>Trading Days:</strong> Sunday to Thursday</li>
                            <li><strong>Trading Hours:</strong> 10:00 AM to 2:30 PM (Cairo Time)</li>
                            <li><strong>Closed:</strong> Friday & Saturday (weekends)</li>
                        </ul>
                        
                        <p><em>${isMarketOpen ? 
                            'Prices should update in real-time' : 
                            'Prices will show last closing values or 0 if no data available'
                        }</em></p>
                    </div>
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Market Hours Check Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        // Auto-run tests
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkMarketHours();
                testAPI();
            }, 500);
        });
    </script>
</body>
</html>
