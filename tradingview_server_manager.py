#!/usr/bin/env python3
"""
TradingView Server Manager
Manages the TradingView API server for real-time stock data
"""

import os
import subprocess
import sys
import time
import signal
import requests
import json
from pathlib import Path

class TradingViewServerManager:
    def __init__(self):
        self.server_process = None
        self.server_port = 3000
        self.server_url = f"http://localhost:{self.server_port}"
        self.tradingview_path = None
        self.find_tradingview_path()

    def find_tradingview_path(self):
        """Find the TradingView server directory"""
        current_dir = Path(__file__).parent
        
        # Check for TradingView-API/web-app folder (original location)
        tradingview_dir = current_dir / "TradingView-API" / "web-app"
        if tradingview_dir.exists() and (tradingview_dir / "server.js").exists():
            self.tradingview_path = str(tradingview_dir)
            print(f"📁 Found TradingView server at: {self.tradingview_path}")
            return True
        
        # Check if tradingview_server folder exists in the current directory
        tradingview_dir = current_dir / "tradingview_server"
        if tradingview_dir.exists():
            self.tradingview_path = str(tradingview_dir)
            print(f"📁 Found TradingView server at: {self.tradingview_path}")
            return True
        
        print("❌ TradingView server folder not found")
        return False

    def is_server_running(self):
        """Check if the TradingView server is already running"""
        try:
            response = requests.get(f"{self.server_url}/api/stocks", timeout=5)
            return response.status_code == 200
        except:
            return False

    def start_server(self):
        """Start the TradingView server"""
        if not self.tradingview_path:
            print("❌ Cannot start server: TradingView-API path not found")
            return False

        if self.is_server_running():
            print("✅ TradingView server is already running")
            return True

        try:
            print("🚀 Starting TradingView server...")
            
            # Change to TradingView directory
            server_script = os.path.join(self.tradingview_path, "server.js")
            
            if not os.path.exists(server_script):
                print(f"❌ Server script not found: {server_script}")
                return False

            # Start the server process
            self.server_process = subprocess.Popen(
                ["node", server_script],
                cwd=self.tradingview_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Wait a moment for server to start
            time.sleep(3)

            # Check if server started successfully
            if self.is_server_running():
                print("✅ TradingView server started successfully")
                print(f"🌐 Server running at: {self.server_url}")
                return True
            else:
                print("❌ Failed to start TradingView server")
                self.stop_server()
                return False

        except Exception as e:
            print(f"❌ Error starting TradingView server: {e}")
            return False

    def stop_server(self):
        """Stop the TradingView server"""
        if self.server_process:
            try:
                print("🛑 Stopping TradingView server...")
                self.server_process.terminate()
                
                # Wait for process to terminate
                try:
                    self.server_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    # Force kill if it doesn't terminate gracefully
                    self.server_process.kill()
                    self.server_process.wait()
                
                self.server_process = None
                print("✅ TradingView server stopped")
                return True
            except Exception as e:
                print(f"❌ Error stopping server: {e}")
                return False
        else:
            print("ℹ️ TradingView server is not running")
            return True

    def get_server_status(self):
        """Get the current server status"""
        if self.is_server_running():
            try:
                response = requests.get(f"{self.server_url}/api/stocks", timeout=5)
                stocks = response.json()
                return {
                    "status": "running",
                    "url": self.server_url,
                    "stocks_count": len(stocks),
                    "last_check": time.time()
                }
            except:
                return {
                    "status": "error",
                    "url": self.server_url,
                    "message": "Server responding but API error"
                }
        else:
            return {
                "status": "stopped",
                "url": self.server_url,
                "message": "Server not responding"
            }

    def get_stock_data(self, symbol=None):
        """Get stock data from the TradingView server"""
        if not self.is_server_running():
            return {"error": "TradingView server is not running"}

        try:
            if symbol:
                response = requests.get(f"{self.server_url}/api/stocks/{symbol}", timeout=10)
            else:
                response = requests.get(f"{self.server_url}/api/stocks", timeout=10)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"API returned status {response.status_code}"}
        except Exception as e:
            return {"error": f"Failed to fetch stock data: {e}"}

    def get_technical_analysis(self, symbol):
        """Get technical analysis for a symbol"""
        if not self.is_server_running():
            return {"error": "TradingView server is not running"}

        try:
            response = requests.get(f"{self.server_url}/api/technical-analysis/{symbol}", timeout=15)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Technical analysis API returned status {response.status_code}"}
        except Exception as e:
            return {"error": f"Failed to get technical analysis: {e}"}

    def restart_server(self):
        """Restart the TradingView server"""
        print("🔄 Restarting TradingView server...")
        self.stop_server()
        time.sleep(2)
        return self.start_server()

# Global instance
tv_server_manager = TradingViewServerManager()

def main():
    """Command line interface for the TradingView server manager"""
    if len(sys.argv) < 2:
        print("Usage: python tradingview_server_manager.py [start|stop|status|restart|test]")
        sys.exit(1)

    command = sys.argv[1].lower()

    if command == "start":
        success = tv_server_manager.start_server()
        sys.exit(0 if success else 1)
    
    elif command == "stop":
        success = tv_server_manager.stop_server()
        sys.exit(0 if success else 1)
    
    elif command == "status":
        status = tv_server_manager.get_server_status()
        print(json.dumps(status, indent=2))
    
    elif command == "restart":
        success = tv_server_manager.restart_server()
        sys.exit(0 if success else 1)
    
    elif command == "test":
        print("🧪 Testing TradingView server...")
        status = tv_server_manager.get_server_status()
        print(f"Status: {status}")
        
        if status["status"] == "running":
            print("\n📊 Testing stock data...")
            stocks = tv_server_manager.get_stock_data()
            if "error" not in stocks:
                print(f"✅ Retrieved {len(stocks)} stocks")
                if stocks:
                    sample_stock = stocks[0]
                    print(f"Sample: {sample_stock.get('name', 'Unknown')} - {sample_stock.get('price', 0)} EGP")
            else:
                print(f"❌ Error: {stocks['error']}")
        else:
            print("❌ Server is not running")
    
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)

if __name__ == "__main__":
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        print("\n🛑 Received interrupt signal...")
        tv_server_manager.stop_server()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    main()
