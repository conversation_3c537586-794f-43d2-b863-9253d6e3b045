#!/usr/bin/env python3
"""
Accessibility Audit Script for Financial Advisor App

This script analyzes the React/TypeScript components for accessibility issues
and provides recommendations for improvements.
"""

import os
import re
from pathlib import Path

def analyze_component_accessibility(file_path):
    """Analyze a single component for accessibility issues."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        recommendations = []
        
        # Check for missing alt text on images
        img_tags = re.findall(r'<img[^>]*>', content, re.IGNORECASE)
        for img in img_tags:
            if 'alt=' not in img:
                issues.append("Missing alt attribute on img tag")
        
        # Check for buttons without accessible labels
        button_tags = re.findall(r'<button[^>]*>', content, re.IGNORECASE)
        for button in button_tags:
            if 'aria-label' not in button and 'aria-labelledby' not in button:
                # Check if button has text content
                if '>' in button and '<' in button:
                    continue  # Likely has text content
                issues.append("Button without accessible label")
        
        # Check for form inputs without labels
        input_tags = re.findall(r'<input[^>]*>', content, re.IGNORECASE)
        for inp in input_tags:
            if 'aria-label' not in inp and 'aria-labelledby' not in inp and 'id=' not in inp:
                issues.append("Input without accessible label")
        
        # Check for headings structure
        headings = re.findall(r'<h[1-6][^>]*>', content, re.IGNORECASE)
        if headings:
            heading_levels = []
            for h in headings:
                level = int(h[2])  # Extract number from h1, h2, etc.
                heading_levels.append(level)
            
            # Check for proper heading hierarchy
            for i in range(1, len(heading_levels)):
                if heading_levels[i] > heading_levels[i-1] + 1:
                    issues.append("Heading hierarchy skips levels")
        
        # Check for color-only information
        if 'color:' in content and 'background' not in content:
            recommendations.append("Consider using icons or text in addition to color")
        
        # Check for keyboard navigation
        if 'onKeyDown' not in content and 'tabIndex' not in content:
            if 'onClick' in content:
                recommendations.append("Add keyboard navigation for clickable elements")
        
        # Check for ARIA attributes usage
        aria_attributes = re.findall(r'aria-[a-z]+', content)
        if not aria_attributes:
            recommendations.append("Consider adding ARIA attributes for better screen reader support")
        
        # Check for semantic HTML
        semantic_tags = ['main', 'nav', 'section', 'article', 'aside', 'header', 'footer']
        has_semantic = any(tag in content.lower() for tag in semantic_tags)
        if not has_semantic:
            recommendations.append("Use semantic HTML elements for better structure")
        
        return {
            'file': file_path,
            'issues': issues,
            'recommendations': recommendations,
            'aria_usage': len(aria_attributes),
            'has_semantic_html': has_semantic
        }
    
    except Exception as e:
        return {
            'file': file_path,
            'error': str(e),
            'issues': [],
            'recommendations': []
        }

def main():
    """Main function to run accessibility audit."""
    print("🔍 Financial Advisor App - Accessibility Audit")
    print("=" * 50)
    
    # Directories to analyze
    src_dir = Path("d:/Financial Advisor/src")
    
    # Find all React/TypeScript component files
    component_files = []
    for pattern in ["**/*.tsx", "**/*.ts"]:
        component_files.extend(src_dir.glob(pattern))
    
    # Filter out non-component files
    component_files = [f for f in component_files if not str(f).endswith('.d.ts')]
    
    print(f"📁 Found {len(component_files)} files to analyze")
    print()
    
    all_results = []
    total_issues = 0
    total_recommendations = 0
    
    for file_path in component_files:
        result = analyze_component_accessibility(file_path)
        all_results.append(result)
        
        if 'error' not in result:
            total_issues += len(result['issues'])
            total_recommendations += len(result['recommendations'])
    
    # Print results
    print("🚨 ACCESSIBILITY ISSUES FOUND:")
    print("-" * 30)
    
    for result in all_results:
        if 'error' in result:
            print(f"❌ Error analyzing {result['file']}: {result['error']}")
            continue
        
        if result['issues'] or result['recommendations']:
            rel_path = str(result['file']).replace('d:/Financial Advisor/', '')
            print(f"\n📄 {rel_path}")
            
            if result['issues']:
                print("  🔴 Issues:")
                for issue in result['issues']:
                    print(f"    • {issue}")
            
            if result['recommendations']:
                print("  🟡 Recommendations:")
                for rec in result['recommendations']:
                    print(f"    • {rec}")
            
            print(f"  📊 ARIA attributes: {result['aria_usage']}")
            print(f"  🏗️  Semantic HTML: {'✅' if result['has_semantic_html'] else '❌'}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 ACCESSIBILITY AUDIT SUMMARY")
    print("=" * 50)
    print(f"📁 Files analyzed: {len(component_files)}")
    print(f"🚨 Total issues: {total_issues}")
    print(f"💡 Total recommendations: {total_recommendations}")
    
    # Priority improvements
    print("\n🎯 PRIORITY IMPROVEMENTS TO IMPLEMENT:")
    print("-" * 40)
    print("1. Add ARIA labels and descriptions")
    print("2. Implement keyboard navigation")
    print("3. Ensure proper heading hierarchy")
    print("4. Add focus management")
    print("5. Improve color contrast")
    print("6. Add screen reader announcements")
    print("7. Implement skip links")
    print("8. Add loading/error state announcements")
    
    return all_results

if __name__ == "__main__":
    main()
