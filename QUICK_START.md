# 🚀 Financial Advisor - Quick Start Guide

## New Unified Startup System

We've simplified the startup process! Instead of 12+ different scripts, now you have **one simple command**:

### 🎯 **Primary Commands**

```bash
# Desktop App (Recommended - most stable)
python start.py desktop

# Web Development Server  
python start.py dev

# Electron Desktop App
python start.py electron

# Python CLI Version
python start.py cli

# Build for Production
python start.py build
```

### 📦 **NPM Scripts** (Alternative)

```bash
npm run start          # Same as: python start.py desktop
npm run start:web      # Same as: python start.py dev  
npm run start:electron # Same as: python start.py electron
npm run start:cli      # Same as: python start.py cli
```

### 🔧 **Requirements Check**

The startup script automatically checks for:
- ✅ Node.js and npm installation
- ✅ Python dependencies (PyWebView)
- ✅ Required files (package.json, etc.)

### 🆘 **Troubleshooting**

If you encounter issues:

1. **Run requirements check:**
   ```bash
   python start.py desktop  # Will show any missing requirements
   ```

2. **Skip requirements check** (if needed):
   ```bash
   python start.py desktop --skip-check
   ```

3. **Get help:**
   ```bash
   python start.py --help
   ```

### 🗂️ **Legacy Scripts**

The old `.bat` files are still there for backward compatibility, but we recommend using the new unified system. They will be moved to `legacy-scripts/` folder in a future update.

### 💡 **What Changed?**

- ✅ **Single entry point** instead of 12+ scripts
- ✅ **Better error handling** and requirements checking  
- ✅ **Consistent experience** across all modes
- ✅ **Easy to remember** commands
- ✅ **Automatic dependency detection**

---

**🎉 That's it! Just run `python start.py desktop` and you're ready to go!**
