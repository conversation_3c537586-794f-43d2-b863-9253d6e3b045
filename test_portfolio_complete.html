<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Integration Test - Complete</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { margin: 5px; padding: 8px 16px; cursor: pointer; }
        .stock-item { margin: 5px 0; padding: 8px; background: #f0f0f0; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Portfolio Integration Test - Complete Verification</h1>
    <p>This test verifies that the simplified Portfolio Overview page is working correctly with live EGX data.</p>

    <div id="test-1" class="test-section info">
        <h3>Test 1: TradingView-API Server Connection</h3>
        <button onclick="testAPIConnection()">Test API Connection</button>
        <div id="api-result"></div>
    </div>

    <div id="test-2" class="test-section info">
        <h3>Test 2: Stock Search & Price Lookup</h3>
        <button onclick="testStockSearch()">Test Stock Search</button>
        <div id="search-result"></div>
    </div>

    <div id="test-3" class="test-section info">
        <h3>Test 3: Live Portfolio Calculation</h3>
        <button onclick="testPortfolioCalculation()">Test Portfolio Calculation</button>
        <div id="portfolio-result"></div>
    </div>

    <div id="test-4" class="test-section info">
        <h3>Test 4: Storage Bridge Compatibility</h3>
        <button onclick="testStorageBridge()">Test Storage Bridge</button>
        <div id="storage-result"></div>
    </div>

    <div id="test-5" class="test-section info">
        <h3>Test 5: Demo Portfolio Creation</h3>
        <button onclick="testDemoPortfolio()">Create Demo Portfolio</button>
        <div id="demo-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000';

        async function testAPIConnection() {
            const resultDiv = document.getElementById('api-result');
            try {
                resultDiv.innerHTML = '<p>Testing connection...</p>';
                const response = await fetch(`${API_BASE}/monitored-stocks`);
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ API Connection Successful</h4>
                            <p>Monitored stocks: ${data.stocks.length}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                    document.getElementById('test-1').className = 'test-section success';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ API Connection Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                document.getElementById('test-1').className = 'test-section error';
            }
        }

        async function testStockSearch() {
            const resultDiv = document.getElementById('search-result');
            const testStocks = ['COMI', 'VALU', 'HRHO', 'CIB'];
            
            try {
                resultDiv.innerHTML = '<p>Testing stock search and prices...</p>';
                const results = [];
                
                for (const symbol of testStocks) {
                    try {
                        const response = await fetch(`${API_BASE}/search?query=${symbol}`);
                        if (response.ok) {
                            const data = await response.json();
                            results.push({
                                symbol,
                                found: data.stocks && data.stocks.length > 0,
                                data: data.stocks ? data.stocks[0] : null
                            });
                        }
                    } catch (error) {
                        results.push({
                            symbol,
                            found: false,
                            error: error.message
                        });
                    }
                }

                const successCount = results.filter(r => r.found).length;
                const html = `
                    <div class="${successCount === testStocks.length ? 'success' : 'error'}">
                        <h4>${successCount === testStocks.length ? '✅' : '⚠️'} Stock Search Results (${successCount}/${testStocks.length})</h4>
                        ${results.map(r => `
                            <div class="stock-item">
                                <strong>${r.symbol}:</strong> 
                                ${r.found ? 
                                    `✅ Found - Price: ${r.data.price} ${r.data.currency || 'EGP'}` : 
                                    `❌ Not found${r.error ? ` (${r.error})` : ''}`
                                }
                            </div>
                        `).join('')}
                    </div>
                `;
                resultDiv.innerHTML = html;
                document.getElementById('test-2').className = `test-section ${successCount === testStocks.length ? 'success' : 'error'}`;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Stock Search Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                document.getElementById('test-2').className = 'test-section error';
            }
        }

        async function testPortfolioCalculation() {
            const resultDiv = document.getElementById('portfolio-result');
            
            try {
                resultDiv.innerHTML = '<p>Testing portfolio calculations...</p>';
                
                // Simulate a test portfolio
                const testPortfolio = {
                    holdings: [
                        { symbol: 'COMI', quantity: 100, avgBuyPrice: 80 },
                        { symbol: 'VALU', quantity: 200, avgBuyPrice: 9 },
                        { symbol: 'CIB', quantity: 50, avgBuyPrice: 85 }
                    ]
                };

                // Get current prices
                const holdingsWithPrices = [];
                let totalValue = 0;
                let totalCost = 0;

                for (const holding of testPortfolio.holdings) {
                    try {
                        const response = await fetch(`${API_BASE}/search?query=${holding.symbol}`);
                        if (response.ok) {
                            const data = await response.json();
                            const currentPrice = data.stocks && data.stocks[0] ? data.stocks[0].price : 0;
                            const currentValue = holding.quantity * currentPrice;
                            const cost = holding.quantity * holding.avgBuyPrice;
                            const pnl = currentValue - cost;
                            
                            holdingsWithPrices.push({
                                ...holding,
                                currentPrice,
                                currentValue,
                                cost,
                                pnl,
                                pnlPercent: cost > 0 ? (pnl / cost * 100) : 0
                            });
                            
                            totalValue += currentValue;
                            totalCost += cost;
                        }
                    } catch (error) {
                        console.error(`Error fetching ${holding.symbol}:`, error);
                    }
                }

                const totalPnL = totalValue - totalCost;
                const totalPnLPercent = totalCost > 0 ? (totalPnL / totalCost * 100) : 0;

                const html = `
                    <div class="success">
                        <h4>✅ Portfolio Calculation Test</h4>
                        <p><strong>Total Portfolio Value:</strong> ${totalValue.toFixed(2)} EGP</p>
                        <p><strong>Total Cost:</strong> ${totalCost.toFixed(2)} EGP</p>
                        <p><strong>Total P&L:</strong> <span style="color: ${totalPnL >= 0 ? 'green' : 'red'}">${totalPnL.toFixed(2)} EGP (${totalPnLPercent.toFixed(2)}%)</span></p>
                        <h5>Holdings:</h5>
                        ${holdingsWithPrices.map(h => `
                            <div class="stock-item">
                                <strong>${h.symbol}</strong>: ${h.quantity} shares @ ${h.currentPrice} EGP = ${h.currentValue.toFixed(2)} EGP
                                <br>P&L: <span style="color: ${h.pnl >= 0 ? 'green' : 'red'}">${h.pnl.toFixed(2)} EGP (${h.pnlPercent.toFixed(2)}%)</span>
                            </div>
                        `).join('')}
                    </div>
                `;
                resultDiv.innerHTML = html;
                document.getElementById('test-3').className = 'test-section success';
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Portfolio Calculation Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                document.getElementById('test-3').className = 'test-section error';
            }
        }

        function testStorageBridge() {
            const resultDiv = document.getElementById('storage-result');
            
            try {
                // Test localStorage functionality
                const testKey = 'portfolio_test_' + Date.now();
                const testData = { test: 'data', timestamp: Date.now() };
                
                localStorage.setItem(testKey, JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                localStorage.removeItem(testKey);
                
                const isValid = retrieved && retrieved.test === 'data';
                
                if (isValid) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Storage Bridge Test Passed</h4>
                            <p>localStorage is working correctly for browser mode</p>
                            <p>Note: Desktop app integration requires Python backend</p>
                        </div>
                    `;
                    document.getElementById('test-4').className = 'test-section success';
                } else {
                    throw new Error('localStorage test failed');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Storage Bridge Test Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                document.getElementById('test-4').className = 'test-section error';
            }
        }

        function testDemoPortfolio() {
            const resultDiv = document.getElementById('demo-result');
            
            try {
                // Simulate creating a demo portfolio
                const demoPortfolio = {
                    id: 'demo_' + Date.now(),
                    name: 'Demo EGX Portfolio',
                    description: 'Sample portfolio with popular EGX stocks',
                    holdings: [
                        { symbol: 'COMI', name: 'Commercial International Bank', quantity: 100, avgBuyPrice: 80 },
                        { symbol: 'VALU', name: 'Talaat Moustafa Group Holding', quantity: 200, avgBuyPrice: 9 },
                        { symbol: 'HRHO', name: 'Hassan Allam Holding', quantity: 50, avgBuyPrice: 25 }
                    ],
                    createdAt: new Date().toISOString()
                };

                // Store in localStorage
                const portfoliosKey = 'real_portfolios';
                const existingPortfolios = JSON.parse(localStorage.getItem(portfoliosKey) || '[]');
                existingPortfolios.push(demoPortfolio);
                localStorage.setItem(portfoliosKey, JSON.stringify(existingPortfolios));

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ Demo Portfolio Created</h4>
                        <p><strong>Portfolio:</strong> ${demoPortfolio.name}</p>
                        <p><strong>Holdings:</strong> ${demoPortfolio.holdings.length} stocks</p>
                        <p><strong>Stored in:</strong> localStorage (browser) / Python backend (desktop)</p>
                        <h5>Holdings:</h5>
                        ${demoPortfolio.holdings.map(h => `
                            <div class="stock-item">
                                <strong>${h.symbol}</strong> (${h.name}): ${h.quantity} shares @ ${h.avgBuyPrice} EGP
                            </div>
                        `).join('')}
                        <p><em>Portfolio saved to localStorage. Visit the Portfolio Overview page to see it in action!</em></p>
                    </div>
                `;
                document.getElementById('test-5').className = 'test-section success';
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Demo Portfolio Creation Failed</h4>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
                document.getElementById('test-5').className = 'test-section error';
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('Running automated tests...');
                testAPIConnection();
                setTimeout(() => testStockSearch(), 1000);
                setTimeout(() => testStorageBridge(), 2000);
            }, 500);
        });
    </script>
</body>
</html>
