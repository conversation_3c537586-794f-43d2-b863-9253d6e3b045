#!/usr/bin/env python3

"""
Investigate Missing Loans Issue
==============================
Check what happened to the other 3 loans after the update.
"""

import sys
import os
sys.path.append('.')
from data_storage import DataStorage
import json

def investigate_loans():
    """Check the current state of loans data"""
    
    print("🔍 INVESTIGATING LOANS DATA")
    print("=" * 50)
    
    storage = DataStorage()
    
    # Check what's in the loans collection
    print("📋 Current Loans Collection:")
    loans = storage.get_collection('loans')
    print(f"Found {len(loans)} loans in collection")
    
    for i, loan in enumerate(loans):
        print(f"  {i+1}. {loan.get('name', 'Unknown')} - ID: {loan.get('id', 'No ID')}")
    
    # Check the data directory structure
    print(f"\n📁 Data Directory Structure:")
    data_dir = storage.data_dir
    loans_dir = os.path.join(data_dir, 'loans')
    
    if os.path.exists(loans_dir):
        loan_files = [f for f in os.listdir(loans_dir) if f.endswith('.json')]
        print(f"Found {len(loan_files)} loan files in directory:")
        
        for file in loan_files:
            print(f"  • {file}")
            
            # Try to read the file directly
            file_path = os.path.join(loans_dir, file)
            try:
                with open(file_path, 'r') as f:
                    content = f.read()
                    if content:
                        try:
                            loan_data = json.loads(content)
                            name = loan_data.get('name', 'Unknown')
                            next_payment = loan_data.get('next_payment_date', 'N/A')
                            print(f"    - Name: {name}, Next Payment: {next_payment}")
                        except:
                            print(f"    - Invalid JSON content")
                    else:
                        print(f"    - Empty file")
            except Exception as e:
                print(f"    - Error reading file: {e}")
    else:
        print("❌ Loans directory does not exist!")
    
    # Check the index
    print(f"\n📇 Loans Index:")
    try:
        # Access the index directly
        loans_index = getattr(storage, 'indices', {}).get('loans', {})
        print(f"Index contains {len(loans_index)} entries:")
        for loan_id, info in loans_index.items():
            print(f"  • {loan_id}: {info}")
    except Exception as e:
        print(f"❌ Error accessing index: {e}")

def fix_missing_loans():
    """Try to recover missing loans from backup or rebuild"""
    
    print(f"\n🔧 ATTEMPTING TO RECOVER MISSING LOANS")
    print("=" * 50)
    
    storage = DataStorage()
    
    # Check if we have backups
    backup_dir = os.path.join(storage.data_dir, 'backups')
    if os.path.exists(backup_dir):
        backups = [f for f in os.listdir(backup_dir) if f.startswith('backup_')]
        backups.sort(reverse=True)  # Latest first
        
        print(f"Found {len(backups)} backup files")
        
        if backups:
            # Try the most recent backup
            latest_backup = backups[0]
            backup_path = os.path.join(backup_dir, latest_backup)
            
            print(f"Checking latest backup: {latest_backup}")
            
            # Backups are typically tar.gz files, but let's see what we have
            print(f"Backup file size: {os.path.getsize(backup_path)} bytes")
            
            # For now, let's manually recreate the missing loans with correct data
            print(f"\n📝 Manually recreating missing loans with corrected data")
            
            # Expected loan data based on our earlier analysis
            missing_loans = [
                {
                    'id': '2a2e2524-5fa9-4b00-9a28-aa3f9d22fd3c',
                    'name': 'ENBD',
                    'principal': 2000000,
                    'term_months': 36,
                    'start_date': '2022-12-01',
                    'next_payment_date': '2025-07-01',
                    'end_date': '2025-12-01',
                    'interest_rate': 17.5,
                    'remaining_balance': 474546.41,
                    'monthly_payment': 71804.13,
                    'user_id': 'default-user',
                    'created_at': '2022-12-01T00:00:00'
                },
                {
                    'id': '4c9946a7-cf86-4557-afe3-187726168725',
                    'name': 'ENBD3',
                    'principal': 3000000,
                    'term_months': 44,
                    'start_date': '2023-02-01',
                    'next_payment_date': '2025-07-01',
                    'end_date': '2026-10-01',
                    'interest_rate': 24.25,
                    'remaining_balance': 1477661.88,
                    'monthly_payment': 103571.90,
                    'user_id': 'default-user',
                    'created_at': '2023-02-01T00:00:00'
                },
                {
                    'id': '9f4507d0-f0f6-45ef-9cd4-0729735ea8fc',
                    'name': 'ENBD2',
                    'principal': 800000,
                    'term_months': 48,
                    'start_date': '2022-10-01',
                    'next_payment_date': '2025-07-01',
                    'end_date': '2026-10-01',
                    'interest_rate': 16,
                    'remaining_balance': 142304.24,
                    'monthly_payment': 22672.22,
                    'user_id': 'default-user',
                    'created_at': '2022-10-01T00:00:00'
                }
            ]
            
            for loan in missing_loans:
                try:
                    # Save as JSON string
                    loan_json = json.dumps(loan)
                    storage.set_item(f"loans/{loan['id']}", loan_json)
                    print(f"  ✅ Recreated {loan['name']}")
                except Exception as e:
                    print(f"  ❌ Failed to recreate {loan['name']}: {e}")
            
            print(f"\n🔄 Verifying recovery...")
            loans_after = storage.get_collection('loans')
            print(f"Now have {len(loans_after)} loans")
            
            for loan in loans_after:
                name = loan.get('name', 'Unknown')
                next_payment = loan.get('next_payment_date', 'N/A')
                print(f"  • {name}: Next payment {next_payment}")

def main():
    """Main function"""
    investigate_loans()
    fix_missing_loans()

if __name__ == '__main__':
    main()
