import os
import webview
from data_storage import DataStorage

# Constants
APP_NAME = "PyWebView Test"
WINDOW_WIDTH = 1000
WINDOW_HEIGHT = 700
DEBUG_MODE = True

def on_loaded(window):
    """Called when the webpage has loaded"""
    print("WebView loaded - page is ready")
    
    # Inject some JavaScript to help with debugging
    try:
        window.evaluate_js("""
            console.log('Test script injected by Python');
            
            // Check if pywebview is available
            if (window.pywebview) {
                console.log('PyWebView is available after injection');
                console.log('API methods:', Object.keys(window.pywebview.api));
            } else {
                console.log('PyWebView is NOT available after injection');
                
                // Try to create the pywebview object if it doesn't exist
                window.pywebview = { api: {} };
                console.log('Created pywebview object manually');
            }
        """)
        print("Debug JavaScript injected")
    except Exception as e:
        print(f"Error injecting JavaScript: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Launch the test application"""
    print(f"Starting {APP_NAME}...")
    print(f"Storage location: {os.path.join(os.path.expanduser('~'), '.financial_advisor')}")
    
    try:
        # Initialize the data storage
        storage = DataStorage()
        
        # Get the path to the test HTML file
        test_html_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'simple-test.html')
        
        if not os.path.exists(test_html_path):
            print(f"Error: Test HTML file not found at {test_html_path}")
            return
        
        # Create the URL for the local file
        url = f"file://{test_html_path}"
        print(f"Loading test page from: {url}")
        
        # Create the window with storage API exposed
        window = webview.create_window(
            title=APP_NAME,
            url=url,
            width=WINDOW_WIDTH,
            height=WINDOW_HEIGHT,
            min_size=(800, 600),
            js_api=storage,
            text_select=True
        )
        
        # Register the loaded event handler
        window.events.loaded += on_loaded
        
        # Start the window
        webview.start(debug=DEBUG_MODE)
        
    except Exception as e:
        print(f"Error in test app: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
